package com.mc.tool.caesar.vpm.pages.operation.rearrange;

import impl.org.controlsfx.skin.ListSelectionViewSkinEx;
import java.util.ArrayList;
import java.util.List;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class RearrangeListSelectionViewSkin<T> extends ListSelectionViewSkinEx<T> {

  private int targetMaxSize;
  private BooleanProperty isEqual = new SimpleBooleanProperty(false);

  /** . */
  public RearrangeListSelectionViewSkin(ListSelectionViewEx<T> view, int maxSize) {
    super(view);
    this.moveToTarget.disableProperty().bind(isEqual);
    this.moveToTargetAll.disableProperty().bind(isEqual);
    this.targetMaxSize = maxSize;
  }

  @Override
  protected void moveToTarget() {
    List<T> selectedItems =
        new ArrayList<>(getSourceListView().getSelectionModel().getSelectedItems());
    if (getTargetListView().getItems().size() + selectedItems.size() <= targetMaxSize) {
      if (getTargetListView().getItems().size() + selectedItems.size() == targetMaxSize) {
        isEqual.set(true);
      }
      move(getSourceListView(), getTargetListView(), selectedItems);
    } else if (getTargetListView().getItems().size() < targetMaxSize) {
      List<T> items =
          selectedItems.subList(0, targetMaxSize - getTargetListView().getItems().size());
      move(getSourceListView(), getTargetListView(), items);
      isEqual.set(true);
    }
    getSourceListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToTargetAll() {
    List<T> allItems = new ArrayList<>(getSourceListView().getItems());
    if (getTargetListView().getItems().size() + allItems.size() <= targetMaxSize) {
      if (getTargetListView().getItems().size() + allItems.size() == targetMaxSize) {
        isEqual.set(true);
      }
      move(getSourceListView(), getTargetListView(), allItems);
    } else if (getTargetListView().getItems().size() < targetMaxSize) {
      List<T> items = allItems.subList(0, targetMaxSize - getTargetListView().getItems().size());
      move(getSourceListView(), getTargetListView(), items);
      isEqual.set(true);
    }
    getSourceListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToSource() {
    move(getTargetListView(), getSourceListView());
    isEqual.set(false);
    getTargetListView().getSelectionModel().clearSelection();
  }

  @Override
  protected void moveToSourceAll() {
    move(getTargetListView(), getSourceListView(), new ArrayList<>(getTargetListView().getItems()));
    isEqual.set(false);
    getTargetListView().getSelectionModel().clearSelection();
  }

  public void setTargetMaxSize(int size) {
    targetMaxSize = size;
  }

  public int getTargetMaxSize() {
    return targetMaxSize;
  }

  public void setIsEqual(boolean bool) {
    isEqual.set(bool);
  }
}
