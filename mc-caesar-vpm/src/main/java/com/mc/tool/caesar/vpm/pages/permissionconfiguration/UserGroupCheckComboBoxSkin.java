package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import impl.org.controlsfx.skin.CheckComboBoxSkinEx;
import javafx.beans.binding.Bindings;
import javafx.collections.ListChangeListener;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.cell.CheckBoxListCell;
import javafx.util.Callback;
import org.controlsfx.control.CheckComboBox;

/**
 * .
 */
public class UserGroupCheckComboBoxSkin extends CheckComboBoxSkinEx<UserGroupData> {
  private WeakAdapter weakAdapter = new WeakAdapter();

  /** Constructor. */
  public UserGroupCheckComboBoxSkin(CheckComboBox<UserGroupData> control) {
    super(control);

    comboBox.setCellFactory(new ComboBoxCallback());
  }

  private class ComboBoxCallback
      implements Callback<ListView<UserGroupData>, ListCell<UserGroupData>> {

    @Override
    public ListCell<UserGroupData> call(ListView<UserGroupData> param) {
      CheckBoxListCell<UserGroupData> result =
          new CheckBoxListCell<>(control::getItemBooleanProperty);

      control
          .getCheckModel()
          .getCheckedItems()
          .addListener(
              weakAdapter.wrap(
                  (ListChangeListener<UserGroupData>)
                      change -> {
                        if (change.next() && result.getItem() != null) {
                          result.disableProperty().unbind();
                          result
                              .disableProperty()
                              .bind(
                                  Bindings.size(control.getCheckModel().getCheckedItems())
                                      .greaterThan(4)
                                      .and(control.getItemBooleanProperty(result.getItem()).not()));
                        }
                      }));
      result.converterProperty().bind(control.converterProperty());
      return result;
    }
  }
}
