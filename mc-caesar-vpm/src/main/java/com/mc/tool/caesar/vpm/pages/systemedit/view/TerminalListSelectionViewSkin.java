package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import impl.org.controlsfx.skin.ListSelectionViewSkinEx;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class TerminalListSelectionViewSkin extends ListSelectionViewSkinEx<VisualEditNode> {

  /** Construct. */
  public TerminalListSelectionViewSkin(ListSelectionViewEx<VisualEditNode> view) {
    super(view);
    this.gridPane.setStyle("-fx-vgap: 0px;");
    this.sourceListView.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
    this.targetListView.setStyle("-fx-border-width:1px;-fx-border-color:#e5e5e5;");
  }
}
