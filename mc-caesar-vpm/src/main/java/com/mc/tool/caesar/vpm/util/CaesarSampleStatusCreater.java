package com.mc.tool.caesar.vpm.util;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Extender.ExtenderInterfaceType;
import com.mc.tool.caesar.api.CaesarConstants.Module.Type;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.Version5x;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.VersionDef;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.SampleModelCreater;
import com.mc.tool.caesar.api.utils.SampleModelCreater.SampleModelInfo;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.ConfigurationMerger;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarDataUtility;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarData2VisualDataModel;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.ScreenBinding;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.ScreenConfig;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.VideoConfig;
import com.mc.tool.caesar.vpm.util.VideoWallGenerator.VideoWallConfig;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.ViewLogger;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;

/**
 * .
 */
public class CaesarSampleStatusCreater {

  /**
   * .
   *
   * @param args args
   */
  public static void main(String[] args) throws IOException {
    System.setProperty("no-fx-mode", "1");
    System.setProperty("generator-mode", "true");
    System.setProperty("latest-dkm-version", String.valueOf(Version5x.VERSION_VALUE));
    String dir = "./";
    //    String dir = "d://VPM-STATUS//";
    Files.createDirectories(Paths.get(dir));
    create384CrossScreenFullTestStatus(dir);
    create36WithFullRx(dir);
    createSampleForMatrixGridFull(dir);
    createSampleForTest(dir);
    create816FullStatus(dir);
    createSampleForVp6Status(dir);
    createSampleForVp7Status(dir);
    createSampleForMicIn(dir);
    createSampleForMatrixGridWithoutGridLine(dir);
    createSampleForEdidTest(dir);
    createSampleForRightsTest(dir);
    createSampleForDhdmi(dir);
    create384MultiviewTestStatus(dir);
  }

  /**
   * 创建级联但没有级联线的状态包.
   *
   * @param dir 目录.
   */
  public static void createSampleForMatrixGridWithoutGridLine(String dir) {
    SampleMatrixInfo sampleMatrixInfo = new SampleMatrixInfo();
    for (int i = 0; i < 2; i++) {
      SampleModelInfo info = createSimpleSingleStatusInfo();
      info.setTxCount(2);
      info.setRxCount(2);
      info.setDeviceName("matrix" + i);
      sampleMatrixInfo.getStatusInfoList().add(info);
    }
    createSampleStatus(sampleMatrixInfo, dir + "36_hw1.2_grid_with_2_without_gridline.status");
  }

  /**
   * 创建级联状态包.
   *
   * @param dir 目录.
   */
  public static void createSampleForMatrixGridWithGridLine(String dir) {
    SampleMatrixInfo sampleMatrixInfo = new SampleMatrixInfo();
    createGridMatricesInfo(sampleMatrixInfo, 2);
    int startPort = 5;
    for (int i = 0; i < 2; i++) {
      sampleMatrixInfo.getGridLines().put(startPort + i, startPort + 36 + i);
    }
    createSampleStatus(sampleMatrixInfo, dir + "36_hw1.2_grid_with_2_with_gridline.status");
  }

  /**
   * 生成完整的级联demo状态包.
   *
   * @param dir 保存路径.
   */
  public static void createSampleForMatrixGridFull(String dir) {
    SampleMatrixInfo sampleMatrixInfo = new SampleMatrixInfo();
    for (int i = 0; i < 2; i++) {
      SampleModelInfo info = createSimpleSingleStatusInfo();
      info.setTxCount(2);
      info.setRxCount(2);
      info.setVp6Count(1);
      info.setDeviceName("matrix" + i);
      sampleMatrixInfo.getStatusInfoList().add(info);
    }
    createSampleStatus(sampleMatrixInfo, dir + "36_hw1.2_grid_full.status");
  }

  protected static void createGridMatricesInfo(SampleMatrixInfo sampleMatrixInfo, int count) {
    for (int i = 0; i < count; i++) {
      SampleModelInfo info = createSimpleSingleStatusInfo();
      info.setTxCount(2);
      info.setRxCount(2);
      info.setDeviceName("matrix" + i);

      sampleMatrixInfo.getStatusInfoList().add(info);
    }
  }

  /**
   * 创建有micin的状态包.
   *
   * @param dir 目录
   */
  public static void createSampleForMicIn(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setTxCount(2);
    info.setRxCount(2);
    info.setMicInConCount(2);

    createSampleStatus(info, dir + "36_hw1.2_mic_in.status");
  }

  /**
   * 创建vp6状态包.
   *
   * @param dir 目录
   */
  public static void createSampleForVp6Status(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setVp6Count(1);
    info.setNoFirstPortVp6Count(1);

    createSampleStatus(info, dir + "36_hw1.2_no_firstport_vp6.status");
  }

  /**
   * 创建vp7状态包.
   *
   * @param dir 目录
   */
  public static void createSampleForVp7Status(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setRxCount(2);
    info.setMatrixType(Type.SINGLE_CPU);
    info.setPorts(36);
    info.setVp7Count(2);

    createSampleStatus(info, dir + "36_hw1.2_vp7.status");
  }

  /**
   * 创建用于edid测试的状态包.
   *
   * @param dir 目录
   */
  public static void createSampleForEdidTest(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setRxCount(2);

    CaesarDeviceController controller = createDeviceController(info);
    controller.getDataModel().getConfigData().getExtenderData(0).setEdid(new byte[] {1, 2, 3}, 0);
    SaveStatusUtility.saveStatus(new File(dir + "36_hw1.2_edidtest.status"), controller, "");
  }

  /**
   * 创建双hdmi状态包.
   *
   * @param dir 生成目录
   */
  public static void createSampleForDhdmi(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setRxCount(2);
    info.setTxCount(2);
    info.setVp6Count(2);

    CaesarDeviceController controller = createDeviceController(info);
    CpuData cpuData =
        controller.getDataModel().getConfigDataManager().getActiveCpus().iterator().next();
    ExtenderData extenderData = cpuData.getExtenderData(0);
    extenderData.getExtenderStatusInfo().setInterfaceCount(2);
    extenderData
        .getExtenderStatusInfo()
        .setInterfaceType(ExtenderInterfaceType.INTERFACE_TYPE_HDMI);
    SaveStatusUtility.saveStatus(new File(dir + "36_hw1.2_dhdmi.status"), controller, "");
  }

  /**
   * 创建用于测试权限的状态包.
   *
   * @param dir 目录
   */
  public static void createSampleForRightsTest(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setPorts(96);
    info.setTxCount(10);
    info.setRxCount(14);
    info.setVp6Count(VideoWallGroupData.GROUP_COUNT + 1);
    CaesarDeviceController controller = createDeviceController(info);
    // 创建测试用户
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      UserData userData = controller.getDataModel().getConfigDataManager().getFreeUser();
      userData.setName("testrights" + i);
      userData.setRights(50);
      userData.setVideoWallRights(1 << i); // 只能操作第一个视频墙
      userData.setPassword("test");
      userData.setStatusActive(true);
    }

    // 创建视频墙
    VisualEditModel model = new VisualEditModel();
    model.init();
    CaesarData2VisualDataModel converter = new CaesarData2VisualDataModel();
    converter.setModel(model);
    converter.setDeviceController(controller);
    converter.loadAll();

    VideoWallConfig videoWallConfig = new VideoWallConfig();
    videoWallConfig.width = 1920;
    videoWallConfig.height = 1080;
    videoWallConfig.vert = 2;
    videoWallConfig.horz = 2;
    ScreenBinding screenBinding = new ScreenBinding();
    screenBinding.locations = new int[] {0, 1, 2, 3, -1, -1, -1, -1};
    ScreenConfig screenConfig = new ScreenConfig();
    screenConfig.binding = screenBinding;
    videoWallConfig.screens = new ScreenConfig[] {screenConfig};

    VideoConfig videoConfig = new VideoConfig();
    videoConfig.xpos = 0;
    videoConfig.ypos = 0;
    videoConfig.sourceid = 0;
    videoConfig.iw = 1920;
    videoConfig.ih = 1080;
    videoConfig.ow = 3840;
    videoConfig.oh = 2160;
    videoConfig.alpha = 1;

    videoWallConfig.videos = new VideoConfig[] {videoConfig};
    List<VpConsoleData> vpConsoleDatas =
        new ArrayList<>(controller.getDataModel().getConfigDataManager().getActiveVpconsolses());
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      CaesarVideoWallData videoWallData =
          VideoWallGenerator.createVideoWall(controller.getDataModel(),
              model, videoWallConfig, Collections.singletonList(vpConsoleDatas.get(i)));
      CaesarDataUtility.caesarVideoWallData2VpVideoWallData(
          videoWallData, controller.getDataModel().getVpDataModel().getVideoWallData(i));
    }
    // 创建跨屏
    List<ConsoleData> rxs =
        new ArrayList<>(controller.getDataModel().getConfigDataManager().getActiveConsoles());
    createCrossScreen(controller, "crossscreen", 1, 2, rxs.subList(0, 2));
    // 创建预案组
    createMultiScreen(model, controller, "multiscreen", 1, 2, rxs.subList(2, 4));

    // 保存状态包
    SaveStatusUtility.saveStatus(
        new File(dir + "96_hw1.2_rightstest.status"),
        controller,
        CaesarModelJsonBuilder.getBuilder().create().toJson(model));
  }

  /**
   * 创建所有端口都是RX的状态.
   *
   * @param dir 存放目录.
   */
  public static void create36WithFullRx(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setRxCount(36);
    info.setMatrixType(Type.SINGLE_CPU);
    info.setPorts(36);
    createSampleStatus(info, dir + "36_hw1.2_full_rx.status");
  }

  /**
   * 为测试创建status文件.
   *
   * @param dir 文件存放目录
   */
  public static void createSampleForTest(String dir) {
    final Type[] types =
        new Type[] {
          Type.SINGLE_CPU,
          Type.SINGLE_CPU,
          Type.STACK_96T_1x36B,
          Type.STACK_96T_12x24B,
          Type.PLUGIN_384,
          Type.PLUGIN_816, Type.PLUGIN_144
        };

    final int[] ports = new int[] {36, 96, 144, 288, 384, 816, 144};

    final int[] hardWardMasterVersion = new int[] {1, 2, 3, 3, 3, 3, 3};

    VersionDef version = new VersionDef();
    version.setMasterVersion(9);
    version.setSubVersion(10);
    version.setYear(19);
    version.setMonth(11);
    version.setDay(12);

    VersionDef hardwareVersion = new VersionDef();
    hardwareVersion.setMasterVersion(1);
    hardwareVersion.setSubVersion(2);

    for (int i = 0; i < types.length; i++) {
      hardwareVersion.setMasterVersion(hardWardMasterVersion[i]);

      SampleModelInfo info = new SampleModelInfo();
      info.setMatrixType(types[i]);
      info.setPorts(ports[i]);
      info.setRxCount(2);
      info.setTxCount(2);
      info.setVp6Count(2);

      initVersion(info, version, hardwareVersion);

      String fileNameFormat = "{0}_{1}_hw{2}.{3}_sample.status";
      String fileName =
          MessageFormat.format(
              fileNameFormat,
              types[i].getName(),
              ports[i],
              hardwareVersion.getMasterVersion(),
              hardwareVersion.getSubVersion());
      createSampleStatus(info, dir + fileName);
    }
  }

  /**
   * 创建简单的status info.
   *
   * @return status info.
   */
  public static SampleModelInfo createSimpleSingleStatusInfo() {
    SampleModelInfo info = new SampleModelInfo();

    info.setMatrixType(Type.SINGLE_CPU);
    info.setPorts(36);

    VersionDef version = new VersionDef();
    version.setMasterVersion(9);
    version.setSubVersion(10);
    version.setYear(19);
    version.setMonth(11);
    version.setDay(12);

    VersionDef hardwareVersion = new VersionDef();
    hardwareVersion.setMasterVersion(1);
    hardwareVersion.setSubVersion(2);

    initVersion(info, version, hardwareVersion);
    return info;
  }

  private static void initVersion(
      SampleModelInfo info, VersionDef version, VersionDef hardwareVersion) {
    info.getMatVersion().setAppVersion(version.copy());
    info.getMatVersion().setSystemVersion(version.copy());
    info.getMatVersion().setFpgaVersion(version.copy());
    info.getMatVersion().setOtherVersion(version.copy());
    info.getMatVersion().setHwVersionDef(hardwareVersion.copy());

    info.getIoVersion().setAppVersion(version.copy());
    info.getIoVersion().setSystemVersion(version.copy());
    info.getIoVersion().setFpgaVersion(version.copy());
    info.getIoVersion().setOtherVersion(version.copy());
    info.getIoVersion().setHwVersionDef(hardwareVersion.copy());

    info.getRxVersion().setAppVersion(version.copy());
    info.getRxVersion().setSystemVersion(version.copy());
    info.getRxVersion().setFpgaVersion(version.copy());
    info.getRxVersion().setOtherVersion(version.copy());
    info.getRxVersion().setHwVersionDef(hardwareVersion.copy());

    info.getTxVersion().setAppVersion(version.copy());
    info.getTxVersion().setSystemVersion(version.copy());
    info.getTxVersion().setFpgaVersion(version.copy());
    info.getTxVersion().setOtherVersion(version.copy());
    info.getTxVersion().setHwVersionDef(hardwareVersion.copy());

    info.getVp6Version().setAppVersion(version.copy());
    info.getVp6Version().setSystemVersion(version.copy());
    info.getVp6Version().setFpgaVersion(version.copy());
    info.getVp6Version().setOtherVersion(version.copy());
    info.getVp6Version().setHwVersionDef(hardwareVersion.copy());
  }

  /**
   * 创建816插满的状态文件.
   *
   * @param dir 文件存放目录.
   */
  public static void create816FullStatus(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setMatrixType(Type.PLUGIN_816);
    info.setPorts(816);
    info.setRxCount(408);
    info.setTxCount(408);

    createSampleStatus(info, dir + "816_hw1.2_full.status");
  }

  /**
   * 创建384跨屏的状态文件.
   *
   * @param dir 文件存放目录.
   */
  public static void create384CrossScreenFullTestStatus(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setMatrixType(Type.PLUGIN_384);
    info.setPorts(384);
    info.setRxCount(192);
    info.setTxCount(192);
    CaesarDeviceController controller = createDeviceController(info);
    List<ConsoleData> consoleDataList =
        new ArrayList<>(controller.getDataModel().getConfigDataManager().getActiveConsoles());
    for (int i = 0; i < controller.getDataModel().getConfigMetaData().getMultiScreenCount(); i++) {
      MultiScreenData screenData =
          controller.getDataModel().getConfigDataManager().getFreeMultiScreenData();
      screenData.setStatusActive(true);
      screenData.setVerticalNumber(1);
      screenData.setHorizontalNumber(1);
      screenData.setName("cross_name_" + i);
      screenData.setConInfoId(0, consoleDataList.get(i).getId());
    }
    // 保存状态包
    SaveStatusUtility.saveStatus(
        new File(dir + "384_hw1.2_cross_screen_full_test.status"), controller, "");
  }

  /**
   * 创建384多画面的状态文件.
   *
   * @param dir 文件存放目录.
   */
  public static void create384MultiviewTestStatus(String dir) {
    SampleModelInfo info = createSimpleSingleStatusInfo();
    info.setMatrixType(Type.PLUGIN_384);
    info.setPorts(384);
    info.setRxCount(192);
    info.setTxCount(192);
    CaesarDeviceController controller = createDeviceController(info);
    Collection<ConsoleData> activeConsoles =
        controller.getDataModel().getConfigDataManager().getActiveConsoles();
    int i = 0;
    for (ConsoleData consoleData : activeConsoles) {
      if (i >= 10) {
        break;
      }
      i++;
      consoleData.setMultiviewIndex(consoleData.getOid() + 1);
      MultiviewData multiviewData = controller.getDataModel().getConfigData().getMultiviewData(consoleData.getOid());
      multiviewData.setUsConIndex(consoleData.getOid() + 1);
    }
    // 保存状态包
    SaveStatusUtility.saveStatus(new File(dir + "384_hw1.2_10_multiview_rx.status"), controller, "");
  }

  /**
   * 创建状态文件.
   *
   * @param info 信息
   * @param path 文件保存路径
   */
  public static void createSampleStatus(SampleModelInfo info, String path) {
    CaesarDeviceController controller = createDeviceController(info);
    SaveStatusUtility.saveStatus(new File(path), controller, "");
    ConfigListUtility.saveCfgx(
        controller.getDataModel(), path + CaesarConstants.CFGX_FILE_EXTENSION);
  }

  /**
   * 创建状态文件.
   *
   * @param info 信息
   * @param path 文件保存路径
   */
  public static void createSampleStatus(SampleMatrixInfo info, String path) {
    CaesarDeviceController controller = createDeviceController(info);
    SaveStatusUtility.saveStatus(new File(path), controller, "");
    ConfigListUtility.saveCfgx(
        controller.getDataModel(), path + CaesarConstants.CFGX_FILE_EXTENSION);
  }

  /**
   * 创建跨屏.
   *
   * @param controller controller
   * @param row 行数
   * @param columns 列数
   * @param rxs 跨屏的rx
   */
  public static void createCrossScreen(
      CaesarDeviceController controller,
      String name,
      int row,
      int columns,
      Collection<ConsoleData> rxs) {
    MultiScreenData multiScreenData =
        controller.getDataModel().getConfigDataManager().getFreeMultiScreenData();
    multiScreenData.setStatusActive(true);
    multiScreenData.setHorizontalNumber(columns);
    multiScreenData.setVerticalNumber(row);
    multiScreenData.setName(name);
    int idx = 0;
    for (ConsoleData consoleData : rxs) {
      multiScreenData.setConInfoId(idx, consoleData.getId());
      idx++;
    }
    multiScreenData.setCtrlConId(multiScreenData.getConInfo(0).getConId());
    controller.addMultiScreenData(multiScreenData);
  }

  /**
   * 创建预案组.
   *
   * @param model model
   * @param controller controller
   * @param row 行数
   * @param columns 列数
   * @param rxs rx
   */
  public static void createMultiScreen(
      VisualEditModel model,
      CaesarDeviceController controller,
      String name,
      int row,
      int columns,
      Collection<ConsoleData> rxs) {
    List<VisualEditNode> nodes = new ArrayList<>();
    for (ConsoleData item : rxs) {
      nodes.add(CaesarDataUtility.findTerminal(model, item.getExtenderData(0)));
    }

    model.addGroup(name, CaesarMultiScreenFunc.class, nodes.toArray(new VisualEditNode[0]));
  }

  /**
   * 创建设备控制器.
   *
   * @param info 信息
   * @return 设备控制器
   */
  public static CaesarDeviceController createDeviceController(SampleModelInfo info) {
    CaesarSwitchDataModel model = new CaesarSwitchDataModel();
    model.initDefaults();
    SampleModelCreater.createDataModel(model, info, 0);
    return new CaesarDeviceController(model, "test", "test", "");
  }

  /**
   * 创建设备控制器.
   *
   * @param info 信息
   * @return 设备控制器
   */
  public static CaesarDeviceController createDeviceController(SampleMatrixInfo info) {
    List<CaesarSwitchDataModel> models = new ArrayList<>();
    List<Integer> lastExtenderIds = new ArrayList<>();
    int totalPorts = 0;
    for (SampleModelInfo statusInfo : info.getStatusInfoList()) {
      CaesarSwitchDataModel model = new CaesarSwitchDataModel();
      model.initDefaults();
      SampleModelCreater.createDataModel(model, statusInfo, totalPorts);
      models.add(model);
      int extSize = model.getConfigDataManager().getActiveExtenders().size();
      ExtenderData lastExtender =
          new ArrayList<>(model.getConfigDataManager().getActiveExtenders()).get(extSize - 1);
      lastExtenderIds.add(lastExtender.getId());
      totalPorts += statusInfo.getPorts();
    }

    ConfigurationMerger merger = new ConfigurationMerger();
    CaesarSwitchDataModel mainModel = models.get(0);
    ViewLogger logger = new ViewLogger();
    for (int i = 1; i < models.size(); i++) {
      merger.merge(mainModel, models.get(i), false, 0, logger);
    }

    // 填充matrixdata与moduleData
    int lastPort = 0;
    int lastModule = 0;
    for (int i = 0; i < info.getStatusInfoList().size(); i++) {
      SampleModelInfo sampleStatusInfo = info.getStatusInfoList().get(i);
      MatrixData matrixData = mainModel.getConfigData().getMatrixData(i);
      matrixData.setStatusActive(true);
      matrixData.setDevice(sampleStatusInfo.getDeviceName());
      matrixData.setPortCount(sampleStatusInfo.getPorts());
      matrixData.setFirstPort(lastPort + 1);
      matrixData.setLastPort(lastPort + sampleStatusInfo.getPorts());
      matrixData.setFirstModule(lastModule + 1);
      matrixData.setHostAddress(IpUtil.getAddressByte("192.168.2." + (i + 1)));
      CaesarSwitchDataModel model = models.get(i);
      model
          .getConfigData()
          .getSystemConfigData()
          .getNetworkDataCurrent1()
          .setAddress(matrixData.getHostAddress());

      MatrixData gridData = model.getConfigData().getGridData();
      int moduleCount = gridData.getLastModule() - gridData.getFirstModule() + 1;
      matrixData.setLastModule(lastModule += moduleCount);

      if (i > 0) {
        for (int j = 1; j < moduleCount + 1; j++) {
          ModuleData srcModuleData = model.getSwitchModuleData().getModuleData(j);
          ModuleData destModuleData = mainModel.getSwitchModuleData().getModuleData(lastModule + j);
          destModuleData.setStatusActive(true);
          destModuleData.setStatusAvailable(true);
          destModuleData.setType(srcModuleData.getType());
          destModuleData.setPorts(srcModuleData.getPorts());
          destModuleData.setVersion(srcModuleData.getVersion().copy());
        }
      }

      lastModule += moduleCount;
      lastPort += sampleStatusInfo.getPorts();
    }
    // 填充port data
    List<Integer> portIndexs = new ArrayList<>();
    int portOffset = 0;
    for (int i = 0; i < info.getStatusInfoList().size(); i++) {
      portIndexs.add(portOffset);
      portOffset += info.getStatusInfoList().get(i).getPorts();
    }
    // 清除之前的端口数据
    for (PortData data : mainModel.getConfigData().getPortDatas()) {
      data.setStatusAvailable(false);
      data.setStatusActive(false);
    }
    // 重新设定
    for (ExtenderData data : mainModel.getConfigDataManager().getActiveExtenders()) {
      for (int i = 0; i < lastExtenderIds.size(); i++) {
        if (data.getId() <= lastExtenderIds.get(i)) {
          int port = portIndexs.get(i);
          data.setPort(port + 1);
          portIndexs.set(i, port + 1);
          PortData portData = mainModel.getConfigData().getPortData(port);
          portData.setStatusAvailable(true);
          portData.setStatusActive(true);
          portData.setExtender(data.getOid() + 1);
          break;
        }
      }
    }

    for (Map.Entry<Integer, Integer> entry : info.getGridLines().entrySet()) {
      int port1 = entry.getKey();
      int port2 = entry.getValue();
      PortData portData1 = mainModel.getConfigData().getPortData(port1 - 1);
      PortData portData2 = mainModel.getConfigData().getPortData(port2 - 1);
      portData1.setStatusMatrix(true);
      portData2.setStatusMatrix(true);
      portData1.setType(port2);
      portData2.setType(port1);
    }

    mainModel.getConfigData().getSystemConfigData().getMatrixGridData().setMatrixGridEnabled(true);
    return new CaesarDeviceController(mainModel, "test", "test", "");
  }

  @Getter
  private static class SampleMatrixInfo {
    private List<SampleModelInfo> statusInfoList = new ArrayList<>();

    private Map<Integer, Integer> gridLines = new HashMap<>();
  }
}
