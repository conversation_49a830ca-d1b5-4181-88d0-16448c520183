package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpType;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuVideoWall;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import java.util.List;

/**
 * .
 */
public class MenuVideoWall4Caesar extends MenuVideoWall {
  private final CaesarVideoWallFuncManager videoWallFuncManager;

  /**
   * Constructor.
   */
  public MenuVideoWall4Caesar(
      SystemEditControllable controllable, CaesarVideoWallFuncManager videoWallFuncManager) {
    super(controllable);
    this.videoWallFuncManager = videoWallFuncManager;
    if (getEmptyIndex() < 0) {
      this.disableProperty().unbind();
      this.setDisable(true);
    }
  }

  protected int getEmptyIndex() {
    int index = -1;
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      if (videoWallFuncManager.getVideoWallFunc(i) == null) {
        index = i;
        break;
      }
    }
    return index;
  }

  @Override
  public MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = new MenuPredicateBinding(controllable, true);
    binding.addSingleSelectionPredicate(new TypePredicate(VpGroup.class).negate());
    binding.addSingleSelectionPredicate(node -> node.getParent() instanceof CaesarVideoWallFunc);
    return binding;
  }

  @Override
  protected void initLayoutData(List<VisualEditNode> nodeList) {
    // 检测是否是同一类型
    long count = nodeList.stream().map(node -> {
      if (node instanceof VpGroup) {
        return ((VpGroup) node).getVpConsoleData().getType();
      }
      return null;
    }).distinct().count();
    if (count > 1) {
      return;
    }
    if (!nodeList.isEmpty() && nodeList.get(0) instanceof VpGroup) {
      VpType type = ((VpGroup) nodeList.get(0)).getVpConsoleData().getType();
      String unusedVideoWallName = getUnusedVideoWallName(type.name());
      CaesarVideoWallFunc func =
          controllable.addGroup(unusedVideoWallName, CaesarVideoWallFunc.class,
              nodeList.toArray(new VisualEditNode[0]));
      if (func != null && !nodeList.isEmpty()) {
        int index = getEmptyIndex();
        func.setVideoWallIndex(index);
        LayoutData layoutData = func.getVideoWallObject().getLayoutData();
        layoutData.getRowsProperty().set(nodeList.size());
        int columns = 8;
        if (type == VpType.VP7) {
          func.getVideoWallObject().setType((short) 1);
          // 更新默认的分辨率
          layoutData.getResWidth().set(3840);
          layoutData.getResHeight().set(2160);
          // 更新默认的OSD宽度和高度
          if (func.getVideoWallObject() instanceof CaesarVideoWallData) {
            ((CaesarVideoWallData) func.getVideoWallObject()).getOsdData().setOsdWidth(40);
            ((CaesarVideoWallData) func.getVideoWallObject()).getOsdData().setOsdHeight(200);
          }
          // 更新默认的列数
          columns = 4;
        }
        layoutData.getColumnsProperty().set(columns);
        func.resetAndAutoBindScreens();
        videoWallFuncManager.setVideoWallFunc(index, func);
      }
    }
  }

  // 获取没被占用的视频墙名字,名为  type_Vw(1)\type_Vw(1)(2)\type_Vw(1)(2)(3)以此类推
  private String getUnusedVideoWallName(String type) {
    String name = type + "_Vw";
    for (int i = 1; i <= VideoWallGroupData.GROUP_COUNT; i++) {
      name = name + "(" + i + ")";
      boolean  isUsed = false;
      for (int j = 0; j < VideoWallGroupData.GROUP_COUNT; j++) {
        if (videoWallFuncManager.getVideoWallFunc(i) != null
            && videoWallFuncManager.getVideoWallFunc(i).getName().equals(name)) {
          isUsed = true;
        }
      }
      if (!isUsed) {
        return name;
      }
    }
    return type + "_Vw_" + System.currentTimeMillis();
  }
}
