package com.mc.tool.caesar.vpm.pages.systemedit.topological;

/**
 * .
 */
public class TopologicalUtility {

  public static final int RADIUS = 150;
  public static final int REGION_WIDTH = 150;
  public static final int REGION_HEIGHT = 30;
  public static final int CIRCLE_RADIUS = 13;

  /**
   * 根据角度获取圆点的x坐标.
   *
   * @param angle 角度
   * @return x坐标
   */
  public static double getCircleCenterXposByAngle(double angle) {
    return -Math.cos(Math.toRadians(angle)) * TopologicalUtility.RADIUS;
  }

  /**
   * 根据角度获取圆点的y坐标.
   *
   * @param angle 角度
   * @return y坐标
   */
  public static double getCircleCenterYposByAngle(double angle) {
    return -Math.sin(Math.toRadians(angle)) * TopologicalUtility.RADIUS;
  }

  public static double getCircleInnerXposByAngle(double angle) {
    return -Math.cos(Math.toRadians(angle)) * (TopologicalUtility.RADIUS - CIRCLE_RADIUS);
  }

  public static double getCircleInnerYposByAngle(double angle) {
    return -Math.sin(Math.toRadians(angle)) * (TopologicalUtility.RADIUS - CIRCLE_RADIUS);
  }

  /**
   * 获取角度获取矩阵项的x坐标加上偏移值.
   *
   * @param angle 角度
   * @return x坐标
   */
  public static double getMatrixItemXposByAngle(double angle) {
    return getCircleCenterXposByAngle(angle)
        - (TopologicalUtility.REGION_WIDTH - TopologicalUtility.CIRCLE_RADIUS);
  }

  /**
   * 根据角度获取矩阵项的y坐标加上偏移值.
   *
   * @param angle 角度
   * @return y坐标
   */
  public static double getMatrixItemYposByAngle(double angle) {
    return getCircleCenterYposByAngle(angle) - TopologicalUtility.REGION_HEIGHT / 2;
  }
}
