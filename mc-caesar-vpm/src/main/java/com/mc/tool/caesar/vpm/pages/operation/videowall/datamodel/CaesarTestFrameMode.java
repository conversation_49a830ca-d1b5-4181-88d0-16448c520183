package com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel;

import lombok.Getter;

/**
 * 测试画面模式.
 */
@Getter
public enum CaesarTestFrameMode {
  // todo 国际化
  CLOSE(0, "关闭测试画面"),
  GRAYSCALE(1, "灰阶"),
  COLOR_BARS(2, "彩条"),
  GRID(3, "网格"),
  CHECKERBOARD(4, "棋盘格"),
  HORIZONTAL_MOVING_BAR(5, "水平移动条"),
  VERTICAL_MOVING_BAR(6, "垂直移动条"),
  BIDIRECTIONAL_MOVING_BAR(7, "双向移动条"),
  CUSTOM_COLOR(8, "自定义颜色输出");

  private final int value;
  private final String name;

  CaesarTestFrameMode(int value, String name) {
    this.value = value;
    this.name = name;
  }

  /**
   * valueOf.
   */
  public static CaesarTestFrameMode valueOf(int value) {
    for (CaesarTestFrameMode mode : CaesarTestFrameMode.values()) {
      if (mode.getValue() == value) {
        return mode;
      }
    }
    return CLOSE;
  }

}
