package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import lombok.Setter;

/**
 * IpcInfo.
 */
@Setter
@JsonPropertyOrder({
    IpcInfo.JSON_PROPERTY_SOURCE_ID,
    IpcInfo.JSON_PROPERTY_NAME,
    IpcInfo.JSON_PROPERTY_STREAMS
})
public class IpcInfo {
  public static final String JSON_PROPERTY_SOURCE_ID = "sourceId";
  private Integer sourceId;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_STREAMS = "streams";
  private List<IpcStreamInfo> streams = new ArrayList<>();

  /**
   * .
   */
  public IpcInfo sourceId(Integer sourceId) {
    this.sourceId = sourceId;
    return this;
  }

  /**
   * Get sourceId.
   *
   * @return sourceId
   **/
  @JsonProperty(JSON_PROPERTY_SOURCE_ID)
  @JsonInclude()
  public Integer getSourceId() {
    return sourceId;
  }

  /**
   * .
   */
  public IpcInfo name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get name.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public String getName() {
    return name;
  }

  /**
   * .
   */
  public IpcInfo streams(List<IpcStreamInfo> streams) {
    this.streams = streams;
    return this;
  }

  /**
   * .
   */
  public IpcInfo addStreamsItem(IpcStreamInfo streamsItem) {
    this.streams.add(streamsItem);
    return this;
  }

  /**
   * Get streams.
   *
   * @return streams
   **/
  @JsonProperty(JSON_PROPERTY_STREAMS)
  @JsonInclude()
  public List<IpcStreamInfo> getStreams() {
    return streams;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IpcInfo ipcInfo = (IpcInfo) o;
    return Objects.equals(this.sourceId, ipcInfo.sourceId)
        && Objects.equals(this.name, ipcInfo.name)
        && Objects.equals(this.streams, ipcInfo.streams);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sourceId, name, streams);
  }


  @Override
  public String toString() {
    return "IpcInfo {\n"
        + "    sourceId: " + toIndentedString(sourceId) + "\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    streams: " + toIndentedString(streams) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
