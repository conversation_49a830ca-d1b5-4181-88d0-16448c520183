package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarPermissionConfigurationPageView extends VBox {

  @Getter private CaesarPermissionConfigurationController controller;

  /** Constructor. */
  public CaesarPermissionConfigurationPageView() {
    try {
      controller =
          InjectorProvider.getInjector().getInstance(CaesarPermissionConfigurationController.class);
      URL location =
          getClass()
              .getResource(
                  "/com/mc/tool/caesar/vpm/pages/permissionconfiguration"
                      + "/permissionconfiguration_view.fxml");
      ResourceBundle resources =
          ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.permissionconfiguration.Bundle");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controller);
      loader.setResources(resources);
      loader.setRoot(this);
      loader.load();
    } catch (IOException ex) {
      log.warn("Can not load permissionconfiguration_view.fxml", ex);
    }
  }
}
