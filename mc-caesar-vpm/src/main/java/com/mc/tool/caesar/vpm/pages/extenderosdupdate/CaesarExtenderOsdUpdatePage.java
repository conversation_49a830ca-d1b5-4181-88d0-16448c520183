package com.mc.tool.caesar.vpm.pages.extenderosdupdate;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.extenderosdupdate.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarExtenderOsdUpdatePage implements Page {
  private final ExtenderOsdUpdatePageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(false);
  public static final String NAME = "extenderosdupdate";

  public CaesarExtenderOsdUpdatePage(CaesarEntity entity) {
    view = new ExtenderOsdUpdatePageView();
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("ExtenderOsdUpdate.pageTitle.text");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-extenderosdupdate-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}

  @Override
  public void refresh() {
    view.getController().refresh();
  }

  @Override
  public void refreshOnShow() {
    view.getController().refresh();
  }
}
