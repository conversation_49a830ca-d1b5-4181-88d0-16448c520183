package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.datamodel.CaesarCrossScreenData;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.datamodel.CaesarCrossScreenData.CrossScreenMode;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collections;
import javafx.beans.property.ObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarCrossScreenFunc extends CrossScreenFunc {

  @Expose @Getter protected int multiScreenOid = -1;
  @Getter protected MultiScreenData deviceData;
  @Expose protected CaesarCrossScreenData crossScreenData = new CaesarCrossScreenData();

  private SimpleObservable resetObservable = new SimpleObservable();

  private boolean hasNameListener = false;

  /**
   * 增加一个名字修改监听器.
   *
   * @param deviceController 设备控制器
   */
  public synchronized void addNameListener(CaesarDeviceController deviceController) {
    if (!hasNameListener) {
      hasNameListener = true;
      // 监听名称修改
      this.nameProperty()
          .addListener(
              (obs, oldVal, newVal) -> {
                if (this.getDeviceData() == null
                    || this.getDeviceData().isStatusNew()
                    || !this.getDeviceData().isStatusActive()) {
                  return;
                }
                deviceController.execute(
                    () -> {
                      this.getDeviceData().setName(this.getName());
                      System.out.println("change name: " + this.getName());
                      try {
                        deviceController
                            .getDataModel()
                            .sendMultiscreenData(
                                Collections.singletonList(this.getDeviceData()));
                      } catch (DeviceConnectionException | BusyException exception) {
                        log.warn("Fail to send multiscreen data!", exception);
                      }
                    });
              });
    }
  }

  public CaesarCrossScreenFunc() {
    crossScreenData.resetCapacity(16);
  }

  /**
   * 设置设备的跨屏数据.
   *
   * @param deviceData 跨屏数据.
   */
  public void setDeviceData(MultiScreenData deviceData) {
    this.deviceData = deviceData;
    if (deviceData != null) {
      multiScreenOid = deviceData.getOid();
    } else {
      multiScreenOid = -1;
    }
  }

  /**
   * .
   */
  public boolean canAddChild() {
    return getChildren().size()
            < crossScreenData.getRows().get() * crossScreenData.getColumns().get()
        && deviceData != null
        && deviceData.isStatusNew();
  }

  /**
   * 设置跨屏布局.
   *
   * @param row 行数
   * @param column 列数
   */
  public void setLayout(int row, int column) {
    crossScreenData.getRows().set(row);
    crossScreenData.getColumns().set(column);
    crossScreenData.resetCapacity(row * column);
    resetObservable.update();
  }

  public CaesarCrossScreenData getCaesarCrossScreenData() {
    return crossScreenData;
  }

  /**
   * 从设备的跨屏数据读取到当前数据.
   *
   * @param matrix 矩阵
   * @param controller 设备的控制器
   */
  public void reload(CaesarMatrix matrix, CaesarDeviceController controller) {
    setName(deviceData.getName());
    addNameListener(controller);
    crossScreenData.getName().set(deviceData.getName());
    setLayout(deviceData.getVerticalNumber(), deviceData.getHorizontalNumber());
    crossScreenData.getDisplayTime().set(deviceData.getUsFrame());
    crossScreenData
        .getMode()
        .set(deviceData.getType() == 0 ? CrossScreenMode.AUTO : CrossScreenMode.MANUAL);
    int ctrlId = deviceData.getCtrlConId();
    ConfigDataManager dataManager = controller.getDataModel().getConfigDataManager();
    crossScreenData.getControlSource().set(findTerminalById(matrix, dataManager, ctrlId));
    for (int i = 0;
        i < Math.min(CaesarConstants.MultiScreen.MAX_CON, crossScreenData.getTargets().size());
        i++) {
      crossScreenData
          .getTarget(i)
          .set(findTerminalById(matrix, dataManager, deviceData.getConInfo(i).getConId()));
    }
  }

  protected VisualEditTerminal findTerminalById(
      CaesarMatrix matrix, ConfigDataManager dataManager, int id) {
    if (id == 0) {
      return null;
    }
    ConsoleData consoleData = dataManager.getConsoleData4Id(id);
    if (consoleData == null) {
      return null;
    }
    return matrix.findTerminal(consoleData);
  }

  /** 把当前数据写入到设备数据. */
  public void commit() {
    deviceData.setName(nameProperty.get());
    deviceData.setHorizontalNumber(crossScreenData.getColumns().get());
    deviceData.setVerticalNumber(crossScreenData.getRows().get());
    deviceData.setUsFrame(crossScreenData.getDisplayTime().get());
    deviceData.setType(crossScreenData.getMode().get() == CrossScreenMode.AUTO ? 0 : 1);
    VisualEditTerminal controlTerminal = crossScreenData.getControlSource().get();
    if (controlTerminal instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) controlTerminal;
      if (conTerminal.getConsoleData() != null) {
        deviceData.setCtrlConId(conTerminal.getConsoleData().getId());
      } else {
        log.warn("Console is null!");
      }
    } else {
      log.warn("Terminal is not con!");
    }
    //
    int count = deviceData.getHorizontalNumber() * deviceData.getVerticalNumber();
    for (int i = 0; i < count; i++) {
      VisualEditTerminal terminal = crossScreenData.getTarget(i).get();
      if (terminal instanceof CaesarConTerminal) {
        CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
        if (conTerminal.getConsoleData() != null) {
          deviceData.setConInfoId(i, conTerminal.getConsoleData().getId());
        } else {
          deviceData.setConInfoId(i, 0);
        }
      } else {
        deviceData.setConInfoId(i, 0);
      }
    }
    for (int i = count; i < CaesarConstants.MultiScreen.MAX_CON; i++) {
      deviceData.setConInfoId(i, 0);
    }
  }

  @Override
  public void init() {
    super.init();

    getAllTerminalChild()
        .addListener(
            (ListChangeListener<VisualEditTerminal>)
                change -> {
                  while (change.next()) {
                    if (change.wasRemoved()) {
                      for (VisualEditTerminal item : change.getRemoved()) {
                        removeItem(item);
                      }
                    }

                    if (change.wasAdded()) {
                      for (VisualEditTerminal item : change.getAddedSubList()) {
                        addItem(item);
                        if (crossScreenData.getControlSource().get() == null) {
                          crossScreenData.getControlSource().set(item);
                        }
                      }
                    }
                  }
                });
  }

  protected void addItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreenData.getTargets()) {
      if (property.get() == null) {
        property.set(item);
        if (!crossScreenData.getConnections().containsKey(item)) {
          SeatConnection connection = new SeatConnection();
          connection.setRx(item);
          crossScreenData.getConnections().put(item, connection);
        }
        break;
      }
    }
  }

  protected void removeItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreenData.getTargets()) {
      if (property.get() == item) {
        property.set(null);
      }
    }
    crossScreenData.getConnections().remove(item);
  }

  @Override
  public CrossScreenObject getCrossScreenData() {
    return crossScreenData;
  }

  @Override
  public void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn) {
    if (fromRow < 0
        || fromRow >= getMaxRow()
        || fromColumn < 0
        || fromColumn >= getMaxColumn()
        || toRow < 0
        || toRow >= getMaxRow()
        || toColumn < 0
        || toColumn >= getMaxColumn()) {
      return;
    }

    int fromIndex = getIndex(fromRow, fromColumn);
    int toIndex = getIndex(toRow, toColumn);
    ObjectProperty<VisualEditTerminal> from = crossScreenData.getTarget(fromIndex);
    VisualEditTerminal target = from.get();
    from.set(null);
    crossScreenData.insertTarget(target, toIndex);
  }

  protected int getIndex(int row, int column) {
    return row * getMaxColumn() + column;
  }

  @Override
  public Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal) {
    int index = crossScreenData.indexOfTarget(terminal);
    if (index < 0) {
      return new Pair<>(-1, -1);
    } else {
      return new Pair<>(index / getMaxColumn(), index % getMaxColumn());
    }
  }

  @Override
  public boolean addScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public boolean removeScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public ObservableList<? extends CrossScreenObject> getScenarios() {
    return null;
  }

  @Override
  public int getMaxRow() {
    return crossScreenData.getRows().get();
  }

  @Override
  public int getMaxColumn() {
    return crossScreenData.getColumns().get();
  }

  @Override
  public boolean isScreenMovable() {
    return deviceData != null && deviceData.isStatusNew();
  }

  @Override
  public boolean isChildIndexChangable() {
    return false;
  }

  @Override
  public SimpleObservable getResetObservable() {
    return resetObservable;
  }
}
