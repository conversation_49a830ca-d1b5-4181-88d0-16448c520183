package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import java.util.Collection;
import java.util.Collections;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * .
 */
@Data
@AllArgsConstructor
public class MacroMultiviewParam implements MacroParam {
  private int index;
  private String layoutName;

  @Override
  public Collection<MacroParam> getChildren() {
    return Collections.emptyList();
  }

  @Override
  public void addChild(MacroParam param) {
  }

  @Override
  public void removeChild(MacroParam param) {
  }

  @Override
  public String getFullName() {
    return String.format("[%d] %s", index, layoutName);
  }

  @Override
  public boolean hasOid() {
    return false;
  }

  @Override
  public int getOid() {
    return -1;
  }
}
