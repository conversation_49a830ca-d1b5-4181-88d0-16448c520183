package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.tool.caesar.vpm.kaito.model.InputCard;
import javafx.util.StringConverter;

/**
 * InputCardStringConverter.
 */
public class InputCardStringConverter extends StringConverter<InputCard> {

  @Override
  public String toString(InputCard object) {
    if (object == null) {
      return "";
    }
    return object.getName();
  }

  @Override
  public InputCard fromString(String string) {
    return null;
  }
}