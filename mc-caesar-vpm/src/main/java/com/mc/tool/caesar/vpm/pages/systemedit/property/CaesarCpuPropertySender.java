package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarCpuPropertySender<T> implements CaesarPropertySender<T> {
  private final CaesarDeviceController controller;
  private final String propertyName;
  private final CpuData cpuData;

  /**
   * Constructor.
   *
   * @param controller device controller
   * @param cpuData cpu data
   * @param propertyName property name
   */
  public CaesarCpuPropertySender(
      CaesarDeviceController controller, CpuData cpuData, String propertyName) {
    this.controller = controller;
    this.propertyName = propertyName;
    this.cpuData = cpuData;
  }

  @Override
  public void sendValue(T value) {
    controller.execute(
        () -> {
          try {
            cpuData.setProperty(propertyName, value);
            controller.getDataModel().sendCpuData(Collections.singletonList(cpuData));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
