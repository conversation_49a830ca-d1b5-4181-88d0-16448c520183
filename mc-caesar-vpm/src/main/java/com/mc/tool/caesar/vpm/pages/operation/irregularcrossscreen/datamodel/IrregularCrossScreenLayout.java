package com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.datamodel;

import com.google.gson.annotations.Expose;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import lombok.Getter;

/**
 * .
 */
public class IrregularCrossScreenLayout {
  /** 视频位置的横坐标. */
  @Expose @Getter private IntegerProperty xpos = new SimpleIntegerProperty();

  /** 视频位置的纵坐标. */
  @Expose @Getter private IntegerProperty ypos = new SimpleIntegerProperty();

  /** 视频的宽度. */
  @Expose @Getter private IntegerProperty width = new SimpleIntegerProperty();

  /** 视频的高度. */
  @Expose @Getter private IntegerProperty height = new SimpleIntegerProperty();
}
