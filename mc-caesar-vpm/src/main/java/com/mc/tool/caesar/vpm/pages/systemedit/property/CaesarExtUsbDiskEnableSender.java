package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarExtUsbDiskEnableSender implements CaesarPropertySender<Boolean> {

  private final CaesarDeviceController controller;
  private final ExtenderData extenderData;
  private final String propertyName;

  /** . */
  public CaesarExtUsbDiskEnableSender(
      CaesarDeviceController controller, ExtenderData extenderData, String propertyName) {
    this.controller = controller;
    this.extenderData = extenderData;
    this.propertyName = propertyName;
  }

  @Override
  public void sendValue(Boolean value) {
    controller.execute(
        () -> {
          extenderData.setProperty(propertyName, value);
          try {
            controller.getDataModel().setExtUsbDiskEnable(extenderData, value);
          } catch (BusyException | ConfigException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
