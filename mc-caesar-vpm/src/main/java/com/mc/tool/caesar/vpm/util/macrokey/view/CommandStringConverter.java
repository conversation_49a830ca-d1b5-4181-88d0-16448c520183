package com.mc.tool.caesar.vpm.util.macrokey.view;

import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import javafx.util.StringConverter;

/**
 * .
 */
public class CommandStringConverter extends StringConverter<Command> {

  @Override
  public String toString(Command object) {
    if (object == null) {
      return "";
    }
    return object.getName();
  }

  @Override
  public Command fromString(String string) {
    return null;
  }
}
