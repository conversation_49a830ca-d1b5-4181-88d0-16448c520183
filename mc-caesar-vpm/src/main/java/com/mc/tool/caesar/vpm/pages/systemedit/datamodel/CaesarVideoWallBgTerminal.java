package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.framework.systemedit.datamodel.AbstractVisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.NetworkInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.collections.ObservableList;

/**
 * .
 */
public class CaesarVideoWallBgTerminal extends AbstractVisualEditNode
    implements VisualEditTerminal {

  private Resolution resolution = new Resolution();

  public CaesarVideoWallBgTerminal(int width, int height) {
    resolution.setWidth(width);
    resolution.setHeight(height);
  }

  @Override
  public boolean isTx() {
    return true;
  }

  @Override
  public boolean isRx() {
    return false;
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.TERMINAL_CELL;
  }

  @Override
  public String getGuid() {
    return "caesar.videowall.bgimg.terminal";
  }

  @Override
  public int getPortCount() {
    return 0;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    return null;
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return null;
  }

  @Override
  public void init() {}

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return null;
  }

  @Override
  public boolean isOnline() {
    return true;
  }

  @Override
  public boolean isValid() {
    return true;
  }

  @Override
  public BooleanProperty onlineProperty() {
    return null;
  }

  @Override
  public Resolution getResolution(int index) {
    return resolution;
  }

  @Override
  public boolean isTargetDeviceConnected() {
    return false;
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty() {
    return null;
  }

  @Override
  public TargetDeviceType getTargetDeviceType() {
    return null;
  }

  @Override
  public void setTargetDeviceType(TargetDeviceType type) {}

  @Override
  public ObjectProperty<TargetDeviceType> targetDeviceTypeProperty() {
    return null;
  }

  @Override
  public NetworkInterfaceType getNetworkInterfaceType() {
    return null;
  }

  @Override
  public void setNetworkInterfaceType(NetworkInterfaceType type) {}

  @Override
  public ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty() {
    return null;
  }

  @Override
  public MultimediaInterfaceType getMultimediaInterfaceType() {
    return null;
  }

  @Override
  public void setMultimediaInterfaceType(MultimediaInterfaceType type) {}

  @Override
  public ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty() {
    return null;
  }

  @Override
  public boolean canSeperate() {
    return false;
  }

  @Override
  public void seperate(boolean sep) {}
}
