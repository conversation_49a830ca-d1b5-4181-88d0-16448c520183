package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarOsdData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.pages.update.UpdateTask;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.TextField;
import javafx.scene.control.ToggleButton;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.stage.Window;
import javax.imageio.ImageIO;

/**
 * .
 */
public class UploadBgImgView extends VBox {

  private static final int BGIMG_MAX_WIDTH = 3840;
  private static final int BGIMG_MAX_HEIGHT = 2160;
  private TextField filePathField;
  private Button selectButton;
  private ToggleButton uploadButton; // 没选中时是上传，选中时是取消
  private ProgressBar progressBar;

  private UpdateLogger logger = new UpdateLogger();
  private CaesarSwitchDataModel model;
  private CaesarVideoWallFunc videoWallFunc;

  private StringProperty filePathProperty = new SimpleStringProperty();
  private BooleanProperty uploadingPropety = new SimpleBooleanProperty();
  private ObjectProperty<BgImgUploadTask> uploadTaskProperty = new SimpleObjectProperty<>();

  /**
   * 创建上传背景图视图.
   *
   * @param model model
   * @param videoWallFunc 视频墙func
   */
  public UploadBgImgView(CaesarSwitchDataModel model, CaesarVideoWallFunc videoWallFunc) {
    this.model = model;
    this.videoWallFunc = videoWallFunc;
    HBox hbox = new HBox();
    hbox.setAlignment(Pos.CENTER);
    hbox.setSpacing(20);

    HBox leftBox = new HBox();
    leftBox.setAlignment(Pos.CENTER);
    leftBox.setSpacing(10);
    Label label = new Label();
    label.setText(CaesarI18nCommonResource.getString("videowall.bgimg.uploadpath"));
    filePathField = new TextField();
    filePathField.setEditable(false);
    filePathField.textProperty().bind(filePathProperty);
    selectButton = new Button();
    selectButton.setText(CaesarI18nCommonResource.getString("videowall.bgimg.selectbtn"));
    HBox.setHgrow(leftBox, Priority.ALWAYS);
    HBox.setHgrow(filePathField, Priority.ALWAYS);
    leftBox.getChildren().addAll(label, filePathField, selectButton);

    HBox rightBox = new HBox();
    rightBox.setAlignment(Pos.CENTER);
    rightBox.setSpacing(10);
    uploadButton = new ToggleButton();
    uploadButton
        .textProperty()
        .bind(
            Bindings.when(uploadButton.selectedProperty())
                .then(CaesarI18nCommonResource.getString("videowall.bgimg.cancel"))
                .otherwise(CaesarI18nCommonResource.getString("videowall.bgimg.upload")));
    uploadButton.setSelected(false);
    // 当没有选择文件时，或者还在上传期间不能上传
    uploadButton
        .disableProperty()
        .bind(
            uploadButton
                .selectedProperty()
                .not()
                .and(filePathProperty.isEmpty().or(uploadingPropety)));
    selectButton.disableProperty().bind(uploadingProperty());
    progressBar = new ProgressBar();
    progressBar.setProgress(0);
    rightBox.getChildren().addAll(uploadButton, progressBar);

    hbox.getChildren().addAll(leftBox, rightBox);

    ListView<String> logList = new ListView<>();
    logList.setItems(logger.getLogs());
    logList.setPrefHeight(200);
    logList.setStyle("-fx-border-size: 1; -fx-border-color: #e6e6e6");

    this.getChildren().addAll(hbox, logList);
    this.setSpacing(10);

    // 监听事件
    selectButton.setOnAction(
        (event) -> {
          FileDialogueFactory factory =
              InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
          FileDialogue fileDialogue = factory.createFileDialogue();
          File file = fileDialogue.showOpenDialog(getScene().getWindow());
          if (file != null) {
            filePathProperty.set(file.getAbsolutePath());
          }
        });

    uploadButton
        .selectedProperty()
        .addListener(
            (obs, oldVal, newVal) -> {
              if (newVal) {
                // 启动上传
                uploadingPropety.set(true);
                uploadTaskProperty.set(new BgImgUploadTask());
                progressBar.progressProperty().bind(uploadTaskProperty.get().progressProperty());
                new Thread(uploadTaskProperty.get()).start();
              } else {
                // 取消上传
                uploadTaskProperty.get().setCancelled(true);
              }
            });
  }

  public ReadOnlyBooleanProperty uploadingProperty() {
    return uploadingPropety;
  }

  class BgImgUploadTask extends UpdateTask {

    private int level1;
    private int level2;

    @Override
    protected Object call() throws Exception {
      return runImpl();
    }

    protected boolean runImpl() {
      try {
        logger.addLog("Start to load image.");
        BufferedImage image;
        try {
          image = ImageIO.read(new File(filePathProperty.get()));
        } catch (IOException exception) {
          logger.addLog("Fail to load image! Ccelled!", exception);
          cancelled = true;
          return false;
        }
        logger.addLog(String.format("Image's size is %d*%d", image.getWidth(), image.getHeight()));
        final byte[] data = translateImage(image);
        logger.addLog("Finish image loading.");
        logger.addLog("Start to upload data.");
        // 准备信息
        Map<Boolean, List<VpConsoleData>> vpconsoles =
            videoWallFunc.getChildren().stream()
                .map((item) -> item instanceof VpGroup ? ((VpGroup) item).getVpConsoleData() : null)
                .collect(Collectors.groupingBy((item) -> item != null && item.isStatusOnline()));
        List<VpConsoleData> offlineData = vpconsoles.get(Boolean.FALSE);
        if (offlineData != null && !offlineData.isEmpty()) {
          for (VpConsoleData item : offlineData) {
            logger.addLog(String.format("%s is not online. Ignored.", item.getName()));
          }
        }
        List<VpConsoleData> onlineData = vpconsoles.get(Boolean.TRUE);
        if (onlineData == null || onlineData.isEmpty()) {
          logger.addLog("No device to upload! Cancelled!");
          cancelled = true;
          return false;
        }
        boolean result = false;
        for (VpConsoleData item : onlineData) {
          logger.addLog("Uploading data to " + item.getName());
          // 升级
          ExtenderData extenderData = item.getInPort(0).getExtenderData(0);
          level1 = Utilities.getLevel1(model, extenderData);
          level2 = Utilities.getLevel2(model, extenderData);
          result = updateImpl(data, CaesarConstants.VP_BGIMG_UPLOAD_PAGE_SIZE, 0, logger);
          if (!result) {
            logger.addLog("Fail to upload data to " + item.getName());
            break;
          } else {
            logger.addLog("Finish uploading data to " + item.getName());
          }
        }
        if (result) {
          logger.addLog("Upload successfully!");
          // 修改背景图信息
          final int width = Math.min(image.getWidth(), BGIMG_MAX_WIDTH);
          final int height = Math.min(image.getHeight(), BGIMG_MAX_HEIGHT);
          final CaesarVideoWallFunc func = videoWallFunc;
          PlatformUtility.runInFxThread(
              () -> {
                CaesarOsdData osdData =
                    ((CaesarVideoWallData) func.getVideoWallObject()).getOsdData();
                osdData.setBgImgWidth(width);
                osdData.setBgImgHeight(height);
              });
        } else {
          logger.addLog("Upload failed!");
        }
        return true;
      } catch (Exception exception) {
        logger.addLog("Unknown error while uploading! Cancelled!", exception);
        return false;
      } finally {
        PlatformUtility.runInFxThread(
            () -> {
              uploadingPropety.set(false);
              uploadButton.setSelected(false);
            });
      }
    }

    private byte[] translateImage(BufferedImage image) {
      final int partition = 1920;
      final int blockSize = 64;
      final double lineSizeTemp = partition * 3 / 512.0;
      final int lineSizeTempInt = (int) lineSizeTemp;
      // Ceil line Size temp
      final int lineSize =
          ((lineSizeTemp - lineSizeTempInt) > 0 ? lineSizeTempInt + 1 : lineSizeTempInt) * 512;
      final int leftStart = 0;
      final int maxWidth = BGIMG_MAX_WIDTH;
      final int maxHeight = BGIMG_MAX_HEIGHT;
      final int rightStart = lineSize * maxHeight;
      final int totalSize = lineSize * maxHeight * 2;

      byte[] data = new byte[totalSize];
      // 连续放64个颜色
      int roffset = 128;
      int goffset = 64;
      int boffset = 0;
      int width = Math.min(image.getWidth(), maxWidth);
      int height = Math.min(image.getHeight(), maxHeight);

      for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
          final int rgb = image.getRGB(x, y);
          final int red = (rgb & 0xff0000) >> 16;
          final int green = (rgb & 0xff00) >> 8;
          final int blue = rgb & 0xff;
          // 地址偏移
          int offset = leftStart + lineSize * y;
          // 横向位置
          int hpos = x;
          if (x >= partition) {
            offset = rightStart + lineSize * y;
            hpos = x - partition;
          }
          // 计算存储位置
          int blockIndex = hpos / blockSize;
          int blockOffset = blockIndex * blockSize * 3; // 一个像素3个颜色
          int index = (y * partition + hpos) % blockSize;
          int rloc = offset + blockOffset + roffset + index;
          int gloc = offset + blockOffset + goffset + index;
          int bloc = offset + blockOffset + boffset + index;
          data[rloc] = (byte) red;
          data[gloc] = (byte) green;
          data[bloc] = (byte) blue;
        }
      }
      return data;
    }

    @Override
    protected void updateOpen() throws DeviceConnectionException, ConfigException, BusyException {}

    @Override
    protected void updateWrite(int offset, byte[] dataToWrite)
        throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().setVpSdCard((byte) level1, (byte) level2, offset, dataToWrite, false);
    }

    @Override
    protected void updateClose(boolean successful)
        throws DeviceConnectionException, ConfigException, BusyException {
      model.getController().resetVpSdCard((byte) level1, (byte) level2);
    }
  }

  /**
   * 显示上传背景图对话框.
   *
   * @param owner 对话框的owner
   * @param model model
   * @param func 视频墙func
   * @param title 对话框标题
   */
  public static void show(
      Window owner, CaesarSwitchDataModel model, CaesarVideoWallFunc func, String title) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    UploadBgImgView view = new UploadBgImgView(model, func);
    dialog.initOwner(owner);
    dialog.setTitle(title);
    dialog.getDialogPane().setContent(view);
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.FINISH);
    dialog
        .getDialogPane()
        .lookupButton(ButtonType.FINISH)
        .disableProperty()
        .bind(view.uploadingProperty());
    dialog.showAndWait();
  }
}
