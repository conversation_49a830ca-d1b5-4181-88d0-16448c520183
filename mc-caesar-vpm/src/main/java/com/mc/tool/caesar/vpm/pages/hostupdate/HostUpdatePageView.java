package com.mc.tool.caesar.vpm.pages.hostupdate;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class HostUpdatePageView extends VBox {

  @Getter private HostUpdatePageController controller;

  /** Contructor. */
  public HostUpdatePageView() {
    try {
      controller = InjectorProvider.getInjector().getInstance(HostUpdatePageController.class);
      URL location =
          getClass().getResource("/com/mc/tool/caesar/vpm/pages/hostupdate/hostupdate_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controller);
      loader.setResources(null);
      loader.setResources(
          ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.hostupdate.Bundle"));
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load hostupdate_view.fxml", exc);
    }
  }
}
