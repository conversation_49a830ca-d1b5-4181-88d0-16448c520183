package com.mc.tool.caesar.vpm.pages.systemedit.topological.graph;

import com.mc.graph.McGraph;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.TopologicalUtility;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixItem;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.systemedit.controller.GraphWrapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javafx.beans.InvalidationListener;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;

/**
 * .
 */
public class TopologicalGraphWrapper implements GraphWrapper {

  protected List<GridMatrixItem> items;
  private TopologicalGraph graph;
  Map<GridMatrixItem, CellObject> matrixCellMap = new HashMap<>();

  private List<InvalidationListener> scaleChangeListeners = new ArrayList<>();
  private List<ListChangeListener<SelectableNode>> selectChangeListeners = new ArrayList<>();

  private BooleanProperty attached = new SimpleBooleanProperty(false);

  /**
   * .
   *
   * @param graph graph
   * @param items 矩阵项
   */
  public TopologicalGraphWrapper(TopologicalGraph graph, Collection<GridMatrixItem> items) {
    this.items = new ArrayList<>(items);
    this.graph = graph;
    graph.init();
    graph
        .getSelectionModel()
        .getSelectedItems()
        .addListener((ListChangeListener<? super SelectableNode>) (change) -> highLightSelection());

    orderNodes(this.items);
  }

  private static void orderNodes(List<GridMatrixItem> nodes) {
    List<OrderInfo> orderInfos = new ArrayList<>();
    for (int i = 0; i < nodes.size(); i++) {
      orderInfos.add(new OrderInfo(i, 0));
      nodes.get(i).setPosition(i);
    }

    int move = 1;
    int iteration = 0;
    while (move > 0 && iteration < 100) {
      iteration++;
      Map<Integer, Integer> orderMap = new HashMap<>();
      for (int i = 0; i < orderInfos.size(); i++) {
        orderMap.put(orderInfos.get(i).position, i);
      }

      for (int i = 0; i < orderInfos.size(); i++) {
        OrderInfo orderInfo = orderInfos.get(i);
        GridMatrixItem node = nodes.get(orderInfo.position);
        int p1 = i;
        double sumX = Math.cos(Math.toRadians(angle(p1, nodes.size())));
        double sumY = Math.sin(Math.toRadians(angle(p1, nodes.size())));
        for (GridMatrixItem near : node.getRelatedItems().keySet()) {
          int p2 = orderMap.get(near.getPosition());
          sumX += Math.cos(Math.toRadians(angle(p2, nodes.size())));
          sumY += Math.sin(Math.toRadians(angle(p2, nodes.size())));
        }
        orderInfo.average = angleOfVector(sumX, sumY);
      }

      List<OrderInfo> oldOrders = new ArrayList<>(orderInfos);
      orderInfos.sort(Comparator.comparingDouble(left -> left.average));
      move = 0;
      for (int i = 0; i < orderInfos.size(); i++) {
        move += Math.abs(orderInfos.get(i).position - oldOrders.get(i).position);
      }
    }

    List<GridMatrixItem> newList = new ArrayList<>();

    for (int i = 0; i < orderInfos.size(); i++) {
      OrderInfo info = orderInfos.get(i);
      GridMatrixItem item = nodes.get(info.position);
      item.setPosition(i);
      newList.add(item);
    }
    nodes.clear();
    nodes.addAll(newList);
  }

  private static double angle(int index, int size) {
    return index * 360.0 / size;
  }

  private static double angleOfVector(double xpos, double ypos) {
    double hypotenuse = Math.sqrt(xpos * xpos + ypos * ypos);
    double theta = Math.asin(ypos / hypotenuse);
    if (xpos < 0) {
      theta = Math.PI - theta;
    }
    if (theta < 0) {
      theta += 2 * Math.PI;
    }
    return theta;
  }

  private void highLightSelection() {
    Set<CellSkin> selectedSkins = new HashSet<>(graph.getSelectionModel().getSelectedCellSkin());
    Set<CellSkin> unHighLightSkins = new HashSet<>(graph.getSkinManager().getAllCellSkin());
    Set<CellObject> highLightCells = new HashSet<>();

    for (CellSkin skin : selectedSkins) {
      CellBindedObject obj = skin.getCell().getBindedObject();
      if (obj instanceof GridMatrixItem) {
        GridMatrixItem item = (GridMatrixItem) obj;
        for (GridMatrixItem near : item.getRelatedItems().keySet()) {
          CellObject cell = matrixCellMap.get(near);
          if (cell != null) {
            highLightCells.add(cell);
          }
        }
      }
      highLightCells.add(skin.getCell());
    }

    for (CellObject cell : highLightCells) {
      cell.highLightProperty().set(true);
    }
    for (CellSkin skin : unHighLightSkins) {
      if (highLightCells.contains(skin.getCell())) {
        continue;
      }
      skin.getCell().highLightProperty().set(false);
    }
  }

  @Override
  public void attachScaleChangeListener(InvalidationListener scaleChangeListener) {
    graph.getGraphCanvas().getScaleProperty().addListener(scaleChangeListener);
    scaleChangeListeners.add(scaleChangeListener);
  }

  @Override
  public void attachSelectChangeListener(ListChangeListener<SelectableNode> selectChangeListener) {
    graph.getSelectionModel().getSelectedItems().addListener(selectChangeListener);
    selectChangeListeners.add(selectChangeListener);
  }

  @Override
  public void attachClickEventHandler(EventHandler<MouseEvent> clickEventHandler) {}

  @Override
  public void attachClickEventFilter(EventHandler<MouseEvent> clickEventFilter) {}

  @Override
  public void attach() {
    attached.set(true);
  }

  @Override
  public void detach() {
    attached.set(false);
    for (InvalidationListener listener : scaleChangeListeners) {
      graph.getGraphCanvas().getScaleProperty().removeListener(listener);
    }
    for (ListChangeListener<SelectableNode> listener : selectChangeListeners) {
      graph.getSelectionModel().getSelectedItems().removeListener(listener);
    }
    scaleChangeListeners.clear();
    selectChangeListeners.clear();
  }

  @Override
  public boolean isAttached() {
    return attached.get();
  }

  @Override
  public void updateModelToGraph() {
    double anglePer = 360.0 / items.size();
    matrixCellMap.clear();
    graph.retainCells();
    graph.retainLinks();
    int size = items.size();
    for (int i = 0; i < size; i++) {
      GridMatrixItem item = items.get(i);
      double angle = anglePer * i;
      double xpos = TopologicalUtility.getMatrixItemXposByAngle(angle);
      double ypos = TopologicalUtility.getMatrixItemYposByAngle(angle);

      CellObject cellObject =
          graph.insertCell(
              item.getPosition() + "",
              item.getPosition(),
              xpos,
              ypos,
              TopologicalUtility.REGION_WIDTH,
              TopologicalUtility.REGION_HEIGHT,
              angle,
              TopologicalSkinFactory.TOPOLOGICAL_MATRIX,
              item);
      matrixCellMap.put(item, cellObject);
      Connector connector =
          cellObject.getConnector(ConnectorIdentifier.getIdentifier(GridMatrixItem.CONNECTOR));
      if (connector != null) {
        connector.highLightProperty().bind(cellObject.highLightProperty());
      }
    }

    for (GridMatrixItem item : items) {
      for (GridMatrixItem nearItem : item.getRelatedItems().keySet()) {
        // neatItem的postion必须比item的大，CaesarGridLinkSkin要求的
        if (nearItem.getPosition() < item.getPosition()) {
          continue;
        }
        CellObject cell1 = matrixCellMap.get(item);
        CellObject cell2 = matrixCellMap.get(nearItem);
        Connector connector1 =
            cell1.getConnector(ConnectorIdentifier.getIdentifier(GridMatrixItem.CONNECTOR));
        Connector connector2 =
            cell2.getConnector(ConnectorIdentifier.getIdentifier(GridMatrixItem.CONNECTOR));
        if (connector1 != null && connector2 != null) {
          LinkObject link = graph.connect(connector1, connector2, true);
          link.highLightProperty()
              .bind(cell1.getSelectedProperty().or(cell2.getSelectedProperty()));
        }
      }
    }
  }

  @Override
  public void clearGraph() {
    
  }

  @Override
  public McGraph getGraph() {
    return graph;
  }

  @Override
  public String getName() {
    return CaesarI18nCommonResource.getString("systemedit.topological");
  }

  static class OrderInfo {
    public int position;
    public double average;

    public OrderInfo(int position, double average) {
      this.position = position;
      this.average = average;
    }
  }
}
