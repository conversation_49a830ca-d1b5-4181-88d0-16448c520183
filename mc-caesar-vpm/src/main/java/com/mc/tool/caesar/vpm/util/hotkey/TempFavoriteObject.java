package com.mc.tool.caesar.vpm.util.hotkey;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FavoriteObject;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.util.ArrayList;
import java.util.Collection;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import lombok.Getter;

/**
 * .
 */
public class TempFavoriteObject implements FavoriteObject {
  @Getter private final FavoriteObject wrappedObeject;
  private BooleanProperty isChangeProperty = new SimpleBooleanProperty(false);
  private final int[] favorite;

  private final CustomPropertyChangeSupport pcs;
  private final ConfigDataManager configDataManager;

  /** Constructor. */
  public TempFavoriteObject(
      CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, FavoriteObject object) {
    this.wrappedObeject = object;
    this.pcs = pcs;
    this.configDataManager = configDataManager;
    this.favorite = object.getFavoriteArray();
  }

  @Override
  public int getFavorite(int idx) {
    return this.favorite[idx];
  }

  @Override
  public void setFavorite(int idx, int favorite) {
    int oldValue = this.favorite[idx];
    this.favorite[idx] = favorite;
    for (int id = 0; id < CaesarConstants.Cpu.FAVORITE; id++) {
      if (this.favorite[id] != wrappedObeject.getFavorite(id)) {
        isChangeProperty.set(this.favorite[id] != wrappedObeject.getFavorite(id));
        break;
      }
    }
    firePropertyChange(FavoriteObject.PROPERTY_FAVORITE, oldValue, this.favorite[idx], idx);
  }

  @Override
  public CpuData getFavoriteData(int idx) {
    return configDataManager.getCpuData(this.favorite[idx] - 1);
  }

  @Override
  public Collection<CpuData> getFavoriteDatas() {
    Collection<CpuData> cpuDatas = new ArrayList<>();
    for (int i = 0; i < this.favorite.length; i++) {
      CpuData cpuData = getFavoriteData(i);
      if (null != cpuData) {
        cpuDatas.add(cpuData);
      }
    }
    return cpuDatas;
  }

  @Override
  public void setFavoriteData(int idx, CpuData cpuData) {
    setFavorite(idx, null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  @Override
  public int[] getFavoriteArray() {
    return favorite.clone();
  }

  public boolean isChange() {
    return isChangeProperty.get();
  }

  public BooleanProperty isChangeProperty() {
    return isChangeProperty;
  }

  /** 把修改应用到wrapped object. */
  public void commitData() {
    int[] favoriteArray = wrappedObeject.getFavoriteArray();
    for (int idx = 0; idx < favoriteArray.length; idx++) {
      wrappedObeject.setFavorite(idx, favorite[idx]);
    }
  }

  public void commitStatus() {
    isChangeProperty.set(false);
    firePropertyChange(FavoriteObject.PROPERTY_FAVORITE, null, null, null);
  }

  /** . */
  public void reload() {
    int[] favoriteArray = wrappedObeject.getFavoriteArray();
    System.arraycopy(favoriteArray, 0, favorite, 0, favoriteArray.length);
  }

  /** 删除所有的修改. */
  public void cancel() {
    reload();
    isChangeProperty.set(false);
    firePropertyChange(FavoriteObject.PROPERTY_FAVORITE, null, null, null);
  }

  protected final void firePropertyChange(
      String propertyName, Object oldValue, Object newValue, int... indizes) {
    int oid = 0;
    if (null == indizes || 0 == indizes.length) {
      this.pcs.fireIndexedPropertyChange(propertyName, oid, oldValue, newValue);
    } else {
      int[] idx = new int[indizes.length + 1];
      idx[0] = oid;
      System.arraycopy(indizes, 0, idx, 1, indizes.length);

      this.pcs.fireMultiIndexedPropertyChange(propertyName, idx, oldValue, newValue);
    }
  }
}
