package com.mc.tool.caesar.vpm.pages.operation.multiscreen.view;

import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.operation.view.VideoSourceTree;
import com.mc.tool.framework.operation.view.VideoSourceTree.SourceMode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoPreviewFunc;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import java.util.function.UnaryOperator;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import javafx.util.converter.NumberStringConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarMultiScreenFuncPane extends CaesarOperationFuncPane
    implements Initializable, ViewControllable {
  @FXML protected VideoSourceTree sourceTree;

  @FXML private VBox videoPreviewContainer;
  @FXML private TableView<ObjectProperty<VisualEditTerminal>> rxList;
  @FXML private TableColumn<ObjectProperty<VisualEditTerminal>, Integer> rxListIndexColumn;
  @FXML private TableColumn<ObjectProperty<VisualEditTerminal>, String> rxListNameColumn;
  @FXML private TextField multiscreenRow;
  @FXML private TextField multiscreenColumn;
  @FXML private TextField multiScreenName;

  private final CaesarMultiScreenFunc func;
  protected CaesarDeviceController deviceController;
  private CaesarMultiScreenOperationView operationView;

  /**
   * Constructor.
   *
   * @param func func
   * @param model model
   * @param deviceController device controller
   */
  public CaesarMultiScreenFuncPane(
      CaesarMultiScreenFunc func, VisualEditModel model, CaesarDeviceController deviceController) {
    super(model);
    this.func = func;
    this.deviceController = deviceController;

    URL location =
        Thread.currentThread()
            .getContextClassLoader()
            .getResource(
                "com/mc/tool/caesar/vpm/pages/operation/multiscreen/multiscreen_func_panel.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setRoot(this);
    loader.setController(this);
    loader.setResources(CaesarI18nCommonResource.getResourceBundle());
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load crossscreen_func_panel.xml", exception);
    }
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
      operationView.getOperationControllable().setDeviceController(getDeviceController());
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // 监听行列变化时，target数据变化要在监听target的监听器（例如screenList）之前
    func.getFuncData()
        .getColumns()
        .addListener(
            (observable, oldValue, newValue) -> {
              if (!oldValue.equals(newValue)) {
                func.getFuncData()
                    .resetCapacity(func.getFuncData().getRows().get() * newValue.intValue());
              }
            });
    func.getFuncData()
        .getRows()
        .addListener(
            (observable, oldValue, newValue) -> {
              if (!oldValue.equals(newValue)) {
                func.getFuncData()
                    .resetCapacity(func.getFuncData().getColumns().get() * newValue.intValue());
              }
            });
    operationView = new CaesarMultiScreenOperationView(systemEditModel, func);
    operationView.getOperationControllable().setDeviceController(getDeviceController());
    getFunctionViewContent().set(operationView.getView());

    sourceTree.init(
        systemEditModel,
        new SimpleObjectProperty<>(func),
        SourceMode.ALL,
        (terminal) ->
            terminal.isOnline() && terminal.isTx() && !(terminal instanceof CaesarUsbTxTerminal));

    // video preview
    videoPreviewContainer.managedProperty().bind(videoPreviewContainer.visibleProperty());
    videoPreviewContainer
        .visibleProperty()
        .bind(
            Bindings.size(
                    systemEditModel
                        .getAllOnlineFuncs()
                        .filtered((func) -> func instanceof VideoPreviewFunc))
                .greaterThan(0));
    // 名称
    multiScreenName.textProperty().bindBidirectional(func.nameProperty());
    // 布局
    UnaryOperator<Change> columnFilter =
        change -> {
          String newText = change.getControlNewText();
          if (newText.matches("[0-9]+")
              && Integer.parseInt(newText) <= 16
              && Integer.parseInt(newText)
                  >= func.getFuncData().getValidTargetize()
                      / Math.max(1, func.getFuncData().getRows().get())) {
            return change;
          }
          return null;
        };
    UnaryOperator<Change> rowFilter =
        change -> {
          String newText = change.getControlNewText();
          if (newText.matches("[0-9]+")
              && Integer.parseInt(newText) <= 16
              && Integer.parseInt(newText)
                  >= func.getFuncData().getValidTargetize()
                      / Math.max(1, func.getFuncData().getColumns().get())) {
            return change;
          }
          return null;
        };
    multiscreenColumn.setTextFormatter(new TextFormatter<>(columnFilter));
    Bindings.bindBidirectional(
        multiscreenColumn.textProperty(),
        func.getFuncData().getColumns(),
        new NumberStringConverter());
    multiscreenRow.setTextFormatter(new TextFormatter<>(rowFilter));
    Bindings.bindBidirectional(
        multiscreenRow.textProperty(), func.getFuncData().getRows(), new NumberStringConverter());

    // 管控端列表
    rxListIndexColumn.setCellFactory(new IndexCellFactory());
    rxListNameColumn.setCellValueFactory(
        (feat) -> {
          StringProperty nameProperty = new SimpleStringProperty();
          if (feat.getValue() != null) {
            ObjectProperty<VisualEditTerminal> value = feat.getValue();
            if (value.get() != null) {
              nameProperty.bind(value.get().nameProperty());
            }
            value.addListener(
                (obs, oldVal, newVal) -> {
                  if (newVal == null) {
                    nameProperty.unbind();
                    nameProperty.set("");
                  } else {
                    nameProperty.bind(newVal.nameProperty());
                  }
                });
          }
          return nameProperty;
        });
    rxList.setItems(func.getFuncData().getTargets());
    //
    updateVideoSourceList(func);
  }

  @FXML
  protected void onSwitch() {
    if (switchFunctional != null) {
      switchFunctional.onSwitch();
    }
  }

  @FXML
  protected void onSaveScenario() {
    operationView.getOperationControllable().saveScenario();
  }

  @FXML
  protected void onSaveAsScenario() {
    operationView.getOperationControllable().saveAsScenario();
  }

  static class IndexCellFactory
      implements Callback<
          TableColumn<ObjectProperty<VisualEditTerminal>, Integer>,
          TableCell<ObjectProperty<VisualEditTerminal>, Integer>> {

    @Override
    public TableCell<ObjectProperty<VisualEditTerminal>, Integer> call(
        TableColumn<ObjectProperty<VisualEditTerminal>, Integer> param) {
      TableCell<ObjectProperty<VisualEditTerminal>, Integer> cell = new TableCell<>();
      cell.textProperty()
          .bind(
              Bindings.createStringBinding(
                  () -> {
                    if (cell.isEmpty()) {
                      return null;
                    } else {
                      return (cell.getIndex() + 1) + "";
                    }
                  },
                  cell.indexProperty(),
                  cell.emptyProperty()));
      return cell;
    }
  }

  @Override
  public void close() {
    super.close();
    sourceTree.destroy();
    operationView.getOperationControllable().close();
  }
}
