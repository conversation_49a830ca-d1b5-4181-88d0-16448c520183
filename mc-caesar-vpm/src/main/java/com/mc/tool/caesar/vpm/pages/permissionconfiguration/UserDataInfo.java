package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.tool.caesar.api.datamodel.UserGroupData;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class UserDataInfo {

  @Getter @Setter private String username;

  @Getter @Setter private String password;
  @Getter @Setter private String rePassword;

  @Getter @Setter private Integer rights;

  @Getter @Setter private Integer videoWallRights;

  @Getter @Setter private Collection<UserGroupData> userGroupDatas;

  @Getter @Setter private Collection<UserGroupData> unSelectUserGroupDatas;

  @Getter @Setter private Integer mouseSpeed;

  @Getter @Setter private Boolean autoConnect;
}
