package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarAnalogAudioInputSender
    implements Caesar<PERSON>ropertySender<ExtenderAnalogAudioInput> {

  private final CaesarDeviceController controller;
  private final ExtenderData extenderData;
  private final String propertyName;

  /**
   * .
   *
   * @param controller controller
   * @param extenderData 外设
   * @param propertyName 属性名
   */
  public CaesarAnalogAudioInputSender(
      CaesarDeviceController controller, ExtenderData extenderData, String propertyName) {
    this.controller = controller;
    this.extenderData = extenderData;
    this.propertyName = propertyName;
  }

  @Override
  public void sendValue(ExtenderAnalogAudioInput value) {
    controller.execute(
        () -> {
          extenderData.setProperty(propertyName, value);
          try {
            controller.getDataModel().setAnalogAudioInput(extenderData, value);
          } catch (ConfigException | BusyException | DeviceConnectionException exc) {
            log.warn("Fail to set analog audio input for {}", extenderData.getName(), exc);
          }
        });
  }
}
