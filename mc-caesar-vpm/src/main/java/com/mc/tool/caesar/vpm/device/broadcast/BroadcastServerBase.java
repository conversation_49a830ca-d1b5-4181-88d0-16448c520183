package com.mc.tool.caesar.vpm.device.broadcast;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.IOException;
import java.net.BindException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.util.Arrays;
import javafx.scene.control.AlertEx.AlertExType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class BroadcastServerBase implements BroadcastServer {

  @Override
  public void run() {
    setShutdown(false);
    try (DatagramSocket ds = new DatagramSocket(getPort())) {
      ds.setSoTimeout(getTimeout());
      byte[] receiveData = new byte[getBufferSize()];
      while (!isShutDown()) {
        try {
          Arrays.fill(receiveData, (byte) 0);
          DatagramPacket dp = new DatagramPacket(receiveData, receiveData.length);
          ds.receive(dp);
          onReceivedData(receiveData.clone(), dp.getLength(), dp.getAddress());
        } catch (IOException se) {
          log.debug("Fail to read broadcast data!", se);
          setShutdown(true);
        }
      }
    } catch (BindException exception) {
      log.warn("Port {} has been binded!", getPort(), exception);
      PlatformUtility.runInFxThread(
          () -> {
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
            ApplicationBase applicationBase =
                InjectorProvider.getInjector().getInstance(ApplicationBase.class);
            if (applicationBase != null) {
              alert.initOwner(applicationBase.getMainWindow());
            }
            alert.setTitle(CaesarI18nCommonResource.getString("matrixgrid.bind.alert.title.text"));
            alert.setHeaderText(null);
            alert.setContentText(
                CaesarI18nCommonResource.getString("matrixgrid.bind.alert.content.text"));
            alert.showAndWait();
          });
    } catch (SocketException se) {
      log.warn("Boardcast server error!", se);
    }
  }

  protected abstract void onReceivedData(byte[] data, int length, InetAddress address);

  @Override
  public int getTimeout() {
    return 10000;
  }

  @Override
  public int getBufferSize() {
    return 1024;
  }
}
