package com.mc.tool.caesar.vpm.pages.operation.videowall.controller;

import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarScreenData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpMatrix;
import com.mc.tool.caesar.vpm.pages.operation.videowall.vp.CaesarVpVideoWall;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.vp.Vp6Constants;
import com.mc.tool.caesar.vpm.util.vp.Vp6Processor;
import com.mc.tool.caesar.vpm.util.vp.Vp7Constants;
import com.mc.tool.caesar.vpm.util.vp.Vp7InfType;
import com.mc.tool.caesar.vpm.util.vp.Vp7Processor;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker.VideoWallError;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javafx.beans.property.ObjectProperty;
import javafx.util.Pair;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallCheckerFunctions {
  public static void registerFunctions(
      ObjectProperty<CaesarVpMatrix> vpMatrix, VideoWallChecker checker) {
    checker.registerChecker(new Vp6Checker(vpMatrix));
    checker.registerChecker(new Vp7Checker(vpMatrix));
  }

  static class DhmiMapper {
    @Data
    static class DhmiChannel {
      public VisualEditTerminal tx;
      public int index;

      public DhmiChannel(VisualEditTerminal tx, int index) {
        this.tx = tx;
        this.index = index;
      }
    }

    private int pairCount = 0;
    private List<DhmiChannel> channels = new ArrayList<>();

    public void add(VisualEditTerminal tx, int index) {
      DhmiChannel found = null;
      for (DhmiChannel channel : channels) {
        if (channel.tx == tx && channel.index == 1 - index) {
          found = channel;
          break;
        }
      }
      if (found == null) {
        channels.add(new DhmiChannel(tx, index));
      } else {
        pairCount++;
        channels.remove(found);
      }
    }

    public int getNeedCount() {
      return pairCount + channels.size();
    }
  }

  static class Vp6Checker implements Function<VideoWallObject, Collection<VideoWallError>> {

    private ObjectProperty<CaesarVpMatrix> vpMatrix;

    public Vp6Checker(ObjectProperty<CaesarVpMatrix> matrix) {
      vpMatrix = matrix;
    }

    @Override
    public Collection<VideoWallError> apply(VideoWallObject videoWallObject) {
      if (!(videoWallObject instanceof CaesarVideoWallData)
          || !((CaesarVideoWallData) videoWallObject).isVp6()) {
        return Collections.emptyList();
      }
      List<VideoWallError> result = new ArrayList<>();
      CaesarVideoWallData caesarVideoWallData = (CaesarVideoWallData) videoWallObject;
      CaesarVpMatrix matrix = vpMatrix.get();
      if (matrix == null) {
        return result;
      }
      long allCount = caesarVideoWallData.getScreens().stream()
          .filter((item) -> item != null && item.getVpScreen() != null
              && item.getVpScreen().getId() >= 0).count();
      long reduceCount = caesarVideoWallData.getScreens().stream()
          .filter((item) -> item != null && item.getVpScreen() != null
              && item.getVpScreen().getId() >= 0)
          .map(item -> item.getVpScreen().getId())
          .distinct().count();
      if (allCount != reduceCount) {
        String errorMessage =
            CaesarI18nCommonResource.getString("videowall.check.screen_duplicate");
        VideoWallError videoWallError = new VideoWallError();
        videoWallError.setErrorReason(errorMessage);
        result.add(videoWallError);
        return result;
      }
      // 提取所有的screen id，方便之后查找
      Map<Integer, CaesarScreenData> screenIdDataMap = caesarVideoWallData.getScreens().stream()
          .filter((item) -> item != null && item.getVpScreen() != null
              && item.getVpScreen().getId() >= 0)
          .collect(Collectors.toMap((item) -> item.getVpScreen().getId(), (item) -> item));
      // 检查
      Collection<Pair<Integer, Vp6Processor.VideoWallError>> errors = Vp6Processor
          .checkVideoWall(matrix, new CaesarVpVideoWall(caesarVideoWallData));
      // 转换数据
      for (Pair<Integer, Vp6Processor.VideoWallError> error : errors) {
        CaesarScreenData screenData = screenIdDataMap.get(error.getKey());
        if (screenData == null) {
          continue;
        }
        VideoWallError videoWallError = new VideoWallError();
        videoWallError.setScreenData(screenData);

        VpConsoleData parentVpCon = screenData.getVpScreen().getParent();
        int max4kCnt = VpConfigUtility.get4kValidPort(parentVpCon);
        int max2kCnt = VpConfigUtility.calMax2kCount(0, parentVpCon);
        String errorMessage = null;
        if (!parentVpCon.isStatusOnline()) {
          // 无法通讯
          errorMessage = CaesarI18nCommonResource.getString("videowall.check.vpcon_offline");
        } else {
          switch (error.getValue()) {
            case WINDOW_OVERFLOW: {
              String format = CaesarI18nCommonResource
                  .getString("videowall.check.vp6_window_limit");
              errorMessage = MessageFormat.format(format, max2kCnt, max4kCnt);
              break;
            }
            case SCREEN_LAYER_OVERFLOW: {
              String format =
                  CaesarI18nCommonResource.getString("videowall.check.vp6_screen_layer_limit");
              errorMessage =
                  MessageFormat.format(
                      format, Vp6Constants.LAYER_PER_PORT);
              break;
            }
            case DEVICE_LAYER_OVERFLOW: {
              String format =
                  CaesarI18nCommonResource.getString("videowall.check.vp6_layer_limit");
              errorMessage =
                  MessageFormat.format(format, Vp6Constants.MAX_TOTAL_LAYER);
              break;
            }
            default: {
              break;
            }
          }
        }
        if (errorMessage != null) {
          videoWallError.setErrorReason(errorMessage);
          result.add(videoWallError);
        }
      }

      return result;
    }
  }

  static class Vp7Checker implements Function<VideoWallObject, Collection<VideoWallError>> {
    private ObjectProperty<CaesarVpMatrix> vpMatrix;

    public Vp7Checker(ObjectProperty<CaesarVpMatrix> matrix) {
      vpMatrix = matrix;
    }

    @Override
    public Collection<VideoWallError> apply(VideoWallObject videoWallObject) {
      if (!(videoWallObject instanceof CaesarVideoWallData)
          || !((CaesarVideoWallData) videoWallObject).isVp7()) {
        return Collections.emptyList();
      }
      List<VideoWallError> result = new ArrayList<>();
      CaesarVideoWallData caesarVideoWallData = (CaesarVideoWallData) videoWallObject;
      CaesarVpMatrix matrix = vpMatrix.get();
      if (matrix == null) {
        return result;
      }
      // 检查
      Collection<Pair<Integer, Vp7Processor.VideoWallError>> errors = Vp7Processor
          .checkVideoWall(matrix, new CaesarVpVideoWall(caesarVideoWallData));
      // 转换数据
      for (Pair<Integer, Vp7Processor.VideoWallError> error : errors) {
        CaesarScreenData screenData = caesarVideoWallData.getScreens().stream()
            .filter((item) -> item != null && item.getVpScreen() != null
                && item.getVpScreen().getId() == error.getKey())
            .findFirst().orElse(null);
        if (screenData == null) {
          continue;
        }
        int vpconId = Vp7Processor.getVp7Id(screenData.getVpScreen().getId());
        Vp7InfType infType = matrix.getVp7InfType(vpconId);
        VideoWallError videoWallError = new VideoWallError();
        videoWallError.setScreenData(screenData);
        String errorMessage = null;
        switch (error.getValue()) {
          case WINDOW_OVERFLOW: {
            String format = CaesarI18nCommonResource.getString("videowall.check.vp7_window_limit");
            errorMessage = MessageFormat.format(format, Vp7Constants.MAX_VIDEO_INPUT, Vp7Constants.MAX_VIDEO_INPUT / 2);
            break;
          }
          case SCREEN_LAYER_OVERFLOW: {
            String format = CaesarI18nCommonResource.getString("videowall.check.vp7_screen_layer_limit");
            errorMessage =
                MessageFormat.format(format, Vp7Processor.getMaxLayerCountPerScreen(infType), infType.getOutputCount());
            break;
          }
          case INVALID_SCREEN: {
            String format = CaesarI18nCommonResource.getString("videowall.check.vp7_invalid_screen");
            errorMessage = MessageFormat.format(format, infType.getOutputCount());
            break;
          }
          default: {
            break;
          }
        }
        if (errorMessage != null) {
          videoWallError.setErrorReason(errorMessage);
          result.add(videoWallError);
        }
      }
      return result;
    }
  }
}
