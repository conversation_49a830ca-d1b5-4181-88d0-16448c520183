package com.mc.tool.caesar.vpm.userright;

import com.mc.tool.caesar.vpm.pages.hostconfiguration.CaesarHostConfigurationPage;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CaesarHotkeyConfigurationPage;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;

/**
 * .
 */
public class CaesarVp6UserRight extends CaesarAdminUserRight {

  @Override
  public boolean isPageVisible(String pageName) {
    if (pageName.equals(CaesarHostConfigurationPage.NAME)) {
      return false;
    }
    if (pageName.equals(CaesarPermissionConfigurationPage.NAME)) {
      return false;
    }
    if (pageName.equals(CaesarHotkeyConfigurationPage.NAME)) {
      return false;
    }
    return super.isPageVisible(pageName);
  }

  @Override
  public boolean isOsdConfigVisible() {
    return false;
  }

  @Override
  public boolean isCrossScreenCreateDeletable() {
    return false;
  }
}
