package com.mc.tool.caesar.vpm.pages.activateconfig;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConfigDataModel;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ConfigData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.activateconfig.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredList;
import javafx.concurrent.Task;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ActivateConfigPageController implements Initializable, ViewControllable {

  @FXML private Label configInfo;
  @FXML private Label nameLabel;
  @FXML private Label nameText;
  @FXML private Label infoLabel;
  @FXML private Label infoText;
  @FXML private TableView<ActivateConfigMetaCdm> tableView;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> indexCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> filenameCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> nameCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> infoCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> addressCol;
  @FXML private TableColumn<ActivateConfigMetaCdm, String> versionCol;
  @FXML private Button activateButton;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private CaesarDeviceController deviceController = null;

  private BooleanProperty activate = new SimpleBooleanProperty(true);

  ObservableList<ActivateConfigMetaCdm> activateConfigList = FXCollections.observableArrayList();

  private final String invalid = NbBundle.getMessage("ActivateConfigPageView.invalid.text");

  private final int bufferLen = 1024;

  private String currentName;
  private String currentInfo;
  private String currentAddress;
  private final AtomicBoolean hasDifferentIp = new AtomicBoolean(false);
  private final AtomicBoolean stoppedWaiting = new AtomicBoolean(false);
  private int id;

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    this.deviceController.execute(this::updateList);
    initCurrent();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    configInfo.setText(NbBundle.getMessage("ConfigInfo.text"));

    nameLabel.setText(NbBundle.getMessage("ActivateName.label.text"));
    infoLabel.setText(NbBundle.getMessage("ActivateInfo.label.text"));

    indexCol.setText(null);
    filenameCol.setText(NbBundle.getMessage("Table.filename.text"));
    nameCol.setText(NbBundle.getMessage("Table.name.text"));
    infoCol.setText(NbBundle.getMessage("Table.info.text"));
    addressCol.setText(NbBundle.getMessage("Table.address.text"));
    versionCol.setText(NbBundle.getMessage("Table.version.text"));

    indexCol.setSortable(false);
    filenameCol.setSortable(false);
    nameCol.setSortable(false);
    infoCol.setSortable(false);
    addressCol.setSortable(false);
    versionCol.setSortable(false);

    tableView
        .widthProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  filenameCol.setPrefWidth((newValue.doubleValue() - indexCol.getPrefWidth()) / 5);
                  nameCol.setPrefWidth((newValue.doubleValue() - indexCol.getPrefWidth()) / 5);
                  infoCol.setPrefWidth((newValue.doubleValue() - indexCol.getPrefWidth()) / 5);
                  addressCol.setPrefWidth((newValue.doubleValue() - indexCol.getPrefWidth()) / 5);
                  versionCol.setPrefWidth((newValue.doubleValue() - indexCol.getPrefWidth()) / 5);
                }));

    indexCol.setCellFactory(col -> new IndexCell());
    filenameCol.setCellValueFactory(item -> item.getValue().getFilenameProperty());
    nameCol.setCellValueFactory(item -> item.getValue().getNameProperty());
    infoCol.setCellValueFactory(item -> item.getValue().getInfoProperty());
    addressCol.setCellValueFactory(item -> item.getValue().getAddressProperty());
    versionCol.setCellValueFactory(item -> item.getValue().getVersionProperty());

    tableView
        .getSelectionModel()
        .selectedItemProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (oldValue != null) {
                    oldValue.setIsSelected(false);
                  }
                  if (newValue != null) {
                    newValue.setIsSelected(true);
                    activate.set(false);
                  }
                }));

    activateButton.setText(NbBundle.getMessage("Button.activate.text"));

    activateButton.disableProperty().bindBidirectional(activate);

    tableView.setItems(activateConfigList);
    initActivateButton();
  }

  /** 重新加载. */
  @Override
  public void reload() {
    try {
      deviceController.submit(this::updateList).get();
    } catch (ExecutionException | InterruptedException exception) {
      log.warn("Fail to reload.", exception);
    }
    Platform.runLater(this::initCurrent);
  }

  private void initActivateButton() {

    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Task<Void> task =
        new Task<Void>() {
          @Override
          protected Void call() throws Exception {
            if (!hasDifferentIp.get()) {
              this.updateMessage("正在重新连接...");
              // 等待断连
              int waitStopCount = 0;
              while (deviceController.getDataModel().isConnected()
                  && !stoppedWaiting.get()
                  && waitStopCount < 60) {
                try {
                  Thread.sleep(1000L);
                } catch (InterruptedException ex) {
                  log.error("thread sleep error", ex);
                }
                waitStopCount++;
              }
              log.info("disconnected");
              int loopCounter = 0;
              // 等待重新连接
              while (!deviceController.getDataModel().isConnected()
                  && loopCounter < 600
                  && !stoppedWaiting.get()) {
                try {
                  Thread.sleep(1000L);
                } catch (InterruptedException ex) {
                  log.error("thread sleep error", ex);
                }
                loopCounter++;
                log.info("waiting for reconnect ...");
              }
              log.info("reconnected");
              if (!stoppedWaiting.get() && loopCounter == 600) {
                log.warn("connect failed!");
                throw new Exception("connect failed");
              }
            } else {
              Platform.runLater(
                  () -> {
                    UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
                    if (app != null) {
                      alert.initOwner(app.getMainWindow());
                    }
                    alert.setTitle("Messager");
                    alert.setHeaderText(null);
                    alert.setContentText("ip已改，需要配置连接");
                    alert.showAndWait();
                  });
            }
            return null;
          }
        };

    activateButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {

              @Override
              public void handle(ActionEvent event) {
                UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
                if (app != null) {
                  alert.initOwner(app.getMainWindow());
                }
                alert.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);
                alert.setTitle(NbBundle.getMessage("alert.activate_confirmation.title"));
                alert.setHeaderText(null);
                alert.setContentText(
                    NbBundle.getMessage("alert.activate_confirmation.content_text"));
                alert.showAndWait();
                if (alert.getResult().equals(ButtonType.CANCEL)) {
                  return;
                }
                FilteredList<ActivateConfigMetaCdm> item =
                    activateConfigList.filtered(ActivateConfigMetaCdm::getIsSelected);
                if (!item.isEmpty()) {
                  id = item.get(0).getFileType();
                  log.info("activate {}.", id);
                  String ip = item.get(0).getAddress();
                  hasDifferentIp.getAndSet(!ip.equals(currentAddress));
                  if (hasDifferentIp.get()) {
                    log.info(
                        String.format("ip different, current(%s) new(%s)", currentAddress, ip));
                  }

                  deviceController.execute(
                      () -> {
                        try {
                          deviceController.getDataModel().activateConfig(id - 1);
                        } catch (ConfigException | BusyException ex) {
                          log.warn("Activate error!");
                        }
                      });
                  app.getTaskManager().addForegroundTask(task);
                }
              }
            }));
  }

  private void initCurrent() {
    if (this.deviceController != null) {
      this.currentName =
          this.deviceController
              .getDataModel()
              .getConfigData()
              .getSystemConfigData()
              .getSystemData()
              .getName();
      this.currentInfo =
          this.deviceController
              .getDataModel()
              .getConfigData()
              .getSystemConfigData()
              .getSystemData()
              .getInfo();
      this.currentAddress =
          IpUtil.getAddressString(
              this.deviceController
                  .getDataModel()
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataCurrent1()
                  .getAddress());
      nameText.setText(this.currentName);
      infoText.setText(this.currentInfo);
    }
  }

  /** 刷新列表，非ui线程调用. */
  private synchronized void updateList() {
    Collection<ActivateConfigMetaCdm> result = listImpl();
    PlatformUtility.runInFxThread(() -> activateConfigList.setAll(result));
  }

  /** 刷新列表，非ui线程调用. */
  private synchronized Collection<ActivateConfigMetaCdm> listImpl() {
    int type;
    InputStream is;
    List<ActivateConfigMetaCdm> result = new ArrayList<>();
    if (deviceController.isDemo()) {
      return result;
    }
    for (String configName : CaesarConstants.CONFIG_NAMES) {
      // Fix bug #1374.
      CaesarConfigDataModel activeConfigModel = new CaesarConfigDataModel();
      type = getFileType(configName);
      try {
        byte[] bytes = readFile(type);
        is = new ByteArrayInputStream(bytes);

        boolean error = false;
        int tryCounter = 0;
        if (!error) {
          do {
            try {
              tryCounter++;
              error = false;
              activeConfigModel.initDefaults();
              activeConfigModel.readData(is, ConfigData.IoMode.Available);
            } catch (ConfigException ex) {
              log.warn("Failed to read activeConfig data!");
              error = true;
            }
          } while (error && tryCounter < 5);
        }
        if (error) {
          activateConfigList.add(
              new ActivateConfigMetaCdm(
                  this.invalid, this.invalid, "s0.0.0.0".substring(1), "", "", 0));
        } else {
          // 保存配置的基本信息
          String filename = configName + CaesarConstants.CFG_FILE_EXTENSION;
          String name =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getName();
          String info =
              activeConfigModel.getConfigData().getSystemConfigData().getSystemData().getInfo();
          byte[] address =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .getAddress();
          Boolean isdhcp =
              activeConfigModel
                  .getConfigData()
                  .getSystemConfigData()
                  .getNetworkDataPreset1()
                  .isDhcp();
          int version = activeConfigModel.getConfigData().getConfigMetaData().getVersion();
          result.add(
              new ActivateConfigMetaCdm(
                  filename,
                  name,
                  info,
                  dhcpRow(isdhcp, address),
                  Version.getVersion(version).getName(),
                  type));
        }

      } catch (ConfigException | BusyException | IOException ex) {
        log.warn("Failed to get config file for {}!", type, ex);
      }
    }
    return result;
  }

  private String dhcpRow(Boolean isDhcp, byte[] address) {
    if (isDhcp) {
      return "DHCP";
    }
    return IpUtil.getAddressString(address);
  }

  private byte[] readFile(int fileType)
      throws DeviceConnectionException, ConfigException, BusyException, IOException {
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    this.deviceController.getDataModel().getController().setSystemFileOpen(fileType);
    byte[] data;
    do {
      data =
          this.deviceController.getDataModel().getController().readSystemFile(fileType, bufferLen);
      if (data != null) {
        bos.write(data);
      }
    } while (data != null && data.length == bufferLen);
    this.deviceController.getDataModel().getController().setSystemFileClose(fileType);
    return bos.toByteArray();
  }

  private int getFileType(String configname) {
    int index = 1;
    for (String config : CaesarConstants.CONFIG_NAMES) {
      if (config.equals(configname)) {
        return index;
      }
      index++;
    }
    return 0;
  }

  private static class IndexCell extends TableCell<ActivateConfigMetaCdm, String> {
    @Override
    public void updateItem(String item, boolean empty) {
      super.updateItem(item, empty);
      this.setText(null);
      this.setGraphic(null);

      if (!empty) {
        int rowIndex = this.getIndex() + 1;
        this.setText(String.valueOf(rowIndex));
      }
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }
}
