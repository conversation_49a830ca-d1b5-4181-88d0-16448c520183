package com.mc.tool.caesar.vpm.pages.operation.videowall.menu;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoData;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.menu.VideoWallMenuBase;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import javafx.collections.ObservableList;
import javax.annotation.Nullable;

/**
 * MenuAudioRx.
 */
public class MenuAudioRx extends VideoWallMenuBase {
  private final ConsoleData con;

  /**
   * MenuAudioRx.
   */
  public MenuAudioRx(VideoWallControllable controllable, @Nullable ConsoleData con) {
    super(controllable);
    if (con != null) {
      setText("[" + con.getId() + "]" + con.getName());
    } else {
      setText(CaesarI18nCommonResource.getString("menu_disconnect"));
    }
    this.con = con;
    setDisable(controllable.getSelectedVideos().size() != 1);
  }

  @Override
  protected void onAction() {
    ObservableList<? extends VideoObject> videos =
        controllable.getVideoWallFunction().getVideoWallObject().getVideos();
    List<CaesarVideoData> effectiveVideos = new ArrayList<>();
    for (VideoObject video : videos) {
      if (video instanceof CaesarVideoData && ((CaesarVideoData) video).getAudioRx().get() > 0) {
        effectiveVideos.add((CaesarVideoData) video);
      }
    }
    if (!effectiveVideos.isEmpty()) {
      effectiveVideos.sort(Comparator.comparingInt(cv -> cv.getAudioSeq().get()));
      for (int i = 0; i < effectiveVideos.size(); i++) {
        effectiveVideos.get(i).getAudioSeq().set(i + 1);
      }
    }
    CaesarVideoData videoData = (CaesarVideoData) controllable.getSelectedVideos().iterator().next();
    if (con == null) {
      videoData.getAudioRx().set(0);
      videoData.getAudioSeq().set(0);
    } else {
      videoData.getAudioRx().set(con.getId());
      videoData.getAudioSeq().set(effectiveVideos.size() + 1);
    }
  }

  @Override
  protected String getMenuText() {
    return "";
  }
}
