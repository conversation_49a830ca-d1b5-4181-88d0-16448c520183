package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarVideoWallFuncManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarIrregularCrossScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuDelete;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.ArrayList;
import java.util.List;
import javafx.scene.control.AlertEx;

/**
 * .
 */
public class MenuDelete4Caesar extends MenuDelete {
  private final CaesarDeviceController deviceController;
  private final CaesarVideoWallFuncManager videoWallFuncManager;

  /** Constructor. */
  public MenuDelete4Caesar(
      SystemEditControllable controllable,
      CaesarDeviceController deviceController,
      CaesarVideoWallFuncManager videoWallFuncManager) {
    super(controllable);
    this.deviceController = deviceController;
    this.videoWallFuncManager = videoWallFuncManager;
  }

  @Override
  protected void onAction() {
    for (VisualEditNode node : controllable.getSelectedNodes()) {
      if (node instanceof CaesarCrossScreenFunc) {
        CaesarCrossScreenFunc func = (CaesarCrossScreenFunc) node;
        deviceController.execute(
            () -> deviceController.deleteMultiScreenData(func.getDeviceData()));
      } else if (node instanceof CaesarVideoWallFunc) {
        int index = ((CaesarVideoWallFunc) node).getVideoWallIndex();
        deviceController.deleteVideoWall(index);
        // fix bug #1454. 删除视频墙时删除连接.
        List<VpConsoleData> vpConsoleDataList = new ArrayList<>();
        for (VisualEditNode child : node.getChildren()) {
          if (child instanceof VpGroup) {
            vpConsoleDataList.add(((VpGroup) child).getVpConsoleData());
          }
        }
        deviceController.resetVpCons(vpConsoleDataList);

        videoWallFuncManager.setVideoWallFunc(index, null);
      } else if (node instanceof CaesarUsbRxTerminal) {
        deviceController.execute(
            () ->
                deviceController.deleteUsbExtender(((CaesarUsbRxTerminal) node).getExtenderData()));
      } else if (node instanceof CaesarUsbTxTerminal) {
        deviceController.execute(
            () ->
                deviceController.deleteUsbExtender(((CaesarUsbTxTerminal) node).getExtenderData()));
      } else if (node instanceof CaesarIrregularCrossScreenFunc) {
        CaesarIrregularCrossScreenFunc func = (CaesarIrregularCrossScreenFunc) node;
        deviceController.execute(
            () -> deviceController.deleteMultiScreenData(func.getDeviceData()));
      } else if (node instanceof CpuGroup) {
        deviceController.execute(
            () -> deviceController.deleteTxRxGroup(((CpuGroup) node).getTxRxGroupData()));
      } else if (node instanceof AudioGroup) {
        // 当音频组被视频墙占用时，不能删除
        int oid = ((AudioGroup) node).getTxRxGroupData().getOid();
        for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
          CaesarVideoWallFunc videoWallFunc = videoWallFuncManager.getVideoWallFunc(i);
          if (videoWallFunc != null) {
            int audioIndex = videoWallFunc.getCaesarVideoWall().getAudioGroupIndex().get();
            if (audioIndex == oid + 1) {
              ApplicationBase applicationBase =
                  InjectorProvider.getInjector().getInstance(ApplicationBase.class);
              ViewUtility.showAlert(
                  applicationBase.getMainWindow(),
                  Bundle.NbBundle.getMessage("menu.audio_delete.alert"),
                  Bundle.NbBundle.getMessage("warning"),
                  AlertEx.AlertExType.WARNING);
              return;
            }
          }
        }
        deviceController.execute(
            () -> deviceController.deleteTxRxGroup(((AudioGroup) node).getTxRxGroupData()));
      }
    }
    super.onAction();
  }
}
