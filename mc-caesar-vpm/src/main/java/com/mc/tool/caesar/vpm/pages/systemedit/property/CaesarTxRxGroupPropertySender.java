package com.mc.tool.caesar.vpm.pages.systemedit.property;

import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarTxRxGroupPropertySender<T> implements CaesarPropertySender<T> {

  private final CaesarDeviceController controller;
  private final String propertyName;
  private final TxRxGroupData txRxGroupData;

  /** Constructor. */
  public CaesarTxRxGroupPropertySender(
      CaesarDeviceController controller, String propertyName, TxRxGroupData txRxGroupData) {
    this.controller = controller;
    this.propertyName = propertyName;
    this.txRxGroupData = txRxGroupData;
  }

  @Override
  public void sendValue(T value) {
    controller.execute(
        () -> {
          try {
            txRxGroupData.setProperty(propertyName, value);
            controller.getDataModel().sendTxRxGroupData(Collections.singletonList(txRxGroupData));
          } catch (DeviceConnectionException | BusyException exception) {
            log.error("Fail to send value!", exception);
          }
        });
  }
}
