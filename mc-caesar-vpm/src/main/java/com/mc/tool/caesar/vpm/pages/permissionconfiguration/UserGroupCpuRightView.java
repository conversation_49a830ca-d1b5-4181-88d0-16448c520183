package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.AccessControlObject;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.cpuright.CpuRightBaseView;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.Button;
import javafx.scene.control.SelectionMode;
import javafx.scene.layout.HBox;
import javafx.stage.Modality;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class UserGroupCpuRightView extends CpuRightBaseView<UserGroupData> {
  @FXML protected HBox btnBox;
  protected Button newUserGroupBtn;
  protected Button editUserGroupBtn;
  protected Button deleteUserGroupBtn;

  /** Constructor. */
  public UserGroupCpuRightView() {
    ResourceBundle bundle = ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common");
    sourceListTitle.set(bundle.getString("user_group_right.user_group_list"));
    accessBlockTitle.set(bundle.getString("user_group_right.user_group_cpu_right_management"));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);

    sourceList.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);

    sourceIdColumn.setCellValueFactory((feat) -> feat.getValue().getIdProperty());
    sourceNameColumn.setCellValueFactory((feat) -> feat.getValue().getNameProperty());

    newUserGroupBtn = new Button(CaesarI18nCommonResource.getString("user_group_right.new_button"));
    newUserGroupBtn.getStyleClass().add("common-button");
    newUserGroupBtn.setOnAction((event) -> onNew());
    newUserGroupBtn.disableProperty().bind(updatingProperty);
    btnBox.getChildren().add(0, newUserGroupBtn);
    editUserGroupBtn =
        new Button(CaesarI18nCommonResource.getString("user_group_right.edit_button"));
    editUserGroupBtn.getStyleClass().add("common-button");
    editUserGroupBtn.disableProperty().bind(selectedItem.isNull().or(updatingProperty));
    editUserGroupBtn.setOnAction((event) -> onEdit());
    btnBox.getChildren().add(1, editUserGroupBtn);
    deleteUserGroupBtn =
        new Button(CaesarI18nCommonResource.getString("user_group_right.delete_button"));
    deleteUserGroupBtn.getStyleClass().add("common-button");
    deleteUserGroupBtn.disableProperty().bind(selectedItem.isNull().or(updatingProperty));
    deleteUserGroupBtn.setOnAction((event) -> onDelete());
    btnBox.getChildren().add(2, deleteUserGroupBtn);
  }

  private void onNew() {
    UserGroupData userGroupData =
        this.deviceController.getDataModel().getConfigDataManager().getFreeUserGroup();
    if (userGroupData != null) {

      EditUserGroupDataDialog editUserGroupDataDialog =
          new EditUserGroupDataDialog(userGroupData, false);
      editUserGroupDataDialog.setDeviceController(deviceController);
      editUserGroupDataDialog.initOwner(sourceList.getScene().getWindow());
      editUserGroupDataDialog.initModality(Modality.APPLICATION_MODAL);
      editUserGroupDataDialog.showAndWait();
      String nameInfo = editUserGroupDataDialog.getResult();
      if (nameInfo != null) {
        updatingProperty.set(true);
        deviceController
            .submitAsync(
                () -> {
                  userGroupData.setInitMode(true);
                  if (this.deviceController
                      .getDataModel()
                      .getConfigData()
                      .getSystemConfigData()
                      .getAccessData()
                      .isUserAccess()) {
                    for (CpuData cpuData :
                        this.deviceController
                            .getDataModel()
                            .getConfigDataManager()
                            .getActiveCpus()) {
                      userGroupData.setNoAccess(cpuData, false);
                      userGroupData.setVideoAccess(cpuData, false);
                    }
                  } else {
                    userGroupData.setNoAccessCpus(
                        this.deviceController.getDataModel().getConfigDataManager().getCpus());
                  }
                  userGroupData.setStatusNew(true);
                  userGroupData.setInitMode(false);

                  userGroupData.setInitMode(true);
                  if (userGroupData.isStatusNew()) {
                    userGroupData.setStatusActive(true);
                    userGroupData.setStatusNew(false);
                  }
                  userGroupData.setName(nameInfo);
                  userGroupData.setInitMode(false);
                  try {
                    this.deviceController
                        .getDataModel()
                        .sendUserGroupData(Collections.singletonList(userGroupData));
                  } catch (DeviceConnectionException | BusyException ex) {
                    log.warn("Can not send user group data!");
                  }
                })
            .whenComplete(
                (result, throwable) -> {
                  if (throwable != null) {
                    log.warn("Can not send user group data!", throwable);
                  }
                  PlatformUtility.runInFxThread(
                      () -> {
                        updatingProperty.set(false);
                        updateTableList();
                      });
                });
      }
    }
  }

  private void onEdit() {
    UserGroupData data = getTableSelectionModel().getSelectedItem();
    EditUserGroupDataDialog editUserGroupDataDialog = new EditUserGroupDataDialog(data, true);
    editUserGroupDataDialog.setDeviceController(deviceController);
    if (sourceList != null && sourceList.getScene() != null) {
      editUserGroupDataDialog.initOwner(sourceList.getScene().getWindow());
    }
    editUserGroupDataDialog.initModality(Modality.APPLICATION_MODAL);
    editUserGroupDataDialog.showAndWait();
    String nameInfo = editUserGroupDataDialog.getResult();
    if (nameInfo != null) {
      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                data.setInitMode(true);
                if (data.isStatusNew()) {
                  data.setStatusActive(true);
                  data.setStatusNew(false);
                }
                data.setName(nameInfo);
                data.setInitMode(false);
                try {
                  this.deviceController
                      .getDataModel()
                      .sendUserGroupData(Collections.singletonList(data));
                } catch (DeviceConnectionException | BusyException ex) {
                  log.warn("Can not send user group data!");
                }
              })
          .whenComplete(
              (result, throwable) -> {
                if (throwable != null) {
                  log.warn("Can not send user group data!", throwable);
                }
                PlatformUtility.runInFxThread(
                    () -> {
                      updatingProperty.set(false);
                      updateTableList();
                    });
              });
    }
  }

  private void onDelete() {
    List<UserGroupData> seletedData = new ArrayList<>(getTableSelectionModel().getSelectedItems());
    deviceController
        .submitAsync(
            () -> {
              List<UserGroupData> userGroupDatas = new ArrayList<>();
              for (UserGroupData data : seletedData) {
                for (UserData userData :
                    this.deviceController.getDataModel().getConfigData().getUserDatas()) {
                  if (userData.isUserGroup(data)) {
                    userData.setUserGroup(data, false);
                  }
                }
                data.delete();
                userGroupDatas.add(data);
              }

              List<FunctionKeyData> functionKeyChangeList = new ArrayList<>();
              for (FunctionKeyData functionKeyData :
                  this.deviceController.getDataModel().getConfigData().getFunctionKeyDatas()) {
                if (functionKeyData.equals(AbstractData.THRESHOLD_UI_LOCAL_CHANGES)) {
                  functionKeyChangeList.add(functionKeyData);
                }
              }
              try {
                if (!functionKeyChangeList.isEmpty()) {
                  this.deviceController.getDataModel().sendFunctionKeyData(functionKeyChangeList);
                }
                this.deviceController.getDataModel().sendUserGroupData(userGroupDatas);
              } catch (DeviceConnectionException | BusyException ex) {
                log.warn("Can not send user group data!");
              }
            })
        .whenComplete((result, throwable) -> {
          if (throwable != null) {
            log.warn("Can not send user group data!", throwable);
          }
          PlatformUtility.runInFxThread(this::updateTableList);
        });
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);

    if (this.deviceController != null) {
      sourceRawList.setAll(
          this.deviceController.getDataModel().getConfigDataManager().getActiveUserGroups());

      CaesarSwitchDataModel model = this.deviceController.getDataModel();
      model.addPropertyChangeListener(
          UserGroupData.PROPERTY_STATUS,
          weakAdapter.wrap(
              (PropertyChangeListener)
                  evt ->
                      Platform.runLater(
                          () ->
                              sourceRawList.setAll(
                                  this.deviceController.getDataModel()
                                      .getConfigDataManager().getActiveUserGroups()))));
    }
  }

  @Override
  protected void onSendNewAccess(AccessControlObject item) {
    if (item instanceof UserGroupData) {
      UserGroupData data = (UserGroupData) item;
      try {
        deviceController.getDataModel().sendUserGroupData(Collections.singletonList(data));
      } catch (DeviceConnectionException | BusyException exception) {
        log.warn("Fail to send user group data!", exception);
      }
    }
  }

  @Override
  public void updateEditableStatus() {
    if (deviceController == null) {
      return;
    }
    editableProperty.set(deviceController.getCaesarUserRight().isUserGroupRightEditable());
  }

  private void updateTableList() {
    sourceRawList.setAll(
        this.deviceController.getDataModel().getConfigDataManager().getActiveUserGroups());
  }
}
