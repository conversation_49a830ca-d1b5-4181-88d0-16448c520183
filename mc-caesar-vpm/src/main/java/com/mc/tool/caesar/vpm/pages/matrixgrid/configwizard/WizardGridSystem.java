package com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard;

import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.CategoryTableCell;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.MatrixData4HostConfig;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.ValidTableCell;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.ValidateStatus;
import com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.MatrixGridConfigUtility.ValidateTableCell;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.ResourceBundle;
import java.util.Set;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.cell.ComboBoxTableCell;
import javafx.scene.control.cell.TextFieldTableCell;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class WizardGridSystem extends UndecoratedWizardPane implements Initializable {

  @FXML private TableView<MatrixData4HostConfig> tableView;
  @FXML private TableColumn<MatrixData4HostConfig, String> categoryCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> nameCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> ipCol;
  @FXML private TableColumn<MatrixData4HostConfig, Number> portCol;
  @FXML private TableColumn<MatrixData4HostConfig, String> validateCol;
  @FXML private TableColumn<MatrixData4HostConfig, ValidateStatus> validCol;

  private ObservableList<MatrixData4HostConfig> items = FXCollections.observableArrayList();

  private final BooleanProperty pass = new SimpleBooleanProperty();

  private final ChangeListener<ValidateStatus> statusChangeListener;

  private final CaesarDeviceController deviceController;

  /** . */
  public WizardGridSystem(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(
        classLoader.getResource(
            "com/mc/tool/caesar/vpm/pages/matrixgrid/configwizard/gridsystem_view.fxml"));
    loader.setResources(
        ResourceBundle.getBundle("com.mc.tool.caesar.vpm.pages.matrixgrid.configwizard.Bundle"));
    loader.setController(this);
    try {
      Node root = loader.load();
      setContent(root);
    } catch (IOException exception) {
      log.warn("Fail to load gridsystem_view.fxml!", exception);
    }

    statusChangeListener = (obs, oldVal, newVal) -> checkStatus();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    categoryCol.setCellFactory((col) -> new CategoryTableCell());
    categoryCol.setPrefWidth(40);

    nameCol.setCellValueFactory((cell) -> cell.getValue().name);
    nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
    nameCol.setPrefWidth(Integer.MAX_VALUE);

    ipCol.setCellValueFactory((cell) -> cell.getValue().ip);
    ipCol.setPrefWidth(Integer.MAX_VALUE);

    portCol.setCellValueFactory((cell) -> cell.getValue().port);
    portCol.setCellFactory(ComboBoxTableCell.forTableColumn(MatrixGridConfigUtility.PORT_LIST));
    portCol.setPrefWidth(Integer.MAX_VALUE);

    validateCol.setCellFactory((view) -> new ValidateTableCell(this::validate));
    validateCol.setPrefWidth(Integer.MAX_VALUE);

    validCol.setCellValueFactory((cell) -> cell.getValue().gridSystemValid);
    validCol.setCellFactory((view) -> new ValidTableCell());
    validCol.setPrefWidth(Integer.MAX_VALUE);

    tableView.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);
    tableView.setItems(items);
    tableView.setEditable(true);
  }

  private void checkStatus() {
    boolean ok = true;
    for (MatrixData4HostConfig item : items) {
      ok &= item.gridSystemValid.get() == ValidateStatus.VALID;
    }
    pass.set(ok);
  }

  @Override
  public void onEnteringPage(UndecoratedWizard wizard) {
    super.onEnteringPage(wizard);
    items.setAll(MatrixGridConfigUtility.getMatrixGridConfigFromWizard(wizard));

    wizard.invalidProperty().bind(pass.not());

    for (MatrixData4HostConfig item : items) {
      item.gridSystemValid.addListener(statusChangeListener);
    }
    checkStatus();
  }

  @Override
  public void onExitingPage(UndecoratedWizard wizard) {
    super.onExitingPage(wizard);
    for (MatrixData4HostConfig item : items) {
      item.gridSystemValid.removeListener(statusChangeListener);
    }

    wizard.getSettings().put(MatrixGridConfigUtility.KEY_HOST_CONFIG, new ArrayList<>(items));
  }

  protected void validate(ValidateTableCell cell, MatrixData4HostConfig config) {
    CaesarNamePredicate predicate = new CaesarNamePredicate();
    Set<String> names = new HashSet<>();

    int portCount = 0;
    for (MatrixData4HostConfig item : items) {
      if (item == config || item.gridSystemValid.get() != ValidateStatus.VALID) {
        continue;
      }
      portCount += item.port.get();
      names.add(item.name.get());
    }
    if (names.contains(config.name.get())) {
      config.gridSystemValid.set(ValidateStatus.NAME_DULPLICATE);
    } else if (!Arrays.asList(MatrixGridConfigUtility.PORT_LIST).contains(config.port.get())) {
      config.gridSystemValid.set(ValidateStatus.PORT_ERROR);
    } else if (portCount + config.port.get()
        > deviceController.getDataModel().getConfigMetaData().getUtilVersion().getMaxExtNumber()) {
      config.gridSystemValid.set(ValidateStatus.PORT_TOO_MUCH);
    } else if (config.name.get().isEmpty()) {
      config.gridSystemValid.set(ValidateStatus.NAME_EMPTY);
    } else if (!predicate.test(config.name.get())) {
      config.gridSystemValid.set(ValidateStatus.NAME_TOO_LONG);
    } else {
      config.gridSystemValid.set(ValidateStatus.VALID);
    }
  }
}
