package com.mc.tool.caesar.vpm.pages.splicingscreen;

import com.mc.common.beans.SimpleBooleanBinding;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.kaito.model.CreateVideoWallGroupInfo;
import com.mc.tool.framework.utility.UndecoratedHeavyWeightDialog;
import java.util.ArrayList;
import java.util.List;
import java.util.function.UnaryOperator;
import javafx.application.Platform;
import javafx.beans.binding.BooleanBinding;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ChoiceBox;
import javafx.scene.control.Control;
import javafx.scene.control.DialogEx;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.FxDialogEx;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.util.Pair;
import javafx.util.converter.IntegerStringConverter;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;
import org.controlsfx.validation.decoration.ValidationDecoration;

/**
 * 创建拼接屏对话框.
 */
public class SplicingScreenInputDialog extends DialogEx<CreateVideoWallGroupInfo> {
  private static final int defaultPort = 8000;
  private GridPane grid;

  private final List<Pair<Node, Node>> gridControls = new ArrayList<>();
  private final List<ValidationSupport> validations = new ArrayList<>();

  private static final String IP_REGX = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})"
      + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
  private static final ValidationDecoration VALIDATION_DECORATION = null;
  

  /**
   * 创建拼接屏的对话框.
   */
  public SplicingScreenInputDialog() {
    TextField ipTextField = initIp();
    TextField nameTextField = initName();
    TextField portTextField = initPort();
    TextField pidTextField = initPid();
    TextField secreteKeyTextField = initSecreteKey();
    ChoiceBox<String> typeChoiceBox = initType();

    initImpl();
    updateGrid();

    setResultConverter(dialogButton -> {
      ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonData.OK_DONE) {
        CreateVideoWallGroupInfo info = new CreateVideoWallGroupInfo();
        info.setIp(ipTextField.getText());
        info.setName(nameTextField.getText());
        info.setPort(Integer.parseInt(portTextField.getText()));
        info.setPid(pidTextField.getText());
        info.setSecreteKey(secreteKeyTextField.getText());
        info.setType(typeChoiceBox.getValue());
        return info;
      } else {
        return null;
      }
    });
  }

  @Override
  protected FxDialogEx createDialog() {
    return new UndecoratedHeavyWeightDialog(this);
  }

  private void initImpl() {
    final DialogPaneEx dialogPane = getDialogPane();
    this.grid = new GridPane();
    this.grid.setHgap(10);
    this.grid.setVgap(10);
    this.grid.setMaxWidth(Double.MAX_VALUE);
    this.grid.setAlignment(Pos.CENTER_LEFT);

    dialogPane.contentTextProperty().addListener(o -> updateGrid());
    dialogPane.setGraphic(null);
    setTitle(Bundle.getMessage("splicing_screen_input_dialog.title"));
    dialogPane.setHeaderText(Bundle.getMessage("splicing_screen_input_dialog.header"));
    dialogPane.getStyleClass().add("text-input-dialog");
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

    BooleanBinding validationBinding = new SimpleBooleanBinding(false);
    for (ValidationSupport support : validations) {
      validationBinding = validationBinding.or(support.invalidProperty());
    }
    dialogPane.lookupButton(ButtonType.OK).disableProperty().bind(validationBinding);
  }

  private TextField initIp() {
    TextField ipTextField = new TextField();
    ipTextField.setMaxWidth(Double.MAX_VALUE);
    ipTextField.setId("ip-input");
    GridPane.setHgrow(ipTextField, Priority.ALWAYS);
    GridPane.setFillWidth(ipTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(ipTextField, new IpValidator());

    Label ipLabel = new Label();
    ipLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    ipLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.ip_address"));

    gridControls.add(new Pair<>(ipLabel, ipTextField));
    validations.add(validationSupport);
    return ipTextField;
  }

  private TextField initName() {
    TextField nameTextField = new TextField();
    nameTextField.setId("user-input");
    nameTextField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(nameTextField, Priority.ALWAYS);
    GridPane.setFillWidth(nameTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(nameTextField, new StringValidator());

    Label nameLabel = new Label();
    nameLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    nameLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.name"));

    gridControls.add(new Pair<>(nameLabel, nameTextField));
    validations.add(validationSupport);
    return nameTextField;
  }

  private TextField initSecreteKey() {
    // -- textfield
    TextField secreteKeyTextField = new PasswordField();
    secreteKeyTextField.setId("pwd-input");
    secreteKeyTextField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(secreteKeyTextField, Priority.ALWAYS);
    GridPane.setFillWidth(secreteKeyTextField, true);

    //ValidationSupport validationSupport = new ValidationSupport();
    //validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    //validationSupport.registerValidator(pwdTextField, new StringValidator());

    // -- label
    Label secreteKeyLabel = new Label();
    secreteKeyLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    secreteKeyLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.secret_key"));

    gridControls.add(new Pair<>(secreteKeyLabel, secreteKeyTextField));
    //validations.add(validationSupport);
    return secreteKeyTextField;
  }

  private TextField initPort() {
    TextField portTextField = new TextField(defaultPort + "");
    GridPane.setHgrow(portTextField, Priority.ALWAYS);
    GridPane.setFillWidth(portTextField, true);
    UnaryOperator<Change> integerFilter = change -> {
      String newText = change.getControlNewText();
      if (newText.matches("-?([1-9][0-9]*)?")) {
        return change;
      }
      return null;
    };

    portTextField.setTextFormatter(
        new TextFormatter<>(new IntegerStringConverter(), defaultPort, integerFilter));

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(portTextField, new PortValidator());

    Label portLabel = new Label();
    portLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    portLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.port"));

    gridControls.add(new Pair<>(portLabel, portTextField));
    validations.add(validationSupport);

    return portTextField;
  }

  private TextField initPid() {
    TextField pidTextField = new TextField();
    pidTextField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(pidTextField, Priority.ALWAYS);
    GridPane.setFillWidth(pidTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(pidTextField, new StringValidator());

    Label pidLabel = new Label();
    pidLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    pidLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.pid"));

    gridControls.add(new Pair<>(pidLabel, pidTextField));
    validations.add(validationSupport);
    return pidTextField;
  }

  private ChoiceBox<String> initType() {
    ChoiceBox<String> typeChoiceBox = new ChoiceBox<>();
    typeChoiceBox.getItems().addAll(CaesarConstants.NovaDeviceType.E.getName(),
        CaesarConstants.NovaDeviceType.ALPHA.getName());
    typeChoiceBox.setValue(CaesarConstants.NovaDeviceType.E.getName());
    typeChoiceBox.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(typeChoiceBox, Priority.ALWAYS);
    GridPane.setFillWidth(typeChoiceBox, true);

    Label typeLabel = new Label();
    typeLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    typeLabel.setText(Bundle.getMessage("splicing_screen_input_dialog.type"));

    gridControls.add(new Pair<>(typeLabel, typeChoiceBox));
    return typeChoiceBox;
  }

  private void updateGrid() {
    grid.getChildren().clear();

    for (int i = 0; i < gridControls.size(); i++) {
      if (gridControls.get(i).getKey() != null) {
        grid.add(gridControls.get(i).getKey(), 0, i);
      }
      if (gridControls.get(i).getValue() != null) {
        grid.add(gridControls.get(i).getValue(), 1, i);
      }
    }

    getDialogPane().setContent(grid);
    if (((TextField) gridControls.get(0).getValue()).getText().isEmpty()
        || !((TextField) gridControls.get(1).getValue()).getText().isEmpty()) {
      Platform.runLater(() -> gridControls.get(0).getValue().requestFocus());
    } else {
      Platform.runLater(() -> gridControls.get(1).getValue().requestFocus());
    }
  }

  static class IpValidator implements Validator<String> {
    @Override
    public ValidationResult apply(Control target, String value) {
      ValidationResult vd = new ValidationResult();
      if (!value.matches(IP_REGX)) {
        vd.addErrorIf(target, Bundle.getMessage("splicing_screen_input_dialog.ip_format_error"), true);
      }
      return vd;
    }
  }

  static class StringValidator implements Validator<String> {

    @Override
    public ValidationResult apply(Control target, String value) {
      ValidationResult vd = new ValidationResult();
      if (value.isEmpty()) {
        vd.addErrorIf(target, "too short", true);
      }
      return vd;
    }

  }

  static class PortValidator implements Validator<String> {
    @Override
    public ValidationResult apply(Control target, String str) {
      ValidationResult vd = new ValidationResult();
      try {
        int value = Integer.parseInt(str);
        if (value <= 0 || value >= 65536) {
          vd.addErrorIf(target, Bundle.getMessage("splicing_screen_input_dialog.port_format_error"),
              true);
        }
      } catch (NumberFormatException exception) {
        vd.addErrorIf(target, Bundle.getMessage("splicing_screen_input_dialog.port_format_error"), true);
      }
      return vd;
    }
  }
}
