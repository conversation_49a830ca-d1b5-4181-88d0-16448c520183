package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.utility.UndecoratedDialog;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Window;
import javafx.util.StringConverter;

/**
 * AudioGroupSelectDialog.
 */
public class AudioGroupSelectDialog extends UndecoratedDialog<Integer> {
  private final ComboBox<Integer> comboBox = new ComboBox<>();

  /**
   * AudioGroupSelectDialog.
   */
  public AudioGroupSelectDialog(Window owner,
                                Integer currentGroup,
                                ObservableList<Integer> listChoices,
                                CaesarDeviceController controller) {
    super();
    initOwner(owner);
    initModality(Modality.APPLICATION_MODAL);
    comboBox.getSelectionModel().select(currentGroup);
    comboBox.setConverter(new IntegerStringConverter(controller));
    VBox contentBox = new VBox(comboBox);
    contentBox.setAlignment(Pos.CENTER);
    contentBox.setPadding(new Insets(10));
    DialogPaneEx dialogPane = new DialogPaneEx();
    dialogPane.setHeaderText(null);
    dialogPane.setContentText(null);
    dialogPane.setContent(contentBox);
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    setResultConverter(dialogButton -> {
      if (dialogButton == ButtonType.OK) {
        return comboBox.getValue();
      }
      return null;
    });
    comboBox.setItems(listChoices);
    setTitle(CaesarI18nCommonResource.getString("videowall.audio_group.dialog.title"));
    setDialogPane(dialogPane);
  }

  static class IntegerStringConverter extends StringConverter<Integer> {

    CaesarDeviceController controller;

    public IntegerStringConverter(CaesarDeviceController controller) {
      this.controller = controller;
    }

    @Override
    public String toString(Integer groupId) {
      if (groupId == null || groupId == 0) {
        return "";
      }
      for (TxRxGroupData txRxGroup : controller.getDataModel().getConfigDataManager()
          .getTxRxGroups()) {
        if (txRxGroup.getOid() + 1 == groupId) {
          return txRxGroup.getName();
        }
      }
      return "";
    }

    @Override
    public Integer fromString(String string) {
      return null;
    }
  }
}
