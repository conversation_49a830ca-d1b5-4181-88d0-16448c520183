package com.mc.tool.caesar.vpm.pages.hostupdate;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.hostupdate.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class CaesarHostUpdatePage implements Page {
  private final HostUpdatePageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);
  public static final String NAME = "hostupdate";

  public CaesarHostUpdatePage(CaesarEntity entity) {
    view = new HostUpdatePageView();
    view.getController().setDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage(CaesarHostUpdatePage.class, "HostUpdate.pageTitle.text");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-hostupdate-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {}
}
