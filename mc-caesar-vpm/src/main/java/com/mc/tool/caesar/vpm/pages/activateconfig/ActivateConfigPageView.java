package com.mc.tool.caesar.vpm.pages.activateconfig;

import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ActivateConfigPageView extends VBox {

  @Getter private ActivateConfigPageController controller;

  /** Contructor. */
  public ActivateConfigPageView() {

    controller = InjectorProvider.getInjector().getInstance(ActivateConfigPageController.class);
    URL location =
        getClass()
            .getResource("/com/mc/tool/caesar/vpm/pages/activateconfig/activateconfig_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(controller);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load activateconfig_View.fxml", exc);
    }
  }
}
