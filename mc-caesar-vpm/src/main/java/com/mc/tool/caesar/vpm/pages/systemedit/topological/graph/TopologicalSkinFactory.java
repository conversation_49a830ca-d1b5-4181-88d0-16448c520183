package com.mc.tool.caesar.vpm.pages.systemedit.topological.graph;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.view.CaesarGridLinkSkin;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.view.CaesarGridMatrixCellSkin;
import javafx.beans.property.DoubleProperty;
import javafx.scene.Parent;

/**
 * .
 */
public class TopologicalSkinFactory implements SkinFactory {

  public static final String TOPOLOGICAL_MATRIX = "TOPOLOGICAL.MATRIX";

  @Override
  public CellSkin createCellSkin(
      CellObject cellObject, Parent parent, Parent container, SkinManager skinManager) {
    CellSkin skin = null;
    if (cellObject.getType().equals(TOPOLOGICAL_MATRIX)) {
      skin = new CaesarGridMatrixCellSkin(cellObject, parent, container, skinManager);
    }
    if (skin != null) {
      skin.getRegion().layoutXProperty().bindBidirectional(cellObject.getXProperty());
      skin.getRegion().layoutYProperty().bindBidirectional(cellObject.getYProperty());
      skin.getRegion().prefWidthProperty().bindBidirectional(cellObject.getWidthProperty());
      skin.getRegion().prefHeightProperty().bindBidirectional(cellObject.getHeightProperty());
      skin.selectedProperty().bindBidirectional(cellObject.getSelectedProperty());
      skinManager.setCellSkin(cellObject, skin);
    }
    return skin;
  }

  @Override
  public CellSkin createScaleCellSkin(
      CellObject cellObject,
      Parent parent,
      Parent container,
      SkinManager skinManager,
      DoubleProperty cellScaleProperty) {
    return null;
  }

  @Override
  public ConnectorSkin createConnectorSkin(String type, Parent parent) {
    return null;
  }

  @Override
  public NewLinkSkin createNewLinkSkin(
      String type, Connector sender, Parent parent, Parent container, SkinManager skinManager) {
    return null;
  }

  @Override
  public LinkSkin createLinkSkin(
      LinkObject link, Parent parent, Parent container, SkinManager skinManager) {
    LinkSkin skin = new CaesarGridLinkSkin(link, parent, container, skinManager);
    skinManager.setLinkSkin(link, skin);
    return skin;
  }
}
