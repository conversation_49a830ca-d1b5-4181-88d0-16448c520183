package com.mc.tool.caesar.vpm.pages.activateconfig;

import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class ActivateConfigMetaCdm {
  private StringProperty filename = new SimpleStringProperty();
  private StringProperty name = new SimpleStringProperty();
  private StringProperty info = new SimpleStringProperty();
  private StringProperty address = new SimpleStringProperty("s0.0.0.0".substring(1));
  private StringProperty version = new SimpleStringProperty();

  int fileType;

  private SimpleBooleanProperty isSelected = new SimpleBooleanProperty(false);

  /** 配置文件数据. */
  public ActivateConfigMetaCdm(
      String filename, String name, String info, String address, String version, int fileType) {
    this.filename.set(filename);
    this.name.set(name);
    this.info.set(info);
    this.address.set(address);
    this.version.set(version);
    this.fileType = fileType;
  }

  /**
   * 复制数据.
   *
   * @param dest 要复制到的对象
   */
  public void copyTo(ActivateConfigMetaCdm dest) {
    dest.filename.set(filename.get());
    dest.name.set(name.get());
    dest.info.set(info.get());
    dest.address.set(address.get());
    dest.version.set(version.get());
    dest.fileType = fileType;
    dest.isSelected.set(isSelected.get());
  }

  public String getFilename() {
    return this.filename.get();
  }

  public StringProperty getFilenameProperty() {
    return filename;
  }

  public String getName() {
    return this.name.get();
  }

  public StringProperty getNameProperty() {
    return name;
  }

  public String getInfo() {
    return this.info.get();
  }

  public StringProperty getInfoProperty() {
    return info;
  }

  public String getAddress() {
    return this.address.get();
  }

  public StringProperty getAddressProperty() {
    return address;
  }

  public String getVersion() {
    return this.version.get();
  }

  public StringProperty getVersionProperty() {
    return version;
  }

  public Boolean getIsSelected() {
    return isSelected.get();
  }

  public void setIsSelected(Boolean select) {
    this.isSelected.set(select);
  }

  public int getFileType() {
    return fileType;
  }

  public void setFileType(int fileType) {
    this.fileType = fileType;
  }
}
