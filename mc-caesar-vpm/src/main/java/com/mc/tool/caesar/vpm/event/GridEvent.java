package com.mc.tool.caesar.vpm.event;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarControllerConstants;
import com.mc.tool.caesar.api.datamodel.GridData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.InetAddress;
import lombok.extern.slf4j.Slf4j;

/** 级联信息的广播事件. */
@Slf4j
public class GridEvent extends ServerEvent {
  private int type; // 事件类型 SET_GRIDMASTER or RET_GRIDINFO
  private GridData gmd;

  public GridEvent(byte[] message, int length, InetAddress inetAddress) {
    super(message, length, inetAddress, "UTF-8");
  }

  @Override
  protected void initialize(byte[] message, int length, InetAddress inetAddress, String charset) {
    super.initialize(message, length, inetAddress, charset);
  }

  @Override
  protected void parse() {
    this.type = -1;

    this.gmd = new GridData();

    InputStream is = new ByteArrayInputStream(getRawBytes());
    CfgReader reader = new CfgReader(is);
    try {
      if (reader.readByteValue() == CaesarControllerConstants.BIN_ESC) {
        int serverByte = reader.readByteValue();
        int requestByte = reader.readByteValue();
        if (serverByte
                == CaesarControllerConstants.BroadcastRequest.SET_GRIDMASTER.getServerRequestByte()
            && requestByte
                == CaesarControllerConstants.BroadcastRequest.SET_GRIDMASTER.getByteValue()) {
          this.type = requestByte;
          readGridData(reader);
        } else if (serverByte
                == CaesarControllerConstants.BroadcastRequest.RET_GRIDINFO.getServerResponseByte()
            && requestByte
                == CaesarControllerConstants.BroadcastRequest.RET_GRIDINFO.getByteValue()) {
          this.type = requestByte;
          readGridInfoData(reader);
        } else if (serverByte
                == CaesarControllerConstants.BroadcastRequest.GET_MATRIX_ACTIVE
                    .getServerRequestByte()
            && requestByte
                == CaesarControllerConstants.BroadcastRequest.GET_MATRIX_ACTIVE.getByteValue()) {
          this.type = requestByte;
          readGridStatusData(reader);
        }
      }
    } catch (ConfigException ex) {
      log.error("Grid response error!", ex);
    }
  }

  private void readGridData(CfgReader reader) throws ConfigException {
    reader.read2ByteValue();
    reader.readByteArray(4);
    reader.readByteArray(4);
    reader.readByteValue();
    reader.readByteValue();
    final int priority = reader.readInteger();
    final String gridName = reader.readString(CaesarConstants.MATRIX_NAME_LEN);
    final String gridDevice = reader.readString(CaesarConstants.MATRIX_DEVICE_LEN);
    reader.readByteArray(64);
    this.gmd.setGridname(gridName);
    this.gmd.setDeviceName(gridDevice);
    this.gmd.setIndex(priority);
  }

  /** 读取设备发现的信息. */
  private void readGridInfoData(CfgReader reader) throws ConfigException {
    final int len = reader.read2ByteValue();
    final byte[] src = reader.readByteArray(4);
    reader.readByteArray(4);
    reader.readByteArray(4);
    final String gridname = reader.readString(CaesarConstants.MATRIX_NAME_LEN);
    final String device = reader.readString(CaesarConstants.MATRIX_DEVICE_LEN);
    final VersionSet version = new VersionSet();
    version.readData(reader);
    final byte[] mac = reader.readByteArray(6);
    // 三期新增:双主控运行状态,长度扩充至152
    if (len == 152) {
      int matrixStatus = reader.readInteger();
      this.gmd.setActive((matrixStatus & 0x01) == 1);
      this.gmd.setBackup((matrixStatus >> 1 & 0x01) == 1);
      this.gmd.setHasMatrixInfo((matrixStatus >> 2 & 0x01) == 1);
    }

    this.gmd.setGridname(gridname);
    this.gmd.setHostname(src);
    this.gmd.setDeviceName(device);
    this.gmd.setAddress(src);
    this.gmd.setVersion(version);
    this.gmd.setMacAddress(mac);
  }

  private void readGridStatusData(CfgReader reader) throws ConfigException {
    reader.read2ByteValue();
    int matrixStatus = reader.readInteger();
    this.gmd.setActive((matrixStatus & 0x01) == 1);
    this.gmd.setBackup((matrixStatus >> 1 & 0x01) == 1);
    this.gmd.setHasMatrixInfo((matrixStatus >> 2 & 0x01) == 1);
  }

  public GridData getGridData() {
    return this.gmd;
  }

  public int getType() {
    return this.type;
  }
}
