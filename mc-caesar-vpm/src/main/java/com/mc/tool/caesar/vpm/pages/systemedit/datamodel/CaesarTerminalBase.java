package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.utils.SwitchType;
import com.mc.tool.framework.systemedit.datamodel.AbstractVisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.NetworkInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;

/**
 * .
 */
public abstract class CaesarTerminalBase extends AbstractVisualEditNode
    implements VisualEditTerminal {
  public static final int CAESAR_NORMAL_PORT = 1;
  public static final int CAESAR_RD_PORT = 2;

  @Expose
  protected ObjectProperty<TargetDeviceType> targetDeviceType = new SimpleObjectProperty<>();

  @Expose
  protected ObjectProperty<NetworkInterfaceType> networkInterfaceType =
      new SimpleObjectProperty<>(NetworkInterfaceType.SPF);

  @Expose
  protected ObjectProperty<MultimediaInterfaceType> multimediaInterfaceType =
      new SimpleObjectProperty<>(MultimediaInterfaceType.HDMI);

  @Expose protected IntegerProperty idProperty = new SimpleIntegerProperty();
  protected BooleanProperty onlineProperty = new SimpleBooleanProperty(false);
  protected BooleanProperty targetDeviceConnected = new SimpleBooleanProperty(false);
  protected BooleanProperty targetDeviceConnected2 = new SimpleBooleanProperty(false);

  protected ObservableList<ConnectorIdentifier> connectorIds = FXCollections.observableArrayList();

  @Getter protected BooleanProperty port1ConnectedProperty = new SimpleBooleanProperty(false);
  @Getter protected BooleanProperty port2ConnectedProperty = new SimpleBooleanProperty(false);

  @Getter private ObservableList<SwitchType> connectionType = FXCollections.observableArrayList();

  public int getId() {
    return idProperty.get();
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.TERMINAL_CELL;
  }

  @Override
  public int getPortCount() {
    if (getExtenderData() != null) {
      return getExtenderData().isStatusRedundant() ? 2 : 1;
    } else {
      return 1;
    }
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    ObservableList<VisualEditTerminal> result = FXCollections.observableArrayList();
    result.add(this);
    return result;
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return FXCollections.observableArrayList();
  }

  @Override
  public void init() {
    if (targetDeviceType.get() == null) {
      if (isRx()) {
        targetDeviceType.set(TargetDeviceType.MONITOR);
      } else {
        targetDeviceType.set(TargetDeviceType.COMPUTER);
      }
    }

    updateConnectors();
  }

  /** 重置信息. */
  public void reset() {
    idProperty.set(0);
    onlineProperty.set(false);
    targetDeviceConnected.set(false);
    port1ConnectedProperty.set(false);
    port2ConnectedProperty.set(false);
  }

  protected void updateConnectors() {
    if (getPortCount() > connectorIds.size()) {
      for (int i = connectorIds.size(); i < getPortCount(); i++) {
        connectorIds.add(ConnectorIdentifier.getIdentifier(i + 1));
      }
    } else {
      connectorIds.remove(getPortCount(), connectorIds.size());
    }
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectorIds;
  }

  @Override
  public boolean isOnline() {
    return onlineProperty.get();
  }

  @Override
  public BooleanProperty onlineProperty() {
    return onlineProperty;
  }

  @Override
  public Resolution getResolution(int index) {
    return null;
  }

  @Override
  public boolean isTargetDeviceConnected() {
    return targetDeviceConnected.get();
  }

  @Override
  public boolean isTargetDeviceConnected(int index) {
    switch (index) {
      case 0:
        return targetDeviceConnected.get();
      case 1:
        return targetDeviceConnected2.get();
      default:
        return targetDeviceConnected.get();
    }
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty() {
    return targetDeviceConnected;
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty(int index) {
    switch (index) {
      case 0:
        return targetDeviceConnected;
      case 1:
        return targetDeviceConnected2;
      default:
        return targetDeviceConnected;
    }
  }

  @Override
  public TargetDeviceType getTargetDeviceType() {
    return targetDeviceType.get();
  }

  @Override
  public void setTargetDeviceType(TargetDeviceType type) {
    targetDeviceType.set(type);
  }

  @Override
  public ObjectProperty<TargetDeviceType> targetDeviceTypeProperty() {
    return targetDeviceType;
  }

  @Override
  public NetworkInterfaceType getNetworkInterfaceType() {
    return networkInterfaceType.get();
  }

  @Override
  public void setNetworkInterfaceType(NetworkInterfaceType type) {
    networkInterfaceType.set(type);
  }

  @Override
  public ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty() {
    return networkInterfaceType;
  }

  @Override
  public MultimediaInterfaceType getMultimediaInterfaceType() {
    return multimediaInterfaceType.get();
  }

  @Override
  public void setMultimediaInterfaceType(MultimediaInterfaceType type) {
    multimediaInterfaceType.set(type);
  }

  @Override
  public ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty() {
    return multimediaInterfaceType;
  }

  @Override
  public boolean canSeperate() {
    return false;
  }

  @Override
  public void seperate(boolean sep) {}

  @Override
  public boolean isValid() {
    return idProperty != null && idProperty.get() != 0;
  }

  public abstract ExtenderData getExtenderData();

  /**
   * 获取分辨率类型.
   *
   * @return  分辨率类型
   */
  public CaesarConstants.Extender.ExtenderVideoResolutionType getResolutionType() {
    ExtenderData data = getExtenderData();
    if (data == null) {
      return CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_UNKNOWN;
    }
    return data.getExtenderStatusInfo().getVideoResolutionType();
  }
}
