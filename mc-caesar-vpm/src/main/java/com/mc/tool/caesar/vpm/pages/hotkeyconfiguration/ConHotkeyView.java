package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.FavoriteObject;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.framework.interfaces.DeviceControllable;
import java.beans.PropertyChangeListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.stage.Modality;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConHotkeyView extends HotkeyBaseView<ConsoleData> {

  /** Constructor. */
  public ConHotkeyView() {
    ResourceBundle bundle = ResourceBundle.getBundle("com.mc.tool.caesar.vpm.i18n.common");
    sourceListTitle.set(bundle.getString("con_hotkey.con_list"));
    hotkeyBlockTitle.set(bundle.getString("con_hotkey.con_hotkey_management"));
    copyBtn.setText(bundle.getString("con_hotkey.copy_btn"));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);

    sourceIdColumn.setCellValueFactory((feat) -> feat.getValue().getIdProperty());
    sourceNameColumn.setCellValueFactory((feat) -> feat.getValue().getNameProperty());
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    super.setDeviceController(deviceController);

    if (this.deviceController != null) {
      updateRawSource();
      CaesarSwitchDataModel model = this.deviceController.getDataModel();
      model.addPropertyChangeListener(
          ConsoleData.PROPERTY_STATUS,
          weakAdapter.wrap(
              (PropertyChangeListener) evt -> Platform.runLater(this::updateRawSource)));
    }
  }

  protected void updateRawSource() {
    sourceRawList.setAll(
        this.deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
    sourceRawList.removeIf(ConsoleData::isStatusVpcon);
  }

  @Override
  protected void onSendNewFavorite(FavoriteObject item) {
    if (item instanceof ConsoleData) {
      ConsoleData data = (ConsoleData) item;
      try {
        deviceController.getDataModel().sendConsoleData(Collections.singletonList(data));
      } catch (DeviceConnectionException | BusyException exception) {
        log.warn("Fail to send console data!", exception);
      }
    }
  }

  @FXML
  protected void onCopy(ActionEvent event) {
    List<ConsoleData> conList =
        new ArrayList<>(deviceController.getDataModel().getConfigDataManager().getActiveConsoles());
    conList.remove(getTableSelectionModel().getSelectedItem());
    CopyDialog<ConsoleData> copyHotkeyDialog = new CopyDialog<>();
    copyHotkeyDialog.setTitle(
        CaesarI18nCommonResource.getString("copy_hotkey_dialog.con_hotkey.title"));
    ((Label) copyHotkeyDialog.getCopySelectionView().getSourceHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_hotkey_dialog.con_hotkey.source_header.title"));
    ((Label) copyHotkeyDialog.getCopySelectionView().getTargetHeader())
        .setText(
            CaesarI18nCommonResource.getString(
                "copy_hotkey_dialog.con_hotkey.target_header.title"));
    copyHotkeyDialog.getCopySelectionView().setCellFactory(col -> new SelectionViewCell());
    copyHotkeyDialog.getCopySelectionView().getSourceItems().setAll(conList);
    if (sourceList != null && sourceList.getScene() != null) {
      copyHotkeyDialog.initOwner(sourceList.getScene().getWindow());
    }
    copyHotkeyDialog.initModality(Modality.APPLICATION_MODAL);
    copyHotkeyDialog.showAndWait();
    Collection<ConsoleData> result = copyHotkeyDialog.getResult();
    if (result != null) {
      int[] favorite = getTableSelectionModel().getSelectedItem().getFavoriteArray();
      updatingProperty.set(true);
      deviceController
          .submitAsync(
              () -> {
                List<ConsoleData> sendData = new ArrayList<>();
                for (ConsoleData consoleData : result) {
                  for (int idx = 0; idx < CaesarConstants.Cpu.FAVORITE; idx++) {
                    consoleData.setFavorite(idx, favorite[idx]);
                    sendData.add(consoleData);
                  }
                }
                try {
                  deviceController.getDataModel().sendConsoleData(sendData);
                } catch (DeviceConnectionException | BusyException exception) {
                  log.warn("Fail to send console data!", exception);
                }
              })
          .whenComplete(
              (item, throwable) -> {
                if (throwable != null) {
                  log.warn("Fail to send console data!", throwable);
                }
                PlatformUtility.runInFxThread(() -> updatingProperty.set(false));
              });
    }
  }

  private static class SelectionViewCell extends ListCell<ConsoleData> {

    @Override
    protected void updateItem(ConsoleData item, boolean empty) {
      super.updateItem(item, empty);
      if (empty || item == null) {
        setText("");
      } else {
        setText(item.getName());
      }
    }
  }
}
