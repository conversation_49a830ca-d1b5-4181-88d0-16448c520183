package com.mc.tool.caesar.vpm.pages.systemedit.graph;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.view.CaesarTerminalCellSkin;
import com.mc.tool.framework.systemedit.graph.SeSkinfactory;
import com.mc.tool.framework.systemedit.view.GroupCellSkin;
import javafx.scene.Parent;
import javafx.scene.image.Image;

/**
 * .
 */
public class CaesarSeSkinFactory extends SeSkinfactory {

  public CaesarSeSkinFactory() {}

  @Override
  protected CellSkin createTerminaCellSkin(
      CellObject cellObject, Parent parent, Parent container, SkinManager skinManager) {
    return new CaesarTerminalCellSkin(cellObject, parent, container, skinManager);
  }

  @Override
  protected CellSkin createSeatCellSkin(
      CellObject cellObject, Parent parent, Parent container, SkinManager skinManager) {
    Object value = cellObject.getValueProperty().get();
    if (value != null && value.equals(CaesarMultiScreenFunc.NAME)) {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      Image icon =
          new Image(
              classLoader.getResourceAsStream(
                  "com/mc/tool/framework/systemedit/multiscreen_group.png"));
      GroupCellSkin groupCellSkin = new GroupCellSkin(cellObject, parent, container, skinManager);
      groupCellSkin.setIcon(icon);
      return groupCellSkin;
    } else {
      return super.createSeatCellSkin(cellObject, parent, container, skinManager);
    }
  }
}
