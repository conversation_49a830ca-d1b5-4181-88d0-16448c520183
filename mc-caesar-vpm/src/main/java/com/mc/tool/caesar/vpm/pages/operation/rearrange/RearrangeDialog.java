package com.mc.tool.caesar.vpm.pages.operation.rearrange;

import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.vpm.pages.operation.rearrange.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarScreenData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.utility.UndecoratedWizard;
import com.mc.tool.framework.utility.UndecoratedWizardPane;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javafx.beans.property.DoubleProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.geometry.Pos;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.ListView;
import javafx.scene.control.TextField;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.VBox;
import javafx.stage.Window;
import javafx.util.Callback;
import javafx.util.Pair;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
@NoArgsConstructor
public final class RearrangeDialog {

  private static final ObservableList<VpConsoleData> targetList =
      FXCollections.observableArrayList();
  private static TextField[] textField;

  /**
   * 自动排列显示单元.
   */
  public static void show(
      Window owner,
      VisualEditFunc func,
      VideoWallControllable videoWallControllable,
      ObservableList<VpConsoleData> vpConDataList,
      CaesarMatrix matrix) {

    // 初始化
    Collection<VpConsoleData> targetItem = new ArrayList<>();
    Collection<VpConsoleData> result = new ArrayList<>(vpConDataList);
    CaesarVideoWallFunc videoWallFunc = null;
    if (func instanceof CaesarVideoWallFunc) {
      videoWallFunc = (CaesarVideoWallFunc) func;
    }
    if (videoWallFunc == null) {
      return;
    }
    CaesarVideoWallData videoWallData = (CaesarVideoWallData) videoWallFunc.getVideoWallObject();

    final int rows = videoWallData.getVertCount();
    final int columns = videoWallData.getHorzCount();

    // 删除已经绑定了的
    for (CaesarScreenData data : videoWallData.getScreens()) {
      if (data.isEmpty()) {
        continue;
      }
      result.remove(data.getVpScreen());
      targetItem.add(data.getVpScreen());
    }

    // 删除分辨率不一样的
    List<VpConsoleData> removedDatas = new ArrayList<>();
    for (VpConsoleData data : result) {
      VpResolution resolution = data.getOutResolution(0);
      if (!resolution.isValid()) {
        removedDatas.add(data);
      }
    }
    result.removeAll(removedDatas);

    // screenData
    ArrayList<CaesarScreenData> caesarScreenDatas = new ArrayList<>();
    ObservableList<? extends ScreenObject> caesarScreenDataList =
        videoWallFunc.getVideoWallObject().getScreens();
    for (ScreenObject screenObject : caesarScreenDataList) {
      if (screenObject instanceof CaesarScreenData) {
        caesarScreenDatas.add((CaesarScreenData) screenObject);
      }
    }

    // 向导
    GridPane guideGrid = new GridPane();
    guideGrid.setVgap(20);
    guideGrid.add(new Label(NbBundle.getMessage("rearrangeDialog.guide.label1")), 0, 0);
    guideGrid.add(new Label(NbBundle.getMessage("rearrangeDialog.guide.label2")), 0, 1);
    UndecoratedWizardPane guide = new UndecoratedWizardPane();
    guide.setHeaderText(NbBundle.getMessage("rearrangeDialog.guide.headerText"));
    guide.setContent(guideGrid);

    // 选择需要使用的单元
    RearrangeDialogListSelectionView<VpConsoleData> listSelectionView =
        new RearrangeDialogListSelectionView<>(rows * columns);

    listSelectionView.setCellFactory(
        new Callback<ListView<VpConsoleData>, ListCell<VpConsoleData>>() {

          @Override
          public ListCell<VpConsoleData> call(ListView<VpConsoleData> param) {
            return new ListCell<VpConsoleData>() {
              @Override
              protected void updateItem(VpConsoleData item, boolean empty) {
                super.updateItem(item, empty);

                if (empty || item == null) {
                  setText("");
                } else {
                  setText(item.getName());
                }
              }
            };
          }
        });

    listSelectionView.getTargetItems().addListener((ListChangeListener<VpConsoleData>) change -> {
      if (change.next()) {
        targetList.setAll(listSelectionView.getTargetItems());
      }
    });
    listSelectionView.getSourceItems().setAll(result);
    listSelectionView.getTargetItems().setAll(targetItem);

    VBox selectorVbox = new VBox();
    selectorVbox.getChildren()
        .add(new Label(NbBundle.getMessage("rearrangeDialog.selector.label")));
    selectorVbox.getChildren().add(listSelectionView);
    UndecoratedWizardPane selector = new UndecoratedWizardPane();

    selector.setGraphic(null);
    selector.setContent(selectorVbox);

    // 排列显示单元
    GridPane showGrid = new GridPane();
    showGrid.setHgap(10);
    showGrid.setVgap(5);
    showGrid.setGridLinesVisible(true);

    int index = 0;
    textField = new TextField[rows * columns];
    for (int row = 0; row < rows; row++) {
      for (int column = 0; column < columns; column++) {
        textField[index] = new TextField("-1");
        textField[index].setId(index + "");
        textField[index].setPrefSize(70, 40);
        textField[index].setAlignment(Pos.CENTER);
        showGrid.add(textField[index], column, row);
        index++;
      }
    }

    Map<Integer, Pair<VpGroup, Integer>> vpMap = new HashMap<>();
    UndecoratedWizardPane arrange = new UndecoratedWizardPane() {
      @Override
      public void onEnteringPage(UndecoratedWizard wizard) {
        initTextField(textField);
        videoWallControllable.beginUpdate();
        // 先清空所有绑定
        caesarScreenDatas.forEach(item -> item.setBingdingScreen(new Pair<>(null, -1)));
        try {
          int screenDatasIndex = 0;
          for (VpConsoleData vpConsoleData : targetList) {
            VpConsoleData parent = vpConsoleData.getParent();
            if (matrix != null) {
              VpGroup vpGroup = matrix.findVpGroup(parent);
              if (parent != null && vpGroup != null) {
                int index = parent.getOutportIndex(vpConsoleData);
                textField[screenDatasIndex].setText(screenDatasIndex + "");
                Pair<VpGroup, Integer> pair = new Pair<>(vpGroup, index);
                vpMap.put(screenDatasIndex, pair);
                caesarScreenDatas.get(screenDatasIndex).setBingdingScreen(pair);
                screenDatasIndex++;
              }
            }
          }
        } finally {
          videoWallControllable.endUpdate();
        }
      }
    };
    VBox showVbox = new VBox();
    Label msg = new Label(NbBundle.getMessage("rearrangeDialog.arrange.msg"));
    Label err = new Label();
    showVbox.getChildren().addAll(msg, showGrid, err);
    arrange.setContent(showVbox);
    ButtonType arrangeActionButton =
        new ButtonType(
            NbBundle.getMessage("rearrangeDialog.arrange.arrangeButton.text"), ButtonData.OK_DONE);
    arrange.getButtonTypes().add(arrangeActionButton);
    Button arrangeButton = (Button) arrange.lookupButton(arrangeActionButton);
    ArrayList<Integer> arrangeNum = new ArrayList<>();
    arrangeButton.addEventFilter(ActionEvent.ACTION, actionEvent -> {
      actionEvent.consume();
      arrangeNum.clear();
      Boolean numberFormat = true;
      for (int num = 0; num < rows * columns; num++) {
        String numString = textField[num].getText();
        Integer numInteger = null;
        try {
          numInteger = Integer.valueOf(numString);
        } catch (NumberFormatException ex) {
          numberFormat = false;
        }

        arrangeNum.add(numInteger);
      }

      if (numberFormat && !checkIndex(arrangeNum)) {
        videoWallControllable.beginUpdate();
        // 先清空所有绑定
        caesarScreenDatas.forEach(item -> item.setBingdingScreen(new Pair<>(null, -1)));
        try {
          Pair<VpGroup, Integer> emptyPair = new Pair<>(null, 0);
          for (int num = 0; num < rows * columns; num++) {
            if (arrangeNum.get(num) != -1 && vpMap.get(arrangeNum.get(num)) != null) {
              caesarScreenDatas.get(num).setBingdingScreen(vpMap.get(arrangeNum.get(num)));
            } else {
              caesarScreenDatas.get(num).setBingdingScreen(emptyPair);
            }
          }
        } finally {
          videoWallControllable.endUpdate();
        }
        err.setText("");
      } else {
        err.setText(NbBundle.getMessage("rearrangeDialog.arrange.arrangeButton.checkText"));
      }
    });
    UndecoratedWizard wizard = new UndecoratedWizard(owner);
    wizard.setFlow(new UndecoratedWizard.LinearFlow(guide, selector, arrange));
    DoubleProperty osdAlpha = videoWallData.getOsdData().getOsdAlphaProperty();
    double temp = osdAlpha.get();
    osdAlpha.set(1);
    wizard.showAndWait().ifPresent(res -> {
      if (res.equals(ButtonType.FINISH)) {
        log.info("Rearrange finish");
      }
    });
    osdAlpha.set(temp);
  }

  private static Boolean checkIndex(ArrayList<Integer> arrangeNum) {
    ArrayList<Integer> noRepeatArrayList = new ArrayList<>(arrangeNum);
    noRepeatArrayList.removeIf(num -> num.equals(-1));
    Set<Integer> noRepeatSet = new HashSet<>(noRepeatArrayList);
    if (noRepeatArrayList.size() != noRepeatSet.size()) {
      return true;
    }
    for (int integer : noRepeatSet) {
      if (integer > targetList.size()) {
        return true;
      }
    }
    return false;
  }

  private static void initTextField(TextField[] textFields) {
    for (TextField textField : textFields) {
      textField.setText("-1");
    }
  }
}
