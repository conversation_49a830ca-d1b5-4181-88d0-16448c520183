package com.mc.tool.caesar.vpm.pages.operation.videowall.controller;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.Vp6ConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.framework.operation.videowall.datamodel.VideoClipData;
import java.util.ArrayList;
import java.util.List;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VpConfigUtility {

  public static final int WIDTH_2K = 1920;

  public static boolean is4kVideo(int width) {
    return width > WIDTH_2K;
  }

  /**
   * 重新排序videoindexes，避免删除后有空余位置.
   *
   * @param deleteStartIndex 开始删除的序号，是videoIndexes数组的序号
   * @param videoIndexes 视频窗口索引集合.
   */
  public static void rerangeForMultipleDelete(int deleteStartIndex, Integer[] videoIndexes) {
    if (videoIndexes.length > deleteStartIndex
        && videoIndexes[deleteStartIndex].equals(videoIndexes[deleteStartIndex - 1])) {
      int findedIndex = -1;
      for (int i = deleteStartIndex + 1; i < videoIndexes.length; ) {
        if (i == videoIndexes.length - 1 || !videoIndexes[i].equals(videoIndexes[i + 1])) {
          findedIndex = i;
          break;
        } else {
          i += 2;
        }
      }
      if (findedIndex >= 0) {
        int value = videoIndexes[deleteStartIndex - 1];
        videoIndexes[deleteStartIndex - 1] = videoIndexes[findedIndex];
        videoIndexes[findedIndex] = value;
      }
    }
  }

  /**
   * 对input分段，output按比例做分段.
   *
   * @param input 输入
   * @param output 输出
   * @param segmentSize 段大小，针对输入
   * @return 分段后的输入与输出.
   */
  static List<Pair<Integer, Integer>> splitInputOutput(int input, int output, int segmentSize) {
    List<Pair<Integer, Integer>> result = new ArrayList<>();
    double factor = output * 1.0 / input;
    int outputSegment = (int) (segmentSize * factor + 0.5);
    while (input > 0) {
      int inputSeg = Math.min(segmentSize, input);
      int outputSeg = Math.min(outputSegment, output);
      result.add(new Pair<>(inputSeg, outputSeg));
      input -= inputSeg;
      output -= outputSeg;
    }
    return result;
  }

  /**
   * 切割videoClipData.
   *
   * @param videoClipData 要切割的clip.
   * @param maxOriginWidth orgin的最大宽度.
   * @return 切割后的clip
   */
  static List<VideoClipData> splitVideoClipData(VideoClipData videoClipData, int maxOriginWidth) {
    List<VideoClipData> result = new ArrayList<>();
    List<Pair<Integer, Integer>> splitResult =
        splitInputOutput(
            videoClipData.getOriginWidth(), videoClipData.getScaledWidth(), maxOriginWidth);
    int startOriginX = 0;
    int startScaledX = 0;
    int index = -1;
    for (Pair<Integer, Integer> pair : splitResult) {
      index++;
      VideoClipData newClip = new VideoClipData();
      videoClipData.copyTo(newClip);
      newClip.setOriginWidth(pair.getKey());
      newClip.setScaledWidth(pair.getValue());
      try {
        // 更新origin clip
        int oldOriginClipEnd = videoClipData.getOriginClipX() + videoClipData.getOriginClipWidth();
        newClip.setOriginClipX(Math.max(videoClipData.getOriginClipX(), startOriginX));
        newClip.setOriginClipWidth(
            Math.min(oldOriginClipEnd, startOriginX + newClip.getOriginWidth())
                - newClip.getOriginClipX());
        newClip.setOriginClipX(newClip.getOriginClipX() - startOriginX);
        if (newClip.getOriginClipWidth() <= 0) {
          continue;
        }
        // 更新scaled clip
        int oldScaledClipEnd = newClip.getScaledClipX() + newClip.getScaledClipWidth();
        newClip.setScaledClipX(Math.max(newClip.getScaledClipX(), startScaledX));
        newClip.setScaledClipWidth(
            Math.min(oldScaledClipEnd, startScaledX + newClip.getScaledWidth())
                - newClip.getScaledClipX());
        newClip.setScaledClipX(newClip.getScaledClipX() - startScaledX);
        if (newClip.getScaledClipWidth() <= 0) {
          assert false;
          continue;
        }

        // 更新output
        newClip.setOutWidth(newClip.getScaledClipWidth());
        newClip.setOutX(
            videoClipData.getOutX()
                + newClip.getScaledClipX()
                + startScaledX
                - videoClipData.getScaledClipX());

        newClip.setSplitIndex(index);
        result.add(newClip);
      } finally {
        startOriginX += pair.getKey();
        startScaledX += pair.getValue();
      }
    }
    return result;
  }

  /**
   * 获取有效的4k端口.
   *
   * @param vpConsoleData vpcon
   * @return 端口数
   */
  public static int get4kValidPort(VpConsoleData vpConsoleData) {
    if (!vpConsoleData.isStatusOnline()) {
      return 0;
    }
    ConsoleData[] cons = vpConsoleData.getInPortList();
    int result = 0;
    for (int i = 0; i < cons.length / 2; i++) {
      if (cons[i] != null && cons[i].isOnline()) {
        result++;
      }
    }
    return result;
  }

  /**
   * 计算最多支持的2k窗口个数.
   *
   * @param cnt4k 现有的4k窗口个数.
   * @param vpConsoleData vp con
   * @return 最多支持的2k窗口个数.
   */
  public static int calMax2kCount(int cnt4k, VpConsoleData vpConsoleData) {
    if (!vpConsoleData.isStatusOnline()) {
      return 0;
    }
    int validCount = vpConsoleData.getValidInPortCount();
    int validCount4k = get4kValidPort(vpConsoleData);
    int validCount2k = validCount - cnt4k;
    if (validCount4k < cnt4k) {
      cnt4k = validCount4k;
    }
    // 统计可用的2k端口
    ConsoleData[] cons = vpConsoleData.getInPortList();
    int counted4k = 0;
    // 先统计只需要一个端口的4k
    for (int i = 0; i < cons.length / 2 && counted4k < cnt4k; i++) {
      ConsoleData left = cons[i];
      if (left == null || !left.isOnline()) {
        continue;
      }
      ConsoleData consoleData = cons[get4kSecondPort(i)];
      if (consoleData == null || !consoleData.isOnline()) {
        counted4k++;
      }
    }
    // 减去需要两个端口的4k
    for (int i = 0; i < cons.length / 2 && counted4k < cnt4k; i++) {
      ConsoleData left = cons[i];
      if (left == null || !left.isOnline()) {
        continue;
      }
      ConsoleData consoleData = cons[get4kSecondPort(i)];
      if (consoleData != null && consoleData.isOnline()) {
        counted4k++;
        validCount2k--;
      }
    }
    return Math.max(validCount2k, 0);
  }

  /**
   * 获取4k信号占用的第二个端口.
   *
   * @param port 第一个端口
   * @return 第二个端口索引.
   */
  public static int get4kSecondPort(int port) {
    if (port >= Vp6ConfigData.VIRTUAL_PORT) {
      // 虚拟端口
      return port + 1;
    } else {
      return port + 4;
    }
  }
}
