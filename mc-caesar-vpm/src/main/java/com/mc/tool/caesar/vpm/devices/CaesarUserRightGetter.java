package com.mc.tool.caesar.vpm.devices;

import com.mc.tool.framework.interfaces.UserRightGetter;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public interface CaesarUserRightGetter extends UserRightGetter {
  boolean isPageVisible(String pageName);

  boolean isSystemLogVisible();

  boolean isActivateConfigVisible();

  boolean isOsdConfigVisible();

  boolean isCrossScreenCreateDeletable();

  boolean isCrossScreenConnectable();

  boolean isMultiScreenCreateDeletable();

  boolean isMultiScreenConnectable();

  boolean isTxConfigEditable();

  boolean isRxConfigEditable();

  boolean isUserEditable();

  boolean isUserRightEditable();

  boolean isRxRightEditable();

  boolean isUserGroupEditable();

  boolean isUserGroupRightEditable();

  boolean isRxHotKeyEditable();

  boolean isRxMacroEditable();

  boolean isUserMacroEditable();

  boolean isVideoWallEditable(VideoWallFunc func);

  boolean isTxGroupCreateDeletable();
}
