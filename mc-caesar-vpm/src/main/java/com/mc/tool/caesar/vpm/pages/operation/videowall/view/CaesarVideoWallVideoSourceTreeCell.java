package com.mc.tool.caesar.vpm.pages.operation.videowall.view;

import com.google.gson.Gson;
import com.mc.tool.caesar.api.datamodel.SourceClipData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.controller.Bundle;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.DraggedVideoSourceInfo;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalClipWrapper;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminalWrapper;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.Arrays;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TreeCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.text.TextAlignment;
import javafx.stage.Modality;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarVideoWallVideoSourceTreeCell extends TreeCell<VisualEditNode> {

  @Data
  static class ClipData {
    String name = "Clip";
    int left = 0;
    int top = 0;
    int right = 0;
    int bottom = 0;
  }

  static class MenuTx extends MenuItem {
    MenuTx(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.add"));
      setOnAction(handler);
    }
  }

  static class MenuEdit extends MenuItem {
    MenuEdit(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.edit"));
      setOnAction(handler);
    }
  }

  static class MenuDelete extends MenuItem {
    MenuDelete(EventHandler<ActionEvent> handler) {
      setText(Bundle.NbBundle.getMessage("signalCut.delete"));
      setOnAction(handler);
    }
  }

  private HBox box;
  private HBox innerBox;
  private Label nameLabel;
  private Label indexLabel;

  private CaesarDeviceController deviceController;

  /**
   * .
   */
  public CaesarVideoWallVideoSourceTreeCell(CaesarDeviceController deviceController) {
    this.deviceController = deviceController;
    box = new HBox();
    box.setAlignment(Pos.CENTER_LEFT);
    nameLabel = new Label();
    indexLabel = new Label();
    innerBox = new HBox();
    innerBox.setAlignment(Pos.CENTER);
    innerBox.getChildren().add(indexLabel);
    Region region = new Region();
    HBox.setHgrow(region, Priority.ALWAYS);
    box.getChildren().addAll(nameLabel, innerBox);
    box.setSpacing(10);
    indexLabel.setAlignment(Pos.CENTER);
    indexLabel.setTextAlignment(TextAlignment.CENTER);
    indexLabel.setTextFill(Color.web("#333333"));
    indexLabel.setPadding(new Insets(0));
    innerBox.setStyle("-fx-background-color: #cccccc; -fx-background-radius: 15; "
        + "-fx-pref-height: 15; -fx-pref-width: 15; " + "-fx-min-height: 15; -fx-min-width: 15; "
        + "-fx-max-height: 15; -fx-max-width: 15");
    innerBox.managedProperty().bind(innerBox.visibleProperty());
  }

  @Override
  protected void updateItem(VisualEditNode item, boolean empty) {
    super.updateItem(item, empty);
    setContextMenu(null);
    if (!empty) {
      graphicProperty().unbind();
      boolean supportClipTx = deviceController != null
          && deviceController.getDataModel().getConfigMetaData().getUtilVersion().supportClipTx();
      if (item instanceof VisualEditTerminal && !(item instanceof CaesarCpuTerminalClipWrapper)) {
        VisualEditTerminal terminal = (VisualEditTerminal) item;
        nameLabel.textProperty().bind(item.nameProperty());
        nameLabel.graphicProperty().bind(new CaesarVideoWallTargetDeviceLogoBinding(terminal));
        if (item instanceof CaesarCpuTerminalWrapper) {
          // 双HDMI TX
          CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) terminal;
          indexLabel.setText((wrapper.getIndex() + 1) + "");
          innerBox.setVisible(wrapper.canSeperate());
          if (supportClipTx) {
            // 添加右键菜单
            ContextMenu menu = new ContextMenu();
            menu.getItems().add(new MenuTx(this::onAddSignalCutAction));
            setContextMenu(menu);
          }
        } else {
          innerBox.setVisible(false);
        }
        setGraphic(box);
        textProperty().unbind();
        setText(null);
        this.setStyle("-fx-font-weight: normal");
      } else if (item instanceof CaesarCpuTerminalClipWrapper && supportClipTx) {
        // TX裁剪
        CaesarCpuTerminalClipWrapper wrapper = (CaesarCpuTerminalClipWrapper) item;
        int sourceClipIndex = wrapper.getSourceClipIndex();
        SourceClipData clipData = deviceController.getDataModel().getConfigData().getSourceClipData(sourceClipIndex);
        if (clipData != null) {
          nameLabel.textProperty().unbind();
          nameLabel.graphicProperty().bind(new CaesarVideoWallTargetDeviceLogoBinding(wrapper));
          nameLabel.setText(String.format("%s(%d,%d,%d,%d)", clipData.getName(), clipData.getLeft(), clipData.getTop(),
              clipData.getRight(), clipData.getBottom()));
          innerBox.setVisible(false);
          setGraphic(box);
          textProperty().unbind();
          setText(null);
          this.setStyle("-fx-font-weight: normal");
          // 添加右键菜单
          ContextMenu menu = new ContextMenu();
          menu.getItems().add(new MenuEdit(this::onEditSignalCutAction));
          menu.getItems().add(new MenuDelete(this::onDeleteSignalCutAction));
          setContextMenu(menu);
        } else {
          textProperty().set("mismatch at " + sourceClipIndex);
          setGraphic(null);
          this.setStyle("-fx-font-weight: bold;");
        }
      } else {
        textProperty().bind(item.nameProperty());
        setGraphic(null);
        this.setStyle("-fx-font-weight: bold;");
      }
      setOnDragDetected(this::onDragDetected);
    } else {
      graphicProperty().unbind();
      setGraphic(null);
      textProperty().unbind();
      setText(null);
    }
  }

  protected void onDragDetected(MouseEvent event) {
    if (getItem() != null && getItem() instanceof VisualEditTerminal) {
      ClipboardContent content = new ClipboardContent();
      content.putString(getItem().getGuid());
      DraggedVideoSourceInfo info = new DraggedVideoSourceInfo();
      if (getItem() instanceof CaesarCpuTerminalWrapper) {
        CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) getItem();
        info.setSourceIndex(wrapper.getIndex());
      }
      if (getItem() instanceof CaesarCpuTerminalClipWrapper) {
        CaesarCpuTerminalClipWrapper wrapper = (CaesarCpuTerminalClipWrapper) getItem();
        info.setClipIndex(wrapper.getSourceClipIndex() + 1);
      }
      content.putUrl(new Gson().toJson(info));
      Image image = getDragImage();
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    }
    event.consume();
  }

  protected Image getDragImage() {
    return ((ImageView) nameLabel.getGraphic()).getImage();
  }

  protected boolean editClipData(Resolution resolution, ClipData clipData) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.setTitle(Bundle.NbBundle.getMessage("signalCut.title"));
    dialog.initOwner(this.getScene().getWindow());
    dialog.initModality(Modality.APPLICATION_MODAL);
    SignalCuttingConfigView configView =
        new SignalCuttingConfigView(clipData.getName(), resolution.getWidth() == 0 ? 1920 : resolution.getWidth(),
            resolution.getHeight() == 0 ? 1080 : resolution.getHeight(), clipData.getLeft(),
            clipData.getTop(), clipData.getRight(), clipData.getBottom());
    dialog.getDialogPane().setContent(configView.getView());
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    Node okButton = dialog.getDialogPane().lookupButton(ButtonType.OK);
    okButton.disableProperty().bind(configView.getPresenter().validProperty().not());
    dialog.showAndWait();
    if (dialog.getResult() == ButtonType.OK) {
      clipData.setTop(configView.getPresenter().getTop());
      clipData.setRight(configView.getPresenter().getRight());
      clipData.setBottom(configView.getPresenter().getBottom());
      clipData.setLeft(configView.getPresenter().getLeft());
      clipData.setName(configView.getPresenter().getName());
      return true;
    } else {
      return false;
    }
  }

  protected void onAddSignalCutAction(ActionEvent event) {
    SourceClipData clipData = deviceController.getDataModel().getConfigDataManager().getFreeSourceClipData();
    if (clipData == null) {
      AlertEx alert = new AlertEx(AlertEx.AlertExType.WARNING);
      alert.setContentText(Bundle.NbBundle.getMessage("signalCut.noFreeClipData"));
      alert.showAndWait();
      return;
    }
    VisualEditNode node = getItem();
    if (node instanceof CaesarCpuTerminalWrapper) {
      CaesarCpuTerminalWrapper wrapper = (CaesarCpuTerminalWrapper) node;
      CaesarCpuTerminal terminal = wrapper.getTerminal();
      if (terminal != null && terminal.getCpuData() != null) {
        // 编辑数据
        Resolution res = terminal.getResolution(wrapper.getIndex());
        final ClipData editableClipData = new ClipData();
        if (editClipData(res, editableClipData)) {
          // 发送数据
          deviceController.execute(() -> {
            clipData.setLeft(editableClipData.getLeft());
            clipData.setTop(editableClipData.getTop());
            clipData.setRight(editableClipData.getRight());
            clipData.setBottom(editableClipData.getBottom());
            clipData.setCpuIndex(terminal.getCpuData().getOid() + 1);
            clipData.setSourceIndex(wrapper.getIndex());
            clipData.setName(editableClipData.getName());
            try {
              deviceController.getDataModel().sendSourceClipData(Arrays.asList(clipData));
            } catch (DeviceConnectionException e) {
              log.error("Failed to send source clip data", e);
            } catch (BusyException e) {
              log.error("Failed to send source clip data", e);
            }
          });
        }
      }
    }
  }


  protected void onEditSignalCutAction(ActionEvent event) {
    VisualEditNode item = getItem();
    if (!(item instanceof CaesarCpuTerminalClipWrapper)) {
      return;
    }
    CaesarCpuTerminalClipWrapper wrapper = (CaesarCpuTerminalClipWrapper) item;
    int sourceClipIndex = wrapper.getSourceClipIndex();
    SourceClipData clipData = deviceController.getDataModel().getConfigData().getSourceClipData(sourceClipIndex);
    if (clipData == null) {
      return;
    }
    CaesarCpuTerminal terminal = wrapper.getTerminalWrapper().getTerminal();
    if (terminal != null && terminal.getCpuData() != null) {
      // 编辑数据
      final ClipData editableClipData = new ClipData();
      editableClipData.setLeft(clipData.getLeft());
      editableClipData.setTop(clipData.getTop());
      editableClipData.setRight(clipData.getRight());
      editableClipData.setBottom(clipData.getBottom());
      editableClipData.setName(clipData.getName());
      Resolution res = terminal.getResolution(wrapper.getTerminalWrapper().getIndex());
      if (editClipData(res, editableClipData)) {
        // 发送数据
        deviceController.execute(() -> {
          clipData.setName(editableClipData.getName());
          clipData.setLeft(editableClipData.getLeft());
          clipData.setTop(editableClipData.getTop());
          clipData.setRight(editableClipData.getRight());
          clipData.setBottom(editableClipData.getBottom());
          try {
            deviceController.getDataModel().sendSourceClipData(Arrays.asList(clipData));
          } catch (DeviceConnectionException e) {
            log.error("Failed to send source clip data", e);
          } catch (BusyException e) {
            log.error("Failed to send source clip data", e);
          }
        });
      }
    }
  }

  protected void onDeleteSignalCutAction(ActionEvent event) {
    VisualEditNode item = getItem();
    if (!(item instanceof CaesarCpuTerminalClipWrapper)) {
      return;
    }
    CaesarCpuTerminalClipWrapper wrapper = (CaesarCpuTerminalClipWrapper) item;
    int sourceClipIndex = wrapper.getSourceClipIndex();
    SourceClipData clipData = deviceController.getDataModel().getConfigData().getSourceClipData(sourceClipIndex);
    if (clipData == null) {
      return;
    }
    // 发送数据
    deviceController.execute(() -> {
      clipData.setCpuIndex(0);
      clipData.setSourceIndex(0);
      try {
        deviceController.getDataModel().sendSourceClipData(Arrays.asList(clipData));
      } catch (DeviceConnectionException e) {
        log.error("Failed to send source clip data", e);
      } catch (BusyException e) {
        log.error("Failed to send source clip data", e);
      }
    });
  }
}
