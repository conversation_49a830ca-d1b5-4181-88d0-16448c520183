package com.mc.tool.caesar.vpm.pages.systemedit.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.util.Collection;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.TextField;
import javafx.stage.Modality;
import javafx.stage.Window;
import javafx.util.StringConverter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConnectDialog implements Initializable {

  private static final String FXML_PATH =
      "com/mc/tool/caesar/vpm/pages/systemedit/connect_dialog/connect_dialog.fxml";
  private static final String BUNDLE_PATH =
      "com/mc/tool/caesar/vpm/pages/systemedit/connect_dialog/Bundle";

  private Node root;

  @FXML private TextField txNameText;

  @FXML private TextField connectModeText;

  @FXML private ComboBox<VisualEditTerminal> rxCombo;

  /** . */
  public ConnectDialog() {
    FXMLLoader loader = new FXMLLoader();
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    loader.setLocation(classLoader.getResource(FXML_PATH));
    loader.setResources(ResourceBundle.getBundle(BUNDLE_PATH));
    loader.setController(this);

    try {
      root = loader.load();
    } catch (IOException exception) {
      log.error("Fail to load connect_dialog.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    txNameText.setEditable(false);
    connectModeText.setEditable(false);
    rxCombo.setConverter(new VisualEditTerminalStringConverter());
  }

  public Node getView() {
    return root;
  }

  public void setTxName(String name) {
    txNameText.setText(name);
  }

  public void setConnectMode(String mode) {
    connectModeText.setText(mode);
  }

  public void setRxCandicates(Collection<VisualEditTerminal> terminals) {
    rxCombo.getItems().setAll(terminals);
  }

  public VisualEditTerminal getSelectedTerminal() {
    return rxCombo.getSelectionModel().getSelectedItem();
  }

  /**
   * 显示连接对话框.
   *
   * @param owner owner
   * @param tx tx名称
   * @param mode 连接模式
   * @param terminals rx列表
   * @return 选中的rx，如果没有选中，返回null
   */
  public static VisualEditTerminal show(
      Window owner, String tx, String mode, Collection<VisualEditTerminal> terminals) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(ResourceBundle.getBundle(BUNDLE_PATH).getString("title"));
    dialog.getDialogPane().getButtonTypes().add(ButtonType.OK);

    ConnectDialog connectDialog = new ConnectDialog();
    connectDialog.setTxName(tx);
    connectDialog.setConnectMode(mode);
    connectDialog.setRxCandicates(terminals);

    dialog.getDialogPane().setContent(connectDialog.getView());
    Optional<ButtonType> result = dialog.showAndWait();
    if (result.isPresent()) {
      return connectDialog.getSelectedTerminal();
    } else {
      return null;
    }
  }

  static class VisualEditTerminalStringConverter extends StringConverter<VisualEditTerminal> {

    @Override
    public String toString(VisualEditTerminal object) {
      return object.getName();
    }

    @Override
    public VisualEditTerminal fromString(String string) {
      return null;
    }
  }
}
