package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mc.tool.caesar.vpm.pages.splicingscreen.TreeViewItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DecodeCard.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DecodeCard implements TreeViewItem {
  /**
   * 凯中的单画面RX或者四画面RX的ID.
   **/
  @JsonProperty("rxid")
  private int rxId;
  /**
   * RX名称.
   **/
  private String name;
  /**
   * RX在凯中的端口.
   **/
  private int port;
  /**
   * 诺瓦母卡sn.
   **/
  private String sn;
  /**
   * RX类型，0表示普通RX，1表示普通四画面RX, 2表示嗨动单画面子卡，3便是嗨动四画面子卡.
   **/
  private int type;
  /**
   * 对应诺瓦数据卡的输入ID.
   **/
  @JsonProperty("inputid")
  private int inputId;
  /**
   * 对应诺瓦输入卡的槽位ID.
   **/
  @JsonProperty("slotid")
  private int slotId;

  @Override
  public String toString() {
    return "DecodeCard {\n"
        + "    rxid: " + toIndentedString(rxId) + "\n"
        + "    name: " + toIndentedString(name) + "\n"
        + "    port: " + toIndentedString(port) + "\n"
        + "    sn: " + toIndentedString(sn) + "\n"
        + "    type: " + toIndentedString(type) + "\n"
        + "    inputid: " + toIndentedString(inputId) + "\n"
        + "    slotid: " + toIndentedString(slotId) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  private String getCardType(int type) {
    switch (type) {
      case 1:
        return "4view";
      case 2:
        return "kaito";
      case 3:
        return "kaito_4view";
      default:
        return "normal";
    }
  }

  @Override
  public String cellToString() {
    return String.format("[%d]%s(%s)", this.getRxId(), this.getName(),
        getCardType(this.getType()));
  }
}
