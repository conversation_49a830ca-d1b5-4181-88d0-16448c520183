package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * .
 */
public class MenuGroupingTx4Caesar extends MenuQuickCreate {

  private TxRxGroupData unActiveTxGroup;

  /** 快速创建TX分组. */
  public MenuGroupingTx4Caesar(SystemEditControllable controllable) {
    super(controllable);
    this.setText(Bundle.NbBundle.getMessage("menu.grouping.tx"));
    if (controllable instanceof CaesarSystemEditController) {
      Collection<TxRxGroupData> existGroups =
          ((CaesarSystemEditController) controllable)
              .getDeviceController()
              .getDataModel()
              .getConfigDataManager()
              .getActiveTxRxGroups();
      userNamesProperty.set(existGroups);
    }
  }

  @Override
  protected void initTerminal() {
    nodes =
        controllable.getModel().getAllTerminals().stream()
            .filter(VisualEditTerminal::isTx)
            .filter(VisualEditTerminal::isOnline)
            .filter(item -> item.getParent() instanceof CaesarMatrix)
            .collect(Collectors.toList());
  }

  @Override
  protected boolean getMenuDisableValue() {
    if (((CaesarSystemEditController) controllable)
        .getDeviceController()
        .getDataModel()
        .getConfigMetaData()
        .getUtilVersion()
        .hasTxRxGroupData()) {
      unActiveTxGroup =
          ((CaesarSystemEditController) controllable)
              .getDeviceController()
              .getDataModel()
              .getConfigDataManager()
              .getUnActiveTxRxGroup();
      return nodes.isEmpty() || unActiveTxGroup == null;
    }
    return nodes.isEmpty();
  }

  @Override
  protected void handleAction() {
    dialog
        .showAndWait()
        .ifPresent(
            res -> {
              if (!res.isEmpty()) {
                String groupName = nameField.getText();
                if (unActiveTxGroup != null) {
                  CpuGroup cpuGroup =
                      controllable.addGroup(
                          groupName, CpuGroup.class, res.toArray(new VisualEditNode[0]));
                  cpuGroup.setTxRxGroupData(unActiveTxGroup);
                  ((CaesarSystemEditController) controllable)
                      .getDeviceController()
                      .execute(
                          () -> {
                            for (VisualEditNode re : res) {
                              if (re instanceof CaesarCpuTerminal) {
                                CaesarCpuTerminal cpuTerminal = (CaesarCpuTerminal) re;
                                cpuTerminal
                                    .getCpuData()
                                    .setTxGroupIndex(unActiveTxGroup.getOid() + 1);
                              }
                            }
                            unActiveTxGroup.setName(groupName);
                            unActiveTxGroup.setType(TxRxGroupDataType.TX);
                            ((CaesarSystemEditController) controllable)
                                .getDeviceController()
                                .addTxRxGroupData(unActiveTxGroup);
                          });
                } else {
                  controllable.addGroup(
                      groupName, VisualEditGroup.class, res.toArray(new VisualEditNode[0]));
                }
              }
            });
  }

  @Override
  protected void initBundle() {
    groupNameLabel.setText(Bundle.NbBundle.getMessage("menu.grouping.tx.name"));
    dialog.setTitle(Bundle.NbBundle.getMessage("menu.grouping.tx"));
  }

  @Override
  protected void setNameFieldText() {
    nameField.setText("Group");
  }
}
