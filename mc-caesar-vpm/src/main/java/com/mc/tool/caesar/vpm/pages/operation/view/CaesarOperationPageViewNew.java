package com.mc.tool.caesar.vpm.pages.operation.view;

import com.mc.tool.caesar.vpm.pages.operation.controller.CaesarOperationControllerNew;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.AnchorPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarOperationPageViewNew extends AnchorPane {
  @Getter private final CaesarOperationControllerNew controllable;

  /** Constructor. */
  public CaesarOperationPageViewNew(VisualEditModel model) {
    URL location =
        getClass().getResource("/com/mc/tool/caesar/vpm/pages/operation/operation_view_new.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    controllable = InjectorProvider.getInjector().getInstance(CaesarOperationControllerNew.class);
    controllable.initModel(model);
    loader.setController(controllable);
    loader.setResources(I18nUtility.getI18nBundle("operation"));
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load operation_view.fxml", exception);
    }
  }
}
