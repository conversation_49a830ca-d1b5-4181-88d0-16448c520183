package com.mc.tool.caesar.vpm.pages.permissionconfiguration;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.vpm.CaesarAuthorityEntity;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.Bundle.NbBundle;
import com.mc.tool.framework.interfaces.Page;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;
import javafx.util.Pair;

/**
 * .
 */
public class CaesarPermissionConfigurationPage implements Page {
  public static final String NAME = "permissionconfiguration";
  public static final String CON_RIGHT_TAB = "conrighttab";
  private final CaesarPermissionConfigurationPageView view;
  private final BooleanProperty visibility = new SimpleBooleanProperty(true);

  public CaesarPermissionConfigurationPage(CaesarEntity entity) {
    view = new CaesarPermissionConfigurationPageView();
    view.getController().setDeviceController(entity.getController());
  }

  public CaesarPermissionConfigurationPage(CaesarAuthorityEntity entity) {
    view = new CaesarPermissionConfigurationPageView();
    view.getController().setAuthorityDeviceController(entity.getController());
  }

  @Override
  public String getTitle() {
    return NbBundle.getMessage("PermissionConfiguration.title");
  }

  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public String getStyleClass() {
    return "caesar-permissionconfiguration-page";
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibility;
  }

  @Override
  public void showObject(Object object) {
    Pair<String, ConsoleData> pair = (Pair<String, ConsoleData>) object;
    switch (pair.getKey()) {
      case CON_RIGHT_TAB:
        view.getController()
            .getTabPane()
            .getSelectionModel()
            .select(view.getController().getConCpuRightTab());
        view.getController().getConCpuRightView().selectSourceItem(pair.getValue());
        break;
      default:
        break;
    }
  }

  @Override
  public void refreshOnShow() {
    view.getController().updateUserRight();
  }
}
