package com.mc.tool.caesar.vpm.pages.hotkeyconfiguration;

import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.Collection;
import javafx.beans.binding.Bindings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogPaneEx;
import lombok.Getter;

/**
 * .
 */
public class CopyDialog<T> extends UndecoratedDialog<Collection<T>> {

  @Getter
  private CopyListSelectionView<T> copySelectionView =
      new CopyListSelectionView<>(Integer.MAX_VALUE);

  private BooleanProperty isEmpty = new SimpleBooleanProperty(false);

  /** 复制对话框. */
  public CopyDialog() {
    getDialogPane().setContent(copySelectionView);
    initImpl();
    setResultConverter(
        (dialogButton) -> {
          ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
          if (data == ButtonData.OK_DONE) {
            return copySelectionView.getTargetItems();
          }
          return null;
        });
  }

  private void initImpl() {

    isEmpty.bind(Bindings.isEmpty(copySelectionView.getTargetItems()));

    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
    dialogPane.lookupButton(ButtonType.OK).disableProperty().bind(isEmpty);
  }
}
