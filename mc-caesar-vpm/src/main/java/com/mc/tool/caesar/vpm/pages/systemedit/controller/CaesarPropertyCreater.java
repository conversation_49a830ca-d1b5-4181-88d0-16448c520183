package com.mc.tool.caesar.vpm.pages.systemedit.controller;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.Console.SuspendPosition;
import com.mc.tool.caesar.api.CaesarExtArg;
import com.mc.tool.caesar.api.CaesarExtArgUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.Version;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderNetworkInfo;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.MultiviewOutputMode;
import com.mc.tool.caesar.api.datamodel.MultiviewSource;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.extargs.DhdmiSelection;
import com.mc.tool.caesar.api.datamodel.extargs.ExtAudioTrigger;
import com.mc.tool.caesar.api.datamodel.extargs.ExtDpMode;
import com.mc.tool.caesar.api.datamodel.extargs.ExtUartBaudRate;
import com.mc.tool.caesar.api.datamodel.extargs.ExtVideoQp;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.Nameable;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarCpuTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarGridLineTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarTerminalBase;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbRxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarUsbTxTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarVideoWallFunc;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.CpuGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.VpGroup;
import com.mc.tool.caesar.vpm.pages.systemedit.menu.usb.UsbBindingConfigBean.UsbType;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarAnalogAudioInputSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarConPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarCpuPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarCpuVirtualOutInSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarEdidSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarExtArgSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarExtBooleanArgSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarExtTouchEnableSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarExtUsbDiskEnableSender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarMultiviewLayoutPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarMultiviewOutputModePropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarProperty;
import com.mc.tool.caesar.vpm.pages.systemedit.property.CaesarTxRxGroupPropertySender;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixItem;
import com.mc.tool.caesar.vpm.pages.systemedit.topological.data.GridMatrixLink;
import com.mc.tool.caesar.vpm.pages.systemedit.view.OffsetSettingsDialog;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.view.NetworkConfigInfo;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import java.beans.PropertyChangeListener;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.Property;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.scene.Node;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.DialogEx;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.stage.FileChooser;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Window;
import javafx.util.StringConverter;
import javafx.util.converter.NumberStringConverter;
import javax.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.ChoicesPropertyItem;
import org.controlsfx.control.EnumPropertyItem;
import org.controlsfx.control.ListPropertyItem;
import org.controlsfx.control.NumberPropertyItem;
import org.controlsfx.control.ObjectPropertyItem;
import org.controlsfx.control.PropertySheet;
import org.controlsfx.control.PropertySheet.Item;
import org.controlsfx.control.SpinnerPropertyItem;
import org.controlsfx.control.StringObjectPropertyItem;
import org.controlsfx.control.StringPropertyItem;
import org.controlsfx.control.TextBlockPropertyItem;
import org.jetbrains.annotations.NotNull;

/**
 * .
 */
@Slf4j
public class CaesarPropertyCreater {
  private final CaesarDeviceController deviceController;
  private final Entity entity;
  private final ObservableList<PropertySheet.Item> cpuProperties =
      FXCollections.observableArrayList();
  private final ObservableList<PropertySheet.Item> conProperties =
      FXCollections.observableArrayList();
  private final ObservableList<PropertySheet.Item> vpConProperties =
      FXCollections.observableArrayList();
  private final ObservableList<PropertySheet.Item> matrixProperties =
      FXCollections.observableArrayList();
  private final WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * Constructor.
   *
   * @param deviceController device controller
   */
  public CaesarPropertyCreater(CaesarDeviceController deviceController, Entity entity) {
    this.deviceController = deviceController;
    this.entity = entity;
  }

  /**
   * 弹出对话框配置cpu id.
   *
   * @param deviceController device controller
   * @param origin 原始数据
   * @param idProperty id属性
   */
  public static void configCpuId(
      CaesarDeviceController deviceController, String origin, CaesarProperty<Number> idProperty) {
    TextFormatter<String> formatter = new TextFormatter<>(new IdChangeOperator());
    Set<Integer> existId = new HashSet<>();
    for (CpuData data : deviceController.getDataModel().getConfigDataManager().getActiveCpus()) {
      existId.add(data.getId());
    }

    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Version version = deviceController.getDataModel().getConfigMetaData().getUtilVersion();
    DialogEx<String> dialog =
        ViewUtility.getTextDialog(
            applicationBase.getMainWindow(),
            origin,
            formatter,
            (item) -> {
              try {
                int value = Integer.parseInt(item);
                return value >= version.getMinCpuId()
                    && value <= version.getMaxCpuId()
                    && !existId.contains(value);
              } catch (NumberFormatException exception) {
                return false;
              }
            });
    dialog.setContentText(String.format("ID(%d~%d)", version.getMinCpuId(), version.getMaxCpuId()));
    Optional<String> result = dialog.showAndWait();
    result.ifPresent(s -> idProperty.set(Integer.parseInt(s)));
  }

  /**
   * 弹出对话框配置con id.
   *
   * @param deviceController device controller
   * @param origin 原来的数据
   * @param idProperty id属性
   */
  public static void configConId(
      CaesarDeviceController deviceController, String origin, CaesarProperty<Number> idProperty) {
    TextFormatter<String> formatter = new TextFormatter<>(new IdChangeOperator());
    Set<Integer> existId = new HashSet<>();
    for (ConsoleData data :
        deviceController.getDataModel().getConfigDataManager().getActiveConsoles()) {
      existId.add(data.getId());
    }
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Version version = deviceController.getDataModel().getConfigMetaData().getUtilVersion();
    DialogEx<String> dialog =
        ViewUtility.getTextDialog(
            applicationBase.getMainWindow(),
            origin,
            formatter,
            (item) -> {
              try {
                int value = Integer.parseInt(item);
                return value >= version.getMinConId()
                    && value <= version.getMaxConId()
                    && !existId.contains(value);
              } catch (NumberFormatException exception) {
                return false;
              }
            });
    dialog.setContentText(String.format("ID(%d~%d)", version.getMinConId(), version.getMaxConId()));
    Optional<String> result = dialog.showAndWait();
    result.ifPresent(s -> idProperty.set(Integer.parseInt(s)));
  }

  protected Collection<Item> createCpuExtraProperties(
      CaesarMatrix matrix, CaesarCpuTerminal cpuTerminal, Version version) {
    cpuProperties.clear();
    CpuData cpuData = cpuTerminal.getCpuData();
    if (!cpuData.isStatusActive()) {
      return cpuProperties;
    }
    List<String> existingCpuNames =
        deviceController.getDataModel().getConfigDataManager().getCpus().stream()
            .map(CpuData::getName)
            .collect(Collectors.toList());

    CaesarUserRightGetter userRightGetter = deviceController.getCaesarUserRight();
    // 名称
    CaesarCpuPropertySender<String> namePropertySender =
        new CaesarCpuPropertySender<>(deviceController, cpuData, CpuData.PROPERTY_NAME);
    CaesarProperty<String> nameProperty =
        new CaesarProperty<>(cpuData.getNameProperty(), namePropertySender);
    TextBlockPropertyItem nameItem = new TextBlockPropertyItem(nameProperty, true);
    nameItem.setName(NbBundle.getMessage("name"));
    nameItem.setEditable(
        userRightGetter.isSystemEditRenamable() && userRightGetter.isTxConfigEditable());
    existingCpuNames.remove(nameProperty.get());
    nameItemAction(nameProperty, nameItem, existingCpuNames);
    cpuProperties.add(nameItem);

    // ID
    CaesarCpuPropertySender<Number> idPropertySender =
        new CaesarCpuPropertySender<>(deviceController, cpuData, CpuData.PROPERTY_ID);
    CaesarProperty<Number> idProperty =
        new CaesarProperty<>(cpuData.getIdProperty(), idPropertySender);
    ObjectProperty<String> idStringProperty = new IdStringObjectProperty(idProperty);
    Bindings.bindBidirectional(idStringProperty, idProperty, new NumberStringConverter("0"));

    TextBlockPropertyItem idItem = new TextBlockPropertyItem(idStringProperty, true);
    idItem.setTextAction(
        (event) -> {
          if (changeIdWarning()) {
            configCpuId(deviceController, idStringProperty.get(), idProperty);
          }
        });
    idProperty.addListener(
        (observable, oldVal, newVal) -> {
          if (newVal.intValue() != 0 && oldVal.intValue() != 0 && !newVal.equals(oldVal)) {
            refresh();
          }
        });
    idItem.setName(NbBundle.getMessage("id"));
    int count = 0;
    for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
      if (cpuData.getExtender(i) > 0) {
        count++;
      }
    }
    idItem.setEditable(userRightGetter.isTxConfigEditable() && count < 2);
    cpuProperties.add(idItem);

    // virtual
    CaesarCpuVirtualOutInSender virtualOutPropertySender =
        new CaesarCpuVirtualOutInSender(
            deviceController, cpuData, CpuData.PROPERTY_STATUS_VIRTUAL_OUT);
    CaesarProperty<Boolean> virtualOutProperty =
        new CaesarProperty<>(cpuData.getVirtualOutProperty(), virtualOutPropertySender);
    ObjectPropertyItem<Boolean> virtualOutItem =
        new ObjectPropertyItem<>(virtualOutProperty, Boolean.class, true);
    virtualOutItem.setName(NbBundle.getMessage("virtual"));
    virtualOutItem.setEditable(userRightGetter.isTxConfigEditable());
    cpuProperties.add(virtualOutItem);

    if (cpuData.getExtenderData(0) == null) {
      return cpuProperties;
    }
    // 外设名称
    CaesarCpuPropertySender<String> extenderNamePropertySender =
        new CaesarCpuPropertySender<>(deviceController, cpuData, ExtenderData.PROPERTY_NAME);
    CaesarProperty<String> extenderNameProperty =
        new CaesarProperty<>(
            cpuData.getExtenderData(0).getNameProperty(), extenderNamePropertySender);
    StringObjectPropertyItem extenderNameItem =
        new StringObjectPropertyItem(extenderNameProperty, true);
    extenderNameItem.setName(NbBundle.getMessage("extenderName"));
    extenderNameItem.setEditable(false);
    cpuProperties.add(extenderNameItem);

    // 扩展单元序列号
    ExtenderData extenderData = cpuData.getExtenderData(0);
    StringPropertyItem serialNumberItem =
        new StringPropertyItem(new SimpleStringProperty(extenderData.getSerial()));
    serialNumberItem.setName(NbBundle.getMessage("serial"));
    serialNumberItem.setEditable(false);
    cpuProperties.add(serialNumberItem);

    // 扩展单元端口
    StringPropertyItem portItem = new StringPropertyItem(extenderData.getPortProperty());
    portItem.setName(NbBundle.getMessage("extenderPort"));
    portItem.setEditable(false);
    cpuProperties.add(portItem);

    // USB使能
    if (CaesarExtArgUtility.checkUsbEnableArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_USB_ENABLE_ID)) {
      ObjectPropertyItem<Boolean> usbDiskEnableItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_USB_ENABLE_ID,
              extenderData.getUsbDiskEnableProperty(), Boolean.class,
              NbBundle.getMessage("usbConType"),
              ExtenderData.PROPERTY_STATUS_USB_DISK_ENABLE,
              userRightGetter, null);
      cpuProperties.add(usbDiskEnableItem);
    }

    // 触摸板使能
    if (CaesarExtArgUtility.checkTouchingScreenIdArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID)) {
      CaesarExtTouchEnableSender touchEnableSender =
          new CaesarExtTouchEnableSender(
              deviceController, cpuData.getExtenderData(0), ExtenderData.PROPERTY_TOUCHING_SCREEN);
      Function<Boolean, Boolean> fn =
          parameter -> {
            ApplicationBase applicationBase =
                InjectorProvider.getInjector().getInstance(ApplicationBase.class);
            UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
            alert.initOwner(applicationBase.getMainWindow());
            alert.setHeaderText(null);
            alert.getButtonTypes().add(ButtonType.CANCEL);
            alert.setTitle(NbBundle.getMessage("txTouchEnable.alert.title"));
            alert.setContentText(NbBundle.getMessage("txTouchEnable.alert.content"));
            Optional<ButtonType> result = alert.showAndWait();
            return result.isPresent() && result.get() == ButtonType.OK;
          };
      CaesarProperty<Boolean> touchEnableProperty =
          new CaesarProperty<>(
              cpuData.getExtenderData(0).getTouchEnableProperty(), touchEnableSender, fn);
      ObjectPropertyItem<Boolean> touchEnableItem =
          new ObjectPropertyItem<>(touchEnableProperty, Boolean.class, true);
      touchEnableItem.setName(NbBundle.getMessage("txTouchEnable"));
      touchEnableItem.setEditable(userRightGetter.isTxConfigEditable());
      cpuProperties.add(touchEnableItem);
    }
    // 图像量化因子
    if (CaesarExtArgUtility.checkVideoQpArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_VIDEO_QP_ID)) {
      ObjectPropertyItem<ExtVideoQp> videoQpItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_VIDEO_QP_ID,
              extenderData.getVideoQpProperty(), ExtVideoQp.class,
              NbBundle.getMessage("txVideoQp"),
              ExtenderData.PROPERTY_VIDEO_QP,
              userRightGetter, null);
      cpuProperties.add(videoQpItem);
    }
    // 高压缩率
    if (CaesarExtArgUtility.checkHighCompressionRatioArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO)) {
      ObjectPropertyItem<Boolean> highCompressionRatioItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO,
              extenderData.getHighCompressionRatioProperty(), Boolean.class,
              NbBundle.getMessage("txHighCompressionRatio"),
              ExtenderData.PROPERTY_HIGH_COMPRESSION_RATIO,
              userRightGetter, null);
      cpuProperties.add(highCompressionRatioItem);
    }

    // RX MODE

    ConsoleData consoleData = cpuData.getConsoleData();
    if (consoleData != null) {
      if (cpuData.isStatusPrivate()) {
        StringPropertyItem rxItem =
            new StringPropertyItem(new SimpleStringProperty(NbBundle.getMessage("mode.private")));
        rxItem.setName(consoleData.getName());
        rxItem.setEditable(false);
        cpuProperties.add(rxItem);
      } else {
        StringPropertyItem rxItem =
            new StringPropertyItem(new SimpleStringProperty(NbBundle.getMessage("mode.full")));
        rxItem.setName(consoleData.getName());
        rxItem.setEditable(false);
        cpuProperties.add(rxItem);
      }
    }

    for (ConsoleData conData : cpuData.getConfigDataManager().getActiveConsoles()) {
      if (conData == cpuData.getConsoleData()) {
        continue;
      }
      CpuData cpu = conData.getCpuData();
      if (cpu != null) {
        if (cpu.isVirtual()) {
          cpu = cpu.getRealCpuData();
        }
        if (cpu != null && cpu.equals(cpuData)) {
          if (conData.isStatusVideoOnly()) {
            StringPropertyItem rxItem =
                new StringPropertyItem(new SimpleStringProperty(NbBundle.getMessage("mode.video")));
            rxItem.setName(conData.getName());
            rxItem.setEditable(false);
            cpuProperties.add(rxItem);
          } else {
            StringPropertyItem rxItem =
                new StringPropertyItem(
                    new SimpleStringProperty(NbBundle.getMessage("mode.shared")));
            rxItem.setName(conData.getName());
            rxItem.setEditable(false);
            cpuProperties.add(rxItem);
          }
        }
      }
    }

    cpuProperties.addAll(createExtenderStatusProperty(deviceController, extenderData));

    List<ExtenderData> extenders = new ArrayList<>(cpuData.getExtenderDatas());
    extenders.remove(extenderData);
    cpuProperties.addAll(createExtenderBindingProperty(extenders));

    if (deviceController.getDataModel().getConfigMetaData().getUtilVersion().hasTxOffset()) {
      // TX偏移属性
      TextBlockPropertyItem offsetItem =
          new TextBlockPropertyItem(
              new SimpleObjectProperty<>(
                  cpuData.getScreenOffsetConfig().getActiveOffset() == 1
                      ? NbBundle.getMessage("txOffset.activated")
                      : NbBundle.getMessage("txOffset.unactivated")),
              true);
      offsetItem.setName(NbBundle.getMessage("offset"));
      offsetItem.setEditable(userRightGetter.isTxConfigEditable());
      offsetItem.setTextAction(
          (event) -> {
            ApplicationBase applicationBase =
                InjectorProvider.getInjector().getInstance(ApplicationBase.class);
            OffsetSettingsDialog dialog = new OffsetSettingsDialog(cpuTerminal);
            dialog.setDeviceController(deviceController);
            dialog.initOwner(applicationBase.getMainWindow());
            Optional<String> result = dialog.showAndWait();
            result.ifPresent(offsetItem::setValue);
          });
      cpuProperties.add(offsetItem);
    }

    // 音频触发
    if (CaesarExtArgUtility.checkAudioTriggerArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER)) {
      ObjectPropertyItem<ExtAudioTrigger> audioTrigger =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_AUDIO_TRIGGER,
              extenderData.getAudioTriggerProperty(), ExtAudioTrigger.class,
              NbBundle.getMessage("txAudioTrigger"),
              ExtenderData.PROPERTY_AUDIO_TRIGGER,
              userRightGetter, null);
      cpuProperties.add(audioTrigger);
    }

    // (音频)触发保持时间
    if (CaesarExtArgUtility.checkTriggerHoldTimeArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME)) {
      NumberPropertyItem triggerHoldTimeItem =
          createNumberExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME,
              extenderData.getTriggerHoldTimeProperty(),
              Integer.class,
              NbBundle.getMessage("txTriggerHoldTime"),
              ExtenderData.PROPERTY_TRIGGER_HOLD_TIME,
              userRightGetter);
      triggerHoldTimeItem.setValueRange(0, 65000);
      cpuProperties.add(triggerHoldTimeItem);
    }

    cpuProperties.addAll(createExtCommonArgProperty(extenderData, version));
    return cpuProperties;
  }

  private Collection<Item> createExtCommonArgProperty(ExtenderData extenderData, Version version) {
    List<Item> result = new ArrayList<>();
    if (CaesarExtArgUtility.checkIcronEnableArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_ICRON_ENABLE)) {
      ObjectPropertyItem<Boolean> icronEnableItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_ICRON_ENABLE,
              extenderData.getIcronEnableProperty(), Boolean.class,
              NbBundle.getMessage("extIcronEnable"),
              ExtenderData.PROPERTY_ICRON_ENABLE,
              deviceController.getCaesarUserRight(), null);
      result.add(icronEnableItem);
    }
    if (CaesarExtArgUtility.checkUartBaudRateArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_UART_BAUD_RATE)) {
      ObjectPropertyItem<ExtUartBaudRate> uartBaudRateItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_UART_BAUD_RATE,
              extenderData.getUartBaudRateProperty(), ExtUartBaudRate.class,
              NbBundle.getMessage("extUartBaudRate"),
              ExtenderData.PROPERTY_UART_BAUDRATE,
              deviceController.getCaesarUserRight(), null);
      result.add(uartBaudRateItem);
    }
    if (CaesarExtArgUtility.checkDoubleDpEnableArgExist(version, extenderData)
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE)) {
      Function<ExtDpMode, Boolean> confirmDpMode = param -> {
        ApplicationBase applicationBase = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
        alert.initOwner(applicationBase.getMainWindow());
        alert.setHeaderText(null);
        alert.setTitle(NbBundle.getMessage("dpModeConfirm.title"));
        alert.setContentText(NbBundle.getMessage("dpModeConfirm.content"));
        alert.getButtonTypes().setAll(ButtonType.OK, ButtonType.CANCEL);
        return alert.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK;
      };
      ObjectPropertyItem<ExtDpMode> dpModeItem =
          createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE,
              extenderData.getDpModeProperty(), ExtDpMode.class,
              NbBundle.getMessage("extDoubleDpEnable"),
              ExtenderData.PROPERTY_DOUBLEDP_ENABLE,
              deviceController.getCaesarUserRight(), confirmDpMode);
      result.add(dpModeItem);
    }
    return result;
  }

  @NotNull
  private <T> ObjectPropertyItem<T> createExtArgProperty(ExtenderData extenderData,
                                                         CaesarExtArg extArg, Property<T> property,
                                                         Class<T> propertyClazz,
                                                         String extArgPropertyTitle,
                                                         String extArgPropertyName,
                                                         CaesarUserRightGetter userRightGetter,
                                                         Function<T, Boolean> confirmFunction) {
    CaesarExtArgSender<T> propertySender =
        new CaesarExtArgSender<>(deviceController, extenderData, extArg.getValue(), extArgPropertyName);
    CaesarProperty<T> caesarProperty;
    if (confirmFunction != null) {
      caesarProperty = new CaesarProperty<>(property, propertySender, confirmFunction);
    } else {
      caesarProperty = new CaesarProperty<>(property, propertySender);
    }
    ObjectPropertyItem<T> propertyItem =
        new ObjectPropertyItem<>(caesarProperty, propertyClazz, true);
    propertyItem.setName(extArgPropertyTitle);
    propertyItem.setEditable(userRightGetter.isTxConfigEditable());
    return propertyItem;
  }

  @NotNull
  private NumberPropertyItem createNumberExtArgProperty(ExtenderData extenderData,
                                                         CaesarExtArg extArg,
                                                         Property<Number> property,
                                                        Class<?> propertyClazz,
                                                         String extArgPropertyTitle,
                                                         String extArgPropertyName,
                                                         CaesarUserRightGetter userRightGetter) {
    CaesarExtArgSender<Number> propertySender =
        new CaesarExtArgSender<>(
            deviceController,
            extenderData,
            extArg.getValue(),
            extArgPropertyName);
    CaesarProperty<Number> caesarProperty =
        new CaesarProperty<>(
            property, propertySender);
    NumberPropertyItem propertyItem =
        new NumberPropertyItem(caesarProperty, propertyClazz, true);
    propertyItem.setName(extArgPropertyTitle);
    propertyItem.setEditable(userRightGetter.isTxConfigEditable());
    return propertyItem;
  }

  protected Collection<Item> createConExtraProperties(
      CaesarMatrix matrix, CaesarConTerminal conTerminal, Version version) {
    ConsoleData consoleData = conTerminal.getConsoleData();
    conProperties.clear();
    if (!consoleData.isStatusActive()) {
      return conProperties;
    }
    List<String> existingConNames =
        deviceController.getDataModel().getConfigDataManager().getActiveConsoles().stream()
            .map(ConsoleData::getName)
            .collect(Collectors.toList());
    CaesarUserRightGetter userRightGetter = deviceController.getCaesarUserRight();
    // 名称
    CaesarConPropertySender<String> namePropertySender =
        new CaesarConPropertySender<>(deviceController, consoleData, ConsoleData.PROPERTY_NAME);
    CaesarProperty<String> nameProperty =
        new CaesarProperty<>(consoleData.getNameProperty(), namePropertySender);
    TextBlockPropertyItem nameItem = new TextBlockPropertyItem(nameProperty, true);
    nameItem.setName(NbBundle.getMessage("name"));
    nameItem.setId("prop-name");
    existingConNames.remove(nameProperty.get());
    nameItemAction(nameProperty, nameItem, existingConNames);
    conProperties.add(nameItem);

    // ID 跨屏下的外设的ID不能修改
    TextBlockPropertyItem idItem = createConsoleIdProperty(consoleData);
    if (conTerminal.getParent() instanceof CrossScreenFunc) {
      idItem.setEditable(false);
    } else {
      int count = 0;
      for (int i = 0; i < CaesarConstants.Extender.CPUCON; i++) {
        if (consoleData.getExtender(i) > 0) {
          count++;
        }
      }
      idItem.setEditable(userRightGetter.isTxConfigEditable() && count < 2);
    }
    conProperties.add(idItem);

    //vp7
    ExtenderData extenderData = consoleData.getExtenderData(0);
    if (consoleData.isStatusVp7()) {
      conProperties.addAll(createVp7Property(deviceController, consoleData, extenderData));
      return conProperties;
    }

    // 预览终端
    if (extenderData != null
        && extenderData.getExtenderStatusInfo().getSpecialExtType()
        == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW) {
      conProperties.addAll(createPreviewProperty(deviceController, consoleData, extenderData));
      return conProperties;
    }

    if (!consoleData.isStatusVpcon()
        && deviceController
            .getCaesarUserRight()
            .isPageVisible(CaesarPermissionConfigurationPage.NAME)) {
      // 允许用户ACL
      CaesarConPropertySender<Boolean> allowLoginPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_ALLOWLOGIN);
      CaesarProperty<Boolean> allowLoginProperty =
          new CaesarProperty<>(consoleData.getAllowLoginProperty(), allowLoginPropertySender);
      ObjectPropertyItem<Boolean> allowLoginItem =
          new ObjectPropertyItem<>(allowLoginProperty, Boolean.class, true);
      allowLoginItem.setName(NbBundle.getMessage("AllowLogin"));
      allowLoginItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(allowLoginItem);

      // 强制登陆
      CaesarConPropertySender<Boolean> forceLoginPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_FORCELOGIN);
      CaesarProperty<Boolean> forceLoginProperty =
          new CaesarProperty<>(consoleData.getForceLoginProperty(), forceLoginPropertySender);
      ObjectPropertyItem<Boolean> forceLoginItem =
          new ObjectPropertyItem<>(forceLoginProperty, Boolean.class, true);
      forceLoginItem.setName(NbBundle.getMessage("ForceLogin"));
      forceLoginItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(forceLoginItem);

      // LOS帧
      CaesarConPropertySender<Boolean> losFramePropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_LOSFRAME);
      CaesarProperty<Boolean> losFrameProperty =
          new CaesarProperty<>(consoleData.getLosFrameProperty(), losFramePropertySender);
      ObjectPropertyItem<Boolean> losFrameItem =
          new ObjectPropertyItem<>(losFrameProperty, Boolean.class, true);
      losFrameItem.setName(NbBundle.getMessage("losFrame"));
      losFrameItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(losFrameItem);

      // 允许CPU接入端轮询
      CaesarConPropertySender<Boolean> allowScanPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_ALLOWSCAN);
      CaesarProperty<Boolean> allowScanProperty =
          new CaesarProperty<>(consoleData.getAllowScanProperty(), allowScanPropertySender);
      ObjectPropertyItem<Boolean> allowScanItem =
          new ObjectPropertyItem<>(allowScanProperty, Boolean.class, true);
      allowScanItem.setName(NbBundle.getMessage("AllowScan"));
      allowScanItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(allowScanItem);

      // 强制CPU接入端轮询
      CaesarConPropertySender<Boolean> forceScanPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_FORCESCAN);
      CaesarProperty<Boolean> forceScanProperty =
          new CaesarProperty<>(consoleData.getForceScanProperty(), forceScanPropertySender);
      ObjectPropertyItem<Boolean> forceScanItem =
          new ObjectPropertyItem<>(forceScanProperty, Boolean.class, true);
      forceScanItem.setName(NbBundle.getMessage("ForceScan"));
      forceScanItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(forceScanItem);

      // 轮询时间
      CaesarConPropertySender<Number> scanTimePropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_SCAN_TIME);
      CaesarProperty<Number> scanTimeProperty =
          new CaesarProperty<>(consoleData.getScanTimeProperty(), scanTimePropertySender);
      NumberPropertyItem scanTimeItem =
          new NumberPropertyItem(scanTimeProperty, Integer.class, true);
      scanTimeItem.setName(NbBundle.getMessage("scanTime"));
      scanTimeItem.setValueRange(2, 999);
      scanTimeItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(scanTimeItem);
    }

    // 扩展单元名称
    if (extenderData == null) {
      return conProperties;
    }
    CaesarConPropertySender<String> extenderNamePropertySender =
        new CaesarConPropertySender<>(deviceController, consoleData, ExtenderData.PROPERTY_NAME);
    CaesarProperty<String> extenderNameProperty =
        new CaesarProperty<>(extenderData.getNameProperty(), extenderNamePropertySender);
    StringObjectPropertyItem extenderNameItem =
        new StringObjectPropertyItem(extenderNameProperty, true);
    extenderNameItem.setName(NbBundle.getMessage("extenderName"));
    extenderNameItem.setEditable(false);
    conProperties.add(extenderNameItem);

    if (!consoleData.isStatusVpcon()) {
      // 扩展单元序列号
      StringPropertyItem serialNumberItem =
          new StringPropertyItem(new SimpleStringProperty(extenderData.getSerial()));
      serialNumberItem.setName(NbBundle.getMessage("serial"));
      serialNumberItem.setEditable(false);
      conProperties.add(serialNumberItem);
    }

    // 扩展单元端口
    StringPropertyItem portItem = new StringPropertyItem(extenderData.getPortProperty());
    portItem.setName(NbBundle.getMessage("extenderPort"));
    portItem.setEditable(false);
    conProperties.add(portItem);

    if (!consoleData.isStatusVpcon()) {
      if (CaesarExtArgUtility.checkUsbEnableArgExist(version, extenderData)
          && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_USB_ENABLE_ID)) {
        // USB使能
        CaesarExtUsbDiskEnableSender usbDiskEnablePropertySender =
            new CaesarExtUsbDiskEnableSender(
                deviceController,
                consoleData.getExtenderData(0),
                ExtenderData.PROPERTY_STATUS_USB_DISK_ENABLE);
        CaesarProperty<Boolean> usbDiskEnableProperty =
            new CaesarProperty<>(
                extenderData.getUsbDiskEnableProperty(), usbDiskEnablePropertySender);
        ObjectPropertyItem<Boolean> usbDiskEnableItem =
            new ObjectPropertyItem<>(usbDiskEnableProperty, Boolean.class, true);
        usbDiskEnableItem.setName(NbBundle.getMessage("usbConType"));
        usbDiskEnableItem.setEditable(userRightGetter.isRxConfigEditable());
        conProperties.add(usbDiskEnableItem);
      }

      // HDMI选择,只有单接口RX才有
      if (extenderData.getExtenderStatusInfo().getInterfaceCount() == 1) {
        ObservableList<DhdmiSelection> listChoices = FXCollections.observableArrayList();
        listChoices.addAll(DhdmiSelection.HDMI1, DhdmiSelection.HDMI2);
        CaesarProperty<DhdmiSelection> dhdmiSelectionProperty =
            new CaesarProperty<>(
                consoleData.cpuHdmiProperty(),
                new CaesarConPropertySender<>(
                    deviceController, consoleData, ConsoleData.PROPERTY_CPU_HDMI));
        ChoicesPropertyItem<DhdmiSelection> dhdmiSelectionItem =
            new ChoicesPropertyItem<>(
                dhdmiSelectionProperty, DhdmiSelection.class, listChoices, true);
        dhdmiSelectionItem.setConverter(new DhdmiSelectionStringConverter());
        dhdmiSelectionItem.setName(NbBundle.getMessage("hdmiSelection"));
        dhdmiSelectionItem.setEditable(
            userRightGetter.isRxConfigEditable() && isTxHasDualHdmi(consoleData));
        conProperties.add(dhdmiSelectionItem);
        deviceController
            .getDataModel()
            .addPropertyChangeListener(
                ConsoleData.PROPERTY_CPU,
                weakAdapter.wrap(
                    (PropertyChangeListener)
                        evt -> {
                          if (evt.getNewValue().equals(0)) {
                            PlatformUtility.runInFxThread(
                                () -> dhdmiSelectionProperty.set(DhdmiSelection.HDMI1));
                          }
                          dhdmiSelectionItem.setEditable(
                              userRightGetter.isRxConfigEditable() && isTxHasDualHdmi(consoleData));
                        }));
      }
      // analog audio
      if (extenderData.getExtenderStatusInfo().getHwSpecial().isMicIn()
          && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID)) {
        CaesarAnalogAudioInputSender analogAudioInputSender =
            new CaesarAnalogAudioInputSender(
                deviceController,
                consoleData.getExtenderData(0),
                ExtenderData.PROPERTY_ANALOG_AUDIO_INPUT);
        CaesarProperty<ExtenderAnalogAudioInput> analogAudioInputProperty =
            new CaesarProperty<>(
                extenderData.getAnalogAudioInputProperty(), analogAudioInputSender);
        EnumPropertyItem<ExtenderAnalogAudioInput> analogAudioInputItem =
            new EnumPropertyItem<>(
                analogAudioInputProperty, ExtenderAnalogAudioInput.class, null, true);
        analogAudioInputItem.setName(NbBundle.getMessage("analog_audio"));
        analogAudioInputItem.setEditable(userRightGetter.isRxConfigEditable());
        conProperties.add(analogAudioInputItem);
      }
    }

    // CPU连接
    StringPropertyItem cpuNameItem;
    CpuData cpuData = consoleData.getCpuData();
    if (cpuData != null) {
      String mode;
      if (consoleData.equals(cpuData.getConsoleData())) {
        if (consoleData.isStatusPrivate()) {
          mode = NbBundle.getMessage("mode.private");
        } else {
          mode = NbBundle.getMessage("mode.full");
        }
      } else if (!consoleData.isStatusVideoOnly()) {
        mode = NbBundle.getMessage("mode.shared");
      } else {
        mode = NbBundle.getMessage("mode.video");
      }
      cpuNameItem = new StringPropertyItem(new ReadOnlyStringWrapper(mode));
      cpuNameItem.setName(cpuData.getName());
      cpuNameItem.setEditable(false);
      conProperties.add(cpuNameItem);
    }

    conProperties.addAll(createExtenderStatusProperty(deviceController, extenderData));
    if (!consoleData.isStatusVpcon()) {
      List<ExtenderData> extenders = new ArrayList<>(consoleData.getExtenderDatas());
      extenders.remove(extenderData);
      conProperties.addAll(createExtenderBindingProperty(extenders));
    }

    if (!consoleData.isStatusVpcon()
        && deviceController.getCaesarUserRight().isOsdConfigVisible()) {
      // OSD透明度
      CaesarConPropertySender<Number> osdTspyPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_OSD_TRANSPARENCY);
      CaesarProperty<Number> osdTspyProperty =
          new CaesarProperty<>(consoleData.getOsdTransparencyProperty(), osdTspyPropertySender);
      NumberPropertyItem osdTspyItem = new NumberPropertyItem(osdTspyProperty, Integer.class, true);
      osdTspyItem.setName(NbBundle.getMessage("osdTspy"));
      osdTspyItem.setValueRange(0, 170);
      osdTspyItem.setDescription(osdTspyItem.getName());
      osdTspyItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(osdTspyItem);

      // osd菜单
      if (CaesarExtArgUtility.checkOsdMenuArgExist(version, extenderData)
          && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_OSD_MENU)) {
        ObjectPropertyItem<Boolean> osdMenuItem =
            createExtArgProperty(extenderData, CaesarExtArg.EXT_ARG_OSD_MENU,
                extenderData.getOsdMenuProperty(), Boolean.class, NbBundle.getMessage("rxOsdMenu"),
                ExtenderData.PROPERTY_OSD_MENU, userRightGetter, null);
        conProperties.add(osdMenuItem);
      }

      // 悬浮窗开启
      CaesarConPropertySender<Boolean> suspendEnablePropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_STATUS_SUSPENDENABLE);
      CaesarProperty<Boolean> suspendEnableProperty =
          new CaesarProperty<>(consoleData.getSuspendEnableProperty(), suspendEnablePropertySender);
      ObjectPropertyItem<Boolean> suspendEnableItem =
          new ObjectPropertyItem<>(suspendEnableProperty, Boolean.class, true);
      suspendEnableItem.setName(NbBundle.getMessage("suspendEnable"));
      suspendEnableItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(suspendEnableItem);

      // 悬浮窗位置
      ObservableList<SuspendPosition> listChoices = FXCollections.observableArrayList();
      listChoices.addAll(
          SuspendPosition.TOPCENTER, SuspendPosition.TOPLEFT, SuspendPosition.TOPRIGHT);
      CaesarConPropertySender<SuspendPosition> suspendPosPropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_SUSPEND_POSITION);
      CaesarProperty<SuspendPosition> suspendPosProperty =
          new CaesarProperty<>(consoleData.getSuspendPositionProperty(), suspendPosPropertySender);
      ChoicesPropertyItem<SuspendPosition> suspendPosItem =
          new ChoicesPropertyItem<>(suspendPosProperty, SuspendPosition.class, listChoices, true);
      suspendPosItem.setConverter(new SuspendPosStringConverter());
      suspendPosItem.setName(NbBundle.getMessage("suspendPos"));
      suspendPosItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(suspendPosItem);

      // 悬浮窗显示时间
      CaesarConPropertySender<Number> suspendTimePropertySender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_SUSPEND_TIME);
      CaesarProperty<Number> suspendTimeProperty =
          new CaesarProperty<>(consoleData.getSuspendTimeProperty(), suspendTimePropertySender);
      NumberPropertyItem suspendTimeItem =
          new NumberPropertyItem(suspendTimeProperty, Integer.class, true);
      suspendTimeItem.setName(NbBundle.getMessage("suspendTime"));
      suspendTimeItem.setValueRange(0, Integer.MAX_VALUE);
      suspendTimeItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(suspendTimeItem);

      // 鼠标速度
      CaesarConPropertySender<Number> mouseSpeedSender =
          new CaesarConPropertySender<>(
              deviceController, consoleData, ConsoleData.PROPERTY_MOUSE_SPEED);
      CaesarProperty<Number> mouseSpeedProperty =
          new CaesarProperty<>(consoleData.getMouseSpeedProperty(), mouseSpeedSender);
      SpinnerPropertyItem mouseSpeedItem =
          new SpinnerPropertyItem(mouseSpeedProperty, Integer.class, true);
      mouseSpeedItem.setName(NbBundle.getMessage("mouseSpeed"));
      mouseSpeedItem.setValueRange(4, 16);
      mouseSpeedItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(mouseSpeedItem);
    }

    // 显示模式
    // todo 显示模式
    //    if (extenderData.getExtenderStatusInfo().getVideoResolutionType().equals(
    //        CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ)) {
    //    }

    if (consoleData.isMultiview()) {
      MultiviewData multiviewData = consoleData.getMultiviewData();
      if (multiviewData != null) {
        // tx连接
        ObservableList<String> strList = FXCollections.observableArrayList();
        for (int i = 0; i < multiviewData.getSourceCount(); i++) {
          MultiviewSource source = multiviewData.getSource(i);
          if (source != null) {
            CpuData sourceData = source.getCpuData();
            if (sourceData != null) {
              strList.add(sourceData.getName());
            }
          }
        }
        ListPropertyItem statusItem = new ListPropertyItem(strList);
        statusItem.setName(NbBundle.getMessage("peer_tx.name"));
        statusItem.setEditable(false);
        conProperties.add(statusItem);

        // 四画面布局
        ObservableList<MultiviewLayoutType> listChoices = FXCollections.observableArrayList();
        listChoices.addAll(MultiviewLayoutType.FULL_SCREEN, MultiviewLayoutType.TWO_GRID, MultiviewLayoutType.FOUR_GRID, MultiviewLayoutType.SIDE_BAR);
        CaesarMultiviewLayoutPropertySender<MultiviewLayoutType> multiviewLayoutPropertySender =
            new CaesarMultiviewLayoutPropertySender<>(
                deviceController, consoleData, MultiviewData.PROPERTY_LAYOUT_TYPE);
        CaesarProperty<MultiviewLayoutType> multiviewLayoutProperty =
            new CaesarProperty<>(multiviewData.getLayoutTypeProperty(), multiviewLayoutPropertySender);
        ChoicesPropertyItem<MultiviewLayoutType> multiviewLayoutItem =
            new ChoicesPropertyItem<>(multiviewLayoutProperty, MultiviewLayoutType.class, listChoices, true);
        multiviewLayoutItem.setConverter(new NameableStringConverter());
        multiviewLayoutItem.setName(NbBundle.getMessage("multiviewLayout"));
        multiviewLayoutItem.setEditable(userRightGetter.isRxConfigEditable());
        conProperties.add(multiviewLayoutItem);
      }
    }

    // 事件联动
    if (!consoleData.isStatusVpcon()
        && extenderData.hasExtArg(CaesarExtArg.EXT_ARG_EVENT_ENABLE)) {
      CaesarExtBooleanArgSender eventEnableSender =
          new CaesarExtBooleanArgSender(
              deviceController,
              consoleData.getExtenderData(0),
              CaesarExtArg.EXT_ARG_EVENT_ENABLE.getValue(),
              ExtenderData.PROPERTY_EVENT_ENABLE);
      CaesarProperty<Boolean> eventEnableProperty =
          new CaesarProperty<>(
              consoleData.getExtenderData(0).getEventEnableProperty(), eventEnableSender);
      ObjectPropertyItem<Boolean> eventEnableItem =
          new ObjectPropertyItem<>(eventEnableProperty, Boolean.class, true);
      eventEnableItem.setName(NbBundle.getMessage("txEventEnable"));
      eventEnableItem.setEditable(userRightGetter.isRxConfigEditable());
      conProperties.add(eventEnableItem);
    }
    conProperties.addAll(createExtCommonArgProperty(extenderData, version));
    return conProperties;
  }

  private boolean isTxHasDualHdmi(ConsoleData consoleData) {
    CpuData cpuData = consoleData.getCpuData();
    if (cpuData != null && cpuData.getExtenderData(0) != null
        && cpuData.getExtenderData(0).getExtenderStatusInfo() != null) {
      return cpuData.getExtenderData(0).getExtenderStatusInfo().getInterfaceCount() > 1;
    }
    return false;
  }

  protected static Collection<PropertySheet.Item> createExtenderBindingProperty(
      Collection<ExtenderData> extenderDatas) {
    final List<PropertySheet.Item> result = new ArrayList<>();

    final String format = "%s|%s|%d";
    final String usbType = "USB";
    final String unknownType = "UNKNOWN";

    List<String> list = new ArrayList<>();
    for (ExtenderData data : extenderDatas) {
      if (data == null) {
        continue;
      }
      String type = unknownType;
      if (data.isUsbCpuType() || data.isUsbConType()) {
        type = usbType;
      }
      String item = String.format(format, type, data.getName(), data.getPort());
      list.add(item);
    }

    ListPropertyItem item = new ListPropertyItem(FXCollections.observableArrayList(list));
    item.setName(Bundle.NbBundle.getMessage("extender_status.extender_binding2"));
    item.setEditable(false);
    result.add(item);
    return result;
  }

  protected static Collection<PropertySheet.Item> createVp7Property(
      CaesarDeviceController deviceController, ConsoleData consoleData, ExtenderData extenderData) {
    List<PropertySheet.Item> result = new ArrayList<>();
    StringPropertyItem subType =
        new StringPropertyItem(new ReadOnlyStringWrapper(extenderData.isStatusActive()
            ? NbBundle.getMessage("extender_status.online")
            : NbBundle.getMessage("extender_status.offline")));
    subType.setName(NbBundle.getMessage("vp.status"));
    subType.setEditable(false);
    result.add(subType);

    // 接口模式
    MultiviewData multiviewData = consoleData.getMultiviewData();
    if (multiviewData != null) {
      ObservableList<MultiviewOutputMode> listChoices = FXCollections.observableArrayList();
      listChoices.addAll(MultiviewOutputMode.FOUR_INTERFACE, MultiviewOutputMode.TWO_INTERFACE,
          MultiviewOutputMode.ONE_INTERFACE);
      CaesarMultiviewOutputModePropertySender<MultiviewOutputMode>
          multiviewOutputModePropertySender =
          new CaesarMultiviewOutputModePropertySender<>(
              deviceController, consoleData, MultiviewData.PROPERTY_OUTPUT_MODE);
      CaesarProperty<MultiviewOutputMode> multiviewLayoutProperty =
          new CaesarProperty<>(multiviewData.getOutputModeProperty(),
              multiviewOutputModePropertySender);
      ChoicesPropertyItem<MultiviewOutputMode> multiviewOutputModeItem =
          new ChoicesPropertyItem<>(multiviewLayoutProperty, MultiviewOutputMode.class, listChoices,
              true);
      multiviewOutputModeItem.setConverter(new NameableStringConverter<>());
      multiviewOutputModeItem.setName(NbBundle.getMessage("multiviewOutputMode"));
      multiviewOutputModeItem.setEditable(
          deviceController.getCaesarUserRight().isRxConfigEditable());
      result.add(multiviewOutputModeItem);
    }

    // 通道分辨率
    try {
      VpConsoleData.VpResolution[] vpconResolution = deviceController.submit(
          () -> deviceController.getDataModel().getVpconResolution(extenderData)).get();
      int length = vpconResolution.length;
      for (int i = 4; i < length; i++) {
        StringPropertyItem item = new StringPropertyItem(
            new ReadOnlyStringWrapper(vpconResolution[i].getText()));
        item.setName(NbBundle.getMessage("vp.channel") + (i - 4 + 1));
        item.setEditable(false);
        result.add(item);
      }
    } catch (ExecutionException | InterruptedException ex) {
      log.error("Failed to get resolution", ex);
    }

    try {
      ExtenderNetworkInfo extNetworkInfo = deviceController.submit(
          () -> deviceController.getDataModel().getExtNetworkInfo(extenderData)).get();
      // ip
      SimpleObjectProperty<String> ipProperty =
          new SimpleObjectProperty<>(IpUtil.getAddressString(extNetworkInfo.getAddress()));
      TextBlockPropertyItem ipItem = new TextBlockPropertyItem(ipProperty);
      netItemAction(ipItem, extNetworkInfo, (data, networkInfo) -> {
        try {
          deviceController.submit(() -> {
            try {
              deviceController.getDataModel().setExtNetworkInfo(data, networkInfo);
            } catch (BusyException | ConfigException | DeviceConnectionException ex) {
              log.error("Fail to set extender network info!", ex);
            }
          }).get();
        } catch (Exception ex) {
          log.error("Fail to set extender network info!", ex);
        }
      }, extenderData);
      ipItem.setName(NbBundle.getMessage("vp.ip"));
      result.add(ipItem);
      // 子网掩码
      StringPropertyItem netmaskItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(IpUtil.getAddressString(extNetworkInfo.getNetmask())));
      netmaskItem.setName(NbBundle.getMessage("vp.netmask"));
      netmaskItem.setEditable(false);
      result.add(netmaskItem);
      // 网关
      StringPropertyItem gatewayItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(IpUtil.getAddressString(extNetworkInfo.getGateway())));
      gatewayItem.setName(NbBundle.getMessage("vp.gateway"));
      gatewayItem.setEditable(false);
      result.add(gatewayItem);
      // MAC地址
      StringPropertyItem macItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(extNetworkInfo.getMacAddress()));
      macItem.setName(NbBundle.getMessage("vp.mac"));
      macItem.setEditable(false);
      result.add(macItem);
    } catch (ExecutionException | InterruptedException ex) {
      log.error("Failed to obtain network information", ex);
    }
    return result;
  }

  protected static Collection<PropertySheet.Item> createExtenderStatusProperty(
      CaesarDeviceController deviceController, ExtenderData extenderData) {
    // 加载外设数据
    deviceController.execute(() -> deviceController.getDataModel().reloadExtData(extenderData));
    // 分辨率类型
    StringPropertyItem item = new StringPropertyItem(
            new ReadOnlyStringWrapper(
                extenderData.getExtenderStatusInfo().getVideoResolutionType().getText()));
    item.setName(NbBundle.getMessage("extender_status.resolution_type"));
    item.setEditable(false);
    List<PropertySheet.Item> result = new ArrayList<>();
    result.add(item);
    // 类型备注
    String resExtraInfo;
    CaesarConstants.Extender.SpecialExtenderType specialExtenderType =
        extenderData.getExtenderStatusInfo().getSpecialExtType();
    switch (specialExtenderType) {
      case HW_4VIEW:
        resExtraInfo = "[4view]";
        break;
      case HW_FUSION:
        resExtraInfo = "[fusion]";
        break;
      case HW_KAITO_02:
        resExtraInfo = "[1view_kaito_02]";
        break;
      case HW_4VIEW_KAITO_02:
        resExtraInfo = "[4view_kaito_02]";
        break;
      case HW_PREVIEW:
        resExtraInfo = "[preview]";
        break;
      default:
        resExtraInfo = "";
        break;
    }
    CaesarConstants.Extender.SpecialExtenderSubType extSubType =
        CaesarConstants.Extender.SpecialExtenderSubType.valueOf(specialExtenderType,
            extenderData.getExtenderStatusInfo().getSpecialExtSubType());
    String extSubTypeTest;
    if (extSubType.equals(CaesarConstants.Extender.SpecialExtenderSubType.NORMAL)
        || extSubType.equals(CaesarConstants.Extender.SpecialExtenderSubType.NONE)) {
      extSubTypeTest = "";
    } else {
      extSubTypeTest = "-" + extSubType.getText();
    }
    StringPropertyItem subType =
        new StringPropertyItem(new ReadOnlyStringWrapper(resExtraInfo + extSubTypeTest));
    subType.setName(NbBundle.getMessage("extender_status.special_ext_sub_type"));
    subType.setEditable(false);
    result.add(subType);
    // 接口数量
    StringPropertyItem interfaceCntItem =
        new StringPropertyItem(
            new ReadOnlyStringWrapper(
                extenderData.getExtenderStatusInfo().getInterfaceCount() + ""));
    interfaceCntItem.setName(NbBundle.getMessage("extender_status.interface_count"));
    interfaceCntItem.setEditable(false);
    result.add(interfaceCntItem);
    // 接口状态
    ObservableList<String> strList = FXCollections.observableArrayList();
    if (extenderData.getExtenderStatusInfo().isMouseOnline()) {
      strList.add(NbBundle.getMessage("extender_status.mouseonline"));
    }
    if (extenderData.getExtenderStatusInfo().isKeyboardOnline()) {
      strList.add(NbBundle.getMessage("extender_status.keyboardonline"));
    }
    if (extenderData.getExtenderStatusInfo().isUsbcableOnline()) {
      strList.add(NbBundle.getMessage("extender_status.usbcableonline"));
    }
    if (extenderData.getExtenderStatusInfo().isUdiskOnline()) {
      strList.add(NbBundle.getMessage("extender_status.udiskonline"));
    }
    ListPropertyItem statusItem = new ListPropertyItem(strList);
    statusItem.setName(NbBundle.getMessage("extender_status.name"));
    statusItem.setEditable(false);
    result.add(statusItem);
    // 接口类型
    StringPropertyItem interfaceTypeItem =
        new StringPropertyItem(
            new ReadOnlyStringWrapper(
                extenderData.getExtenderStatusInfo().getInterfaceType().getText()));
    interfaceTypeItem.setName(NbBundle.getMessage("extender_status.interface_type"));
    interfaceTypeItem.setEditable(false);
    result.add(interfaceTypeItem);
    // 多接口状态
    for (int i = 0; i < extenderData.getExtenderStatusInfo().getInterfaceCount(); i++) {
      StringPropertyItem videoCableOnline =
          new StringPropertyItem(
              new ReadOnlyStringWrapper(
                  extenderData.getExtenderStatusInfo().getVideoCableOnline().getChStatus(i)
                      ? NbBundle.getMessage("extender_status.online")
                      : NbBundle.getMessage("extender_status.offline")));
      videoCableOnline.setName(NbBundle.getMessage("extender_status.video_cable_status") + (i + 1));
      videoCableOnline.setEditable(false);
      result.add(videoCableOnline);

      String resInfo =
          extenderData.getResolution(i).getWidth()
              + "*"
              + extenderData.getResolution(i).getHeight();
      StringPropertyItem resItem = new StringPropertyItem(new SimpleStringProperty(resInfo));
      resItem.setEditable(false);
      resItem.setName(NbBundle.getMessage("resolution") + (i + 1));
      result.add(resItem);

      StringPropertyItem videoInputItem =
          new StringPropertyItem(
              new ReadOnlyStringWrapper(
                  extenderData.getExtenderStatusInfo().getVideoInput().getChStatus(i)
                      ? NbBundle.getMessage("extender_status.has_input")
                      : NbBundle.getMessage("extender_status.no_input")));
      videoInputItem.setName(NbBundle.getMessage("extender_status.video_input_status") + (i + 1));
      videoInputItem.setEditable(false);
      result.add(videoInputItem);

      StringPropertyItem edidValid =
          new StringPropertyItem(
              new ReadOnlyStringWrapper(
                  extenderData.getExtenderStatusInfo().getEdidvalid().getChStatus(i)
                      ? NbBundle.getMessage("extender_status.valid")
                      : NbBundle.getMessage("extender_status.invalid")));
      edidValid.setName(NbBundle.getMessage("extender_status.edid_status") + (i + 1));
      edidValid.setEditable(false);
      result.add(edidValid);

      // EDID
      if (extenderData.getCpuData() != null) {
        CaesarEdidSender edidSender =
            new CaesarEdidSender(deviceController, extenderData, ExtenderData.PROPERTY_EDID + i, i);
        CaesarProperty<String> edidProperty =
            new CaesarProperty<>(extenderData.getEdidProperty(i), edidSender);
        TextBlockPropertyItem edidItem = new TextBlockPropertyItem(edidProperty, true);
        edidItem.setName("EDID" + (i + 1));
        edidItem.setId("edid-" + (i + 1) + "-property");
        edidItem.setTextAction(
            (event) -> {
              String text = loadEdid(((Node) event.getSource()).getScene().getWindow());
              if (text != null) {
                edidProperty.set(text);
              }
            });
        result.add(edidItem);
      } else if (extenderData.getConsoleData() != null) {
        CaesarProperty<String> edidProperty =
            new CaesarProperty<>(extenderData.getEdidProperty(i), null);
        TextBlockPropertyItem edidItem = new TextBlockPropertyItem(edidProperty, true);
        edidItem.setName("EDID" + (i + 1));
        edidItem.setId("edid-" + (i + 1) + "-property");
        edidItem.setTextAction(
            (event) ->
                saveEdid(((Node) event.getSource()).getScene().getWindow(), edidProperty.get()));
        result.add(edidItem);
      }
    }

    return result;
  }

  protected TextBlockPropertyItem createConsoleIdProperty(ConsoleData consoleData) {
    CaesarConPropertySender<Number> idPropertySender =
        new CaesarConPropertySender<>(deviceController, consoleData, ConsoleData.PROPERTY_ID);
    CaesarProperty<Number> idProperty =
        new CaesarProperty<>(consoleData.getIdProperty(), idPropertySender);
    ObjectProperty<String> idStringProperty = new IdStringObjectProperty(idProperty);
    Bindings.bindBidirectional(idStringProperty, idProperty, new NumberStringConverter("0"));

    TextBlockPropertyItem idItem = new TextBlockPropertyItem(idStringProperty, true);
    idItem.setTextAction(
        (event) -> {
          if (changeIdWarning()) {
            configConId(deviceController, idStringProperty.get(), idProperty);
          }
        });
    idProperty.addListener(
        (observable, oldVal, newVal) -> {
          if (newVal.intValue() != 0 && oldVal.intValue() != 0 && !newVal.equals(oldVal)) {
            refresh();
          }
        });
    idItem.setName(NbBundle.getMessage("id"));
    return idItem;
  }

  protected Collection<Item> createCpuGroupExtraProperties(CaesarMatrix matrix, CpuGroup cpuGroup) {
    // 名称
    TxRxGroupData txRxGroupData = cpuGroup.getTxRxGroupData();
    vpConProperties.clear();
    CaesarTxRxGroupPropertySender<String> namePropertySender =
        new CaesarTxRxGroupPropertySender<>(deviceController, TxRxGroupData.PROPERTY_NAME,
            txRxGroupData);
    CaesarProperty<String> nameProperty =
        new CaesarProperty<>(cpuGroup.nameProperty(), namePropertySender);
    TextBlockPropertyItem nameItem = new TextBlockPropertyItem(nameProperty, true);
    nameItem.setName(NbBundle.getMessage("name"));
    List<String> existGroupNames =
        deviceController.getDataModel().getConfigDataManager().getActiveTxRxGroups().stream()
            .filter(item -> cpuGroup.getTxRxGroupData().getOid() != item.getOid())
            .map(TxRxGroupData::getName)
            .collect(Collectors.toList());
    nameItemAction(nameProperty, nameItem, existGroupNames);
    vpConProperties.add(nameItem);
    return vpConProperties;
  }

  protected Collection<Item> createAudioGroupExtraProperties(CaesarMatrix matrix, AudioGroup audioGroup) {
    TxRxGroupData txRxGroupData = audioGroup.getTxRxGroupData();
    CaesarTxRxGroupPropertySender<String> namePropertySender =
        new CaesarTxRxGroupPropertySender<>(deviceController, TxRxGroupData.PROPERTY_NAME,
            txRxGroupData);
    CaesarProperty<String> nameProperty =
        new CaesarProperty<>(audioGroup.nameProperty(), namePropertySender);
    TextBlockPropertyItem nameItem = new TextBlockPropertyItem(nameProperty, true);
    nameItem.setName(NbBundle.getMessage("name"));
    List<String> existGroupNames =
        deviceController.getDataModel().getConfigDataManager().getActiveTxRxGroups().stream()
            .filter(item -> audioGroup.getTxRxGroupData().getOid() != item.getOid())
            .map(TxRxGroupData::getName)
            .collect(Collectors.toList());
    nameItemAction(nameProperty, nameItem, existGroupNames);

    List<PropertySheet.Item> audioGroupProperties = new ArrayList<>();
    audioGroupProperties.add(nameItem);

    StringPropertyItem item =
        new StringPropertyItem(new ReadOnlyStringWrapper(NbBundle.getMessage("type.audio_group")));
    item.setName(NbBundle.getMessage("type"));
    item.setEditable(false);
    audioGroupProperties.add(item);

    return audioGroupProperties;
  }

  protected Collection<Item> createVisualEditGroupExtraProperties(
      CaesarMatrix matrix, VisualEditGroup vpGroup) {
    vpConProperties.clear();
    CaesarProperty<String> nameProperty = new CaesarProperty<>(vpGroup.nameProperty(), null);
    TextBlockPropertyItem nameItem = new TextBlockPropertyItem(nameProperty, true);
    nameItem.setName(NbBundle.getMessage("name"));
    // 获取已存在的视频墙名称
    List<String> existGroupNames = getExistVwNames(vpGroup);
    nameItemAction(nameProperty, nameItem, existGroupNames);
    vpConProperties.add(nameItem);
    return vpConProperties;
  }

  // 获取已存在的视频墙名称
  private List<String> getExistVwNames(VisualEditGroup vpGroup) {
    List<String> existGroupNames = new ArrayList<>();
    for (int videoWallIndex = 0; videoWallIndex < VideoWallGroupData.GROUP_COUNT;
         videoWallIndex++) {
      if (vpGroup instanceof CaesarVideoWallFunc) {
        if (videoWallIndex == ((CaesarVideoWallFunc) vpGroup).getVideoWallIndex()) {
          continue;
        }
        VideoWallGroupData.VideoWallData wallData =
            deviceController.getDataModel().getVpDataModel().getVideoWallData(videoWallIndex);
        if (wallData != null && !wallData.getName().isEmpty()) {
          existGroupNames.add(wallData.getName());
        }
      }
    }
    return existGroupNames;
  }

  protected Collection<Item> createMatrixExtraProperties(
      CaesarDeviceController deviceController, CaesarMatrix matrix) {
    matrixProperties.clear();
    CaesarSwitchDataModel model;

    try {
      model =
          deviceController
              .submit(
                  () -> Utilities.getExternalModel(deviceController.getDataModel(), matrix.getIp()))
              .get();
    } catch (InterruptedException | ExecutionException exception) {
      log.warn("Fail to get model for " + matrix.getIp(), exception);
      return Collections.emptyList();
    }

    SystemConfigData systemConfigData = model.getConfigData().getSystemConfigData();

    StringPropertyItem nameItem =
        new StringPropertyItem(systemConfigData.getSystemData().getDeviceProperty());
    nameItem.setName(NbBundle.getMessage("host.name"));
    nameItem.setEditable(false);
    matrixProperties.add(nameItem);

    // 主机类型
    ModuleData module = model.getSwitchModuleData().getModuleData(0);
    CaesarConstants.Module.Type type = null;
    if (module != null) {
      type = CaesarConstants.Module.Type.valueOf(module.getType());
    }
    StringPropertyItem typeItem =
        new StringPropertyItem(
            new ReadOnlyStringWrapper(type == null ? "UNKNOWN" : type.getName()));
    typeItem.setEditable(false);
    typeItem.setName(NbBundle.getMessage("matrixType"));
    matrixProperties.add(typeItem);

    // 主机版本
    String version = String.format("%x", model.getConfigMetaData().getVersion());
    StringPropertyItem versionItem = new StringPropertyItem(new ReadOnlyStringWrapper(version));
    versionItem.setEditable(false);
    versionItem.setName(NbBundle.getMessage("version"));
    matrixProperties.add(versionItem);

    String ipAddress = matrix.getIp();
    StringPropertyItem ipAddressItem = new StringPropertyItem(new SimpleStringProperty(ipAddress));
    ipAddressItem.setName(NbBundle.getMessage("host.ipAddress"));
    ipAddressItem.setEditable(false);
    matrixProperties.add(ipAddressItem);

    String netMask =
        systemConfigData.getNetworkDataPreset1().isDhcp()
            ? IpUtil.getAddressString(systemConfigData.getNetworkDataCurrent1().getNetmask())
            : IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getNetmask());
    StringPropertyItem netMaskItem = new StringPropertyItem(new SimpleStringProperty(netMask));
    netMaskItem.setName(NbBundle.getMessage("host.netMask"));
    netMaskItem.setEditable(false);
    matrixProperties.add(netMaskItem);

    String macAddress =
        systemConfigData.getNetworkDataCurrent1().getMacAddress() == null
            ? ""
            : systemConfigData.getNetworkDataCurrent1().getMacAddress();
    StringPropertyItem macAddressItem =
        new StringPropertyItem(new SimpleStringProperty(macAddress));
    macAddressItem.setName(NbBundle.getMessage("host.macAddress"));
    macAddressItem.setEditable(false);
    matrixProperties.add(macAddressItem);

    String isOnline =
        deviceController.isOnline()
            ? NbBundle.getMessage("host.online")
            : NbBundle.getMessage("host.offline");
    StringPropertyItem isOnlineItem = new StringPropertyItem(new SimpleStringProperty(isOnline));
    isOnlineItem.setName(NbBundle.getMessage("host.status"));
    isOnlineItem.setEditable(false);
    matrixProperties.add(isOnlineItem);

    // Tx数量
    Collection<CpuData> cpus = model.getConfigDataManager().getActiveCpus();
    int txNum = cpus.size();
    for (CpuData cpu : cpus) {
      ExtenderData extenderData = cpu.getExtenderData(0);
      if (extenderData != null
          && Utilities.isExtenderInMatrix(model, matrix.getMatrixData(), extenderData)) {
        continue;
      }
      if (cpu.isStatusDelete()) {
        continue;
      }
      txNum--;
    }
    NumberPropertyItem txNumItem =
        new NumberPropertyItem(new SimpleIntegerProperty(txNum), Integer.class);
    txNumItem.setName(NbBundle.getMessage("txNum"));
    txNumItem.setId("txnum-property");
    txNumItem.setEditable(false);
    matrixProperties.add(txNumItem);

    // Rx数量
    Collection<ConsoleData> cons = model.getConfigDataManager().getActiveConsoles();
    int rxNum = cons.size();
    for (ConsoleData con : cons) {
      ExtenderData extenderData = con.getExtenderData(0);
      if (extenderData != null
          && Utilities.isExtenderInMatrix(model, matrix.getMatrixData(), extenderData)) {
        continue;
      }
      if (extenderData != null && extenderData.isStatusDelete()) {
        continue;
      }
      rxNum--;
    }
    NumberPropertyItem rxNumItem =
        new NumberPropertyItem(new SimpleIntegerProperty(rxNum), Integer.class);
    rxNumItem.setName(NbBundle.getMessage("rxNum"));
    rxNumItem.setId("rxnum-property");
    rxNumItem.setEditable(false);
    matrixProperties.add(rxNumItem);

    return matrixProperties;
  }

  protected Collection<Item> createVpProperties(CaesarMatrix matrix, VpGroup vpGroup) {
    ObservableList<Item> properties = FXCollections.observableArrayList();
    properties.addAll(vpGroup.getProperties());
    if (vpGroup.getVpConsoleData() != null) {
      VpConsoleData vpConsoleData = vpGroup.getVpConsoleData();

      String statusValue =
          vpConsoleData.isStatusOnline()
              ? NbBundle.getMessage("vp.status.normal")
              : NbBundle.getMessage("vp.status.error");
      StringPropertyItem statusItem =
          new StringPropertyItem(new ReadOnlyStringWrapper(statusValue));
      statusItem.setName(NbBundle.getMessage("vp.status"));
      statusItem.setEditable(false);
      properties.add(statusItem);

      for (int i = 0; i < vpConsoleData.getInPortCount(); i++) {
        ConsoleData consoleData = vpConsoleData.getInPort(i);
        if (consoleData == null) {
          continue;
        }
        TextBlockPropertyItem item = createConsoleIdProperty(consoleData);
        item.setName(MessageFormat.format(NbBundle.getMessage("vp.input"), i + 1));
        properties.add(item);
      }

      for (int i = 0; i < vpConsoleData.getOutPortCount(); i++) {
        VpConsoleData subConsoleData = vpConsoleData.getOutPortList()[i];
        if (subConsoleData == null) {
          continue;
        }
        StringPropertyItem item = new StringPropertyItem(subConsoleData.nameProperty());
        item.setName(MessageFormat.format(NbBundle.getMessage("vp.output"), i + 1));
        properties.add(item);
      }
    }
    return properties;
  }

  protected Collection<Item> createUsbProperty(CaesarMatrix matrix, CaesarTerminalBase terminal) {
    ObservableList<Item> properties = FXCollections.observableArrayList();
    ExtenderData extenderData = terminal.getExtenderData();
    if (extenderData == null) {
      return properties;
    }
    //
    StringPropertyItem typeItem;
    if (terminal.isTx()) {
      typeItem = new StringPropertyItem(new ReadOnlyStringWrapper(UsbType.USB_TX.toString()));
    } else {
      typeItem = new StringPropertyItem(new ReadOnlyStringWrapper(UsbType.USB_RX.toString()));
    }
    typeItem.setName(NbBundle.getMessage("extender_status.usb_type"));
    typeItem.setEditable(false);
    properties.add(typeItem);
    // 端口
    StringPropertyItem portItem = new StringPropertyItem(extenderData.getPortProperty());
    portItem.setName(NbBundle.getMessage("extenderPort"));
    portItem.setEditable(false);
    properties.add(portItem);
    // 扩展器绑定
    StringPropertyItem bindingItem;
    if (extenderData.getConsoleData() != null) {
      bindingItem = new StringPropertyItem(extenderData.getConsoleData().getNameProperty());
    } else if (extenderData.getCpuData() != null) {
      bindingItem = new StringPropertyItem(extenderData.getCpuData().getNameProperty());
    } else {
      bindingItem = new StringPropertyItem(new ReadOnlyStringWrapper(""));
    }
    bindingItem.setName(NbBundle.getMessage("extender_status.extender_binding"));
    bindingItem.setEditable(false);
    properties.add(bindingItem);
    return properties;
  }

  protected Collection<Item> createGridLineProperties(
      CaesarMatrix matrix, CaesarGridLineTerminal terminal) {
    ObservableList<Item> properties = FXCollections.observableArrayList();

    StringPropertyItem nameItem = new StringPropertyItem(terminal.nameProperty());
    nameItem.setEditable(true);
    nameItem.setName(NbBundle.getMessage("name"));
    properties.add(nameItem);

    StringPropertyItem typeItem =
        new StringPropertyItem(new ReadOnlyStringWrapper(NbBundle.getMessage("gridInterface")));
    typeItem.setEditable(false);
    typeItem.setName(NbBundle.getMessage("type"));
    properties.add(typeItem);
    // 本地端口
    Collection<ConnectorIdentifier> ids = matrix.getTerminalPorts(terminal);
    if (ids.size() > 0) {
      ConnectorIdentifier connectorIdentifier = ids.iterator().next();
      StringPropertyItem localPort = new StringPropertyItem(connectorIdentifier.textProperty());
      localPort.setName(NbBundle.getMessage("localPort"));
      localPort.setEditable(false);
      properties.add(localPort);
    }
    // 对端主机与对端端口
    PortData portData = terminal.getPortData();
    int connectedPort = portData.getType();
    MatrixDefinitionData remoteMatrix = null;
    for (MatrixDefinitionData matrixDefinitionData :
        Utilities.getActiveMatrices(deviceController.getDataModel())) {
      if (connectedPort >= matrixDefinitionData.getFirstPort()
          && connectedPort <= matrixDefinitionData.getLastPort()) {
        remoteMatrix = matrixDefinitionData;
        break;
      }
    }
    if (remoteMatrix != null) {
      StringPropertyItem remoteMatrixItem =
          new StringPropertyItem(new ReadOnlyStringWrapper(remoteMatrix.getDevice()));
      remoteMatrixItem.setEditable(false);
      remoteMatrixItem.setName(NbBundle.getMessage("remoteMatrix"));
      properties.add(remoteMatrixItem);

      StringPropertyItem remotePortItem =
          new StringPropertyItem(new ReadOnlyStringWrapper(connectedPort + ""));
      remotePortItem.setEditable(false);
      remotePortItem.setName(NbBundle.getMessage("remotePort"));
      properties.add(remotePortItem);
    }
    // 使用状态
    StringProperty statusProperty = new SimpleStringProperty();
    statusProperty.bind(
        Bindings.when(terminal.channel1UsedProperty().or(terminal.channel2UsedProperty()))
            .then(NbBundle.getMessage("used"))
            .otherwise(NbBundle.getMessage("unused")));
    StringPropertyItem statusItem = new StringPropertyItem(statusProperty);
    statusItem.setEditable(false);
    statusItem.setName(NbBundle.getMessage("usedStatus"));
    properties.add(statusItem);
    // 通道1占用端口
    StringProperty channel1PortProperty = new SimpleStringProperty();
    channel1PortProperty.bind(terminal.channel1UsedPortProperty().asString());
    StringPropertyItem channel1PortItem = new StringPropertyItem(channel1PortProperty);
    channel1PortItem.setEditable(false);
    channel1PortItem.setName(NbBundle.getMessage("channel1Port"));
    properties.add(channel1PortItem);
    // 通道2占用端口
    StringProperty channel2PortProperty = new SimpleStringProperty();
    channel2PortProperty.bind(terminal.channel2UsedPortProperty().asString());
    StringPropertyItem channel2PortItem = new StringPropertyItem(channel2PortProperty);
    channel2PortItem.setEditable(false);
    channel2PortItem.setName(NbBundle.getMessage("channel2Port"));
    properties.add(channel2PortItem);

    return properties;
  }

  /** 创建额外的属性. */
  public Collection<Item> createExtraProperties(CaesarMatrix matrix, VisualEditNode node,
                                                Version version) {
    if (node.isTx() && node instanceof CaesarCpuTerminal) {
      return createCpuExtraProperties(matrix, (CaesarCpuTerminal) node, version);
    } else if (node.isTx() && node instanceof CaesarUsbTxTerminal) {
      CaesarUsbTxTerminal terminal = (CaesarUsbTxTerminal) node;
      return createUsbProperty(matrix, terminal);
    } else if (node.isRx() && node instanceof CaesarConTerminal) {
      return createConExtraProperties(matrix, (CaesarConTerminal) node, version);
    } else if (node.isRx() && node instanceof CaesarUsbRxTerminal) {
      CaesarUsbRxTerminal terminal = (CaesarUsbRxTerminal) node;
      return createUsbProperty(matrix, terminal);
    } else if (node instanceof CaesarGridLineTerminal) {
      return createGridLineProperties(matrix, (CaesarGridLineTerminal) node);
    } else if (node instanceof VpGroup) {
      return createVpProperties(matrix, (VpGroup) node);
    } else if (node instanceof CpuGroup) {
      return createCpuGroupExtraProperties(matrix, (CpuGroup) node);
    } else if (node instanceof AudioGroup) {
      return createAudioGroupExtraProperties(matrix, (AudioGroup) node);
    } else if (node instanceof VisualEditGroup) {
      return createVisualEditGroupExtraProperties(matrix, (VisualEditGroup) node);
    } else if (node instanceof CaesarMatrix) {
      return createMatrixExtraProperties(deviceController, (CaesarMatrix) node);
    } else {
      return Collections.emptyList();
    }
  }

  /**
   * 创建级联拓扑项的属性列表.
   *
   * @param item 级联拓扑的主机项.
   * @return 属性列表
   */
  public static Collection<Item> createGridMatrixItemProperties(GridMatrixItem item) {
    List<Item> result = new ArrayList<>();
    StringPropertyItem nameProperty =
        new StringPropertyItem(new ReadOnlyStringWrapper(item.getName()));
    nameProperty.setEditable(false);
    nameProperty.setName(NbBundle.getMessage("gridmatrix.name"));
    result.add(nameProperty);

    int index = 1;
    for (Map.Entry<GridMatrixItem, Set<GridMatrixLink>> entry : item.getRelatedItems().entrySet()) {
      ObservableList<String> list = FXCollections.observableArrayList();
      ListPropertyItem connectionProperty = new ListPropertyItem(list);
      list.add(entry.getKey().getName());
      for (GridMatrixLink link : entry.getValue()) {
        list.add(link.localPort + "(" + link.remotePort + ")");
      }
      connectionProperty.setName(NbBundle.getMessage("gridmatrix.connection") + index);
      connectionProperty.setEditable(false);
      result.add(connectionProperty);
      index++;
    }
    return result;
  }

  /**
   * 预览终端属性列别.
   */
  public Collection<Item> createPreviewProperty(
      CaesarDeviceController deviceController, ConsoleData consoleData, ExtenderData extenderData) {
    List<PropertySheet.Item> result = new ArrayList<>();
    // 扩展单元序列号
    StringPropertyItem serialNumberItem =
        new StringPropertyItem(new SimpleStringProperty(extenderData.getSerial()));
    serialNumberItem.setName(NbBundle.getMessage("serial"));
    serialNumberItem.setEditable(false);
    result.add(serialNumberItem);
    // 类型备注
    CaesarConstants.Extender.SpecialExtenderSubType extSubType =
        CaesarConstants.Extender.SpecialExtenderSubType.valueOf(
            extenderData.getExtenderStatusInfo().getSpecialExtType(),
            extenderData.getExtenderStatusInfo().getSpecialExtSubType());
    String extSubTypeTest;
    if (extSubType.equals(CaesarConstants.Extender.SpecialExtenderSubType.NONE)) {
      extSubTypeTest = "";
    } else {
      extSubTypeTest = "-" + extSubType.getText();
    }
    StringPropertyItem subType =
        new StringPropertyItem(new ReadOnlyStringWrapper("[preview]" + extSubTypeTest));
    subType.setName(NbBundle.getMessage("extender_status.special_ext_sub_type"));
    subType.setEditable(false);
    result.add(subType);
    // 端口
    StringPropertyItem portItem = new StringPropertyItem(extenderData.getPortProperty());
    portItem.setName(NbBundle.getMessage("extenderPort"));
    portItem.setEditable(false);
    result.add(portItem);
    try {
      ExtenderNetworkInfo extNetworkInfo = deviceController.submit(
          () -> deviceController.getDataModel().getExtNetworkInfo(extenderData)).get();
      // ip
      SimpleObjectProperty<String> ipProperty =
          new SimpleObjectProperty<>(IpUtil.getAddressString(extNetworkInfo.getAddress()));
      TextBlockPropertyItem ipItem = new TextBlockPropertyItem(ipProperty);
      netItemAction(ipItem, extNetworkInfo, (data, networkInfo) -> {
        try {
          deviceController.submit(() -> {
            try {
              deviceController.getDataModel().setExtNetworkInfo(data, networkInfo);
            } catch (BusyException | ConfigException | DeviceConnectionException ex) {
              log.error("Fail to set extender network info!", ex);
            }
          }).get();
        } catch (Exception ex) {
          log.error("Fail to set extender network info!", ex);
        }
      }, extenderData);
      ipItem.setName(NbBundle.getMessage("vp.ip"));
      ipItem.setEditable(
          CaesarConstants.Extender.SpecialExtenderSubType.PRIMARY.equals(extSubType));
      result.add(ipItem);
      // 子网掩码
      StringPropertyItem netmaskItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(IpUtil.getAddressString(extNetworkInfo.getNetmask())));
      netmaskItem.setName(NbBundle.getMessage("vp.netmask"));
      netmaskItem.setEditable(false);
      result.add(netmaskItem);
      // 网关
      StringPropertyItem gatewayItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(IpUtil.getAddressString(extNetworkInfo.getGateway())));
      gatewayItem.setName(NbBundle.getMessage("vp.gateway"));
      gatewayItem.setEditable(false);
      result.add(gatewayItem);
      // MAC地址
      StringPropertyItem macItem = new StringPropertyItem(
          new ReadOnlyStringWrapper(extNetworkInfo.getMacAddress()));
      macItem.setName(NbBundle.getMessage("vp.mac"));
      macItem.setEditable(false);
      result.add(macItem);
    } catch (ExecutionException | InterruptedException ex) {
      log.error("Failed to obtain network information", ex);
    }
    return result;
  }

  /**
   * 设置网络配置对话框动作.
   *
   * @param netItem 网络配置项
   * @param extNetworkInfo 扩展网络信息
   * @param networkInfoConsumer 网络信息消费者，用于处理网络信息设置
   * @param extenderData 扩展数据
   */
  public static void netItemAction(TextBlockPropertyItem netItem,
                                   ExtenderNetworkInfo extNetworkInfo,
                                   BiConsumer<ExtenderData, ExtenderNetworkInfo> networkInfoConsumer,
                                   ExtenderData extenderData) {
    netItem.setTextAction(actionEvent -> {
      ApplicationBase applicationBase =
          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      DialogEx<NetworkConfigInfo> networkConfigDialog =
          ViewUtility.getNetworkConfigDialog(applicationBase.getMainWindow(),
              IpUtil.getAddressString(extNetworkInfo.getAddress()),
              IpUtil.getAddressString(extNetworkInfo.getNetmask()),
              IpUtil.getAddressString(extNetworkInfo.getGateway()));
      Optional<NetworkConfigInfo> result = networkConfigDialog.showAndWait();
      result.ifPresent(networkConfigInfo -> {
        try {
          ExtenderNetworkInfo extenderNetworkInfo = new ExtenderNetworkInfo();
          extenderNetworkInfo.setAddress(IpUtil.getAddressByte(networkConfigInfo.getIpAddress()));
          extenderNetworkInfo.setNetmask(IpUtil.getAddressByte(networkConfigInfo.getSubnetMask()));
          extenderNetworkInfo.setGateway(IpUtil.getAddressByte(networkConfigInfo.getGateway()));
          // 使用传入的消费者处理网络信息设置
          networkInfoConsumer.accept(extenderData, extenderNetworkInfo);
        } catch (Exception ex) {
          log.error("Fail to set extender network info!", ex);
        }
      });
    });
  }

  /**
   * 弹出文件选择对话框选择edid文件加载.
   *
   * @param window 父window
   * @return 如果加载成功，返回加载到的字符串，否则返回null
   */
  private static String loadEdid(Window window) {
    FileChooser chooser = new FileChooser();
    chooser.getExtensionFilters().add(new ExtensionFilter("EDID files (*.edid)", "*.edid"));
    File file = chooser.showOpenDialog(window);
    if (file != null) {
      FileInputStream fis = null;
      try {
        fis = new FileInputStream(file);
        byte[] data = new byte[256];
        int readedlength = fis.read(data);
        if (readedlength != 256) {
          UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
          alert.initOwner(window);
          alert.setContentText("Error edid file!");
          alert.showAndWait();
        } else {
          return DatatypeConverter.printHexBinary(data).toLowerCase(Locale.US);
        }
      } catch (IOException exception) {
        log.warn("Fail to load edid!", exception);
      } finally {
        if (fis != null) {
          try {
            fis.close();
          } catch (IOException exception) {
            log.warn("Fail to close the file input stream!", exception);
          }
        }
      }
    }
    return null;
  }

  /**
   * 弹出文件保存对话框，保存输入的edid字符串.
   *
   * @param window 父window
   * @param text 要保存的edid字符串
   */
  private static void saveEdid(Window window, String text) {
    if (text == null) {
      return;
    }

    FileDialogueFactory factory =
        InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
    FileDialogue fileDialogue = factory.createFileDialogue();
    fileDialogue.setInitialFileName("HDMIMonitor");
    fileDialogue.addExtensionFilter(new ExtensionFilter("EDID files (*.edid)", "*.edid"));
    File file = fileDialogue.showSaveDialog(window);
    if (file != null) {
      FileOutputStream fileOutputStream = null;
      try {
        fileOutputStream = new FileOutputStream(file);
        fileOutputStream.write(DatatypeConverter.parseHexBinary(text));
      } catch (IOException | RuntimeException exp) {
        log.warn("Can not write the file : {}", file.getAbsolutePath());
      } finally {
        if (fileOutputStream != null) {
          try {
            fileOutputStream.close();
          } catch (IOException exc) {
            log.warn("Can not close the file output stream!");
          }
        }
      }
    }
  }

  static class IdChangeOperator implements UnaryOperator<Change> {

    @Override
    public Change apply(Change change) {
      String newText = change.getControlNewText();
      if (newText.matches("[0-9]{1,4}")) {
        return change;
      }
      return null;
    }
  }

  private void nameItemAction(
      CaesarProperty<String> nameProperty,
      TextBlockPropertyItem nameItem,
      List<String> existingNames) {
    nameItem.setTextAction(
        (event) -> {
          Predicate<String> predicate = new CaesarNamePredicate(existingNames);
          ApplicationBase applicationBase =
              InjectorProvider.getInjector().getInstance(ApplicationBase.class);
          Optional<String> result =
              ViewUtility.getNameFromDialog(
                  applicationBase.getMainWindow(), nameProperty.get(), null, predicate);
          result.ifPresent(nameProperty::set);
        });
  }

  /** 修改ID提示框. */
  public static boolean changeIdWarning() {
    UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
    ApplicationBase applicationBase =
        InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    alert.initOwner(applicationBase.getMainWindow());
    alert.getButtonTypes().add(ButtonType.CANCEL);
    alert.setHeaderText(null);
    alert.setTitle(NbBundle.getMessage("changeId.alert.title"));
    alert.setContentText(NbBundle.getMessage("changeId.alert.content"));
    Optional<ButtonType> result = alert.showAndWait();
    return result.isPresent() && result.get() == ButtonType.OK;
  }

  private void refresh() {
    ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    Task<Void> task =
        new Task<Void>() {
          @Override
          protected Void call() {
            updateMessage(CaesarI18nCommonResource.getString("page.refreshing"));
            try {
              Thread.sleep(1000);
            } catch (InterruptedException ex) {
              log.error("" + ex);
            }
            for (Page page : entity.getPages()) {
              if (page.getVisibleProperty().get()) {
                page.refresh();
              }
            }
            return null;
          }
        };
    app.getTaskManager().addForegroundTask(task);
  }

  private static class SuspendPosStringConverter extends StringConverter<SuspendPosition> {

    @Override
    public String toString(SuspendPosition object) {
      return object.getName();
    }

    @Override
    public SuspendPosition fromString(String string) {
      return null;
    }
  }

  private static class NameableStringConverter<T extends Nameable> extends StringConverter<T> {

    @Override
    public String toString(T object) {
      return object.getName();
    }

    @Override
    public T fromString(String string) {
      return null;
    }
  }

  private static class DhdmiSelectionStringConverter extends StringConverter<DhdmiSelection> {

    @Override
    public String toString(DhdmiSelection object) {
      switch (object) {
        case AUTO:
          return NbBundle.getMessage("hdmiSelection.auto");
        case HDMI1:
          return NbBundle.getMessage("hdmiSelection.hdmi1");
        case HDMI2:
          return NbBundle.getMessage("hdmiSelection.hdmi2");
        default:
          return null;
      }
    }

    @Override
    public DhdmiSelection fromString(String string) {
      return null;
    }
  }

  private static class IdStringObjectProperty extends SimpleObjectProperty<String> {

    private CaesarProperty<Number> idProperty;

    public IdStringObjectProperty(CaesarProperty<Number> idProperty) {
      this.idProperty = idProperty;
    }

    @Override
    public void unbind() {
      super.unbind();
      idProperty.unbind();
    }
  }
}
