package com.mc.tool.caesar.vpm.pages.operation.multiscreen.view;

import com.mc.tool.caesar.vpm.pages.operation.multiscreen.controller.CaesarMultiScreenController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.operation.interfaces.OperationView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import java.io.IOException;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarMultiScreenOperationView extends VBox implements OperationView {
  private CaesarMultiScreenController controller;

  /**
   * Constructor.
   *
   * @param model model
   * @param func func
   */
  public CaesarMultiScreenOperationView(VisualEditModel model, VisualEditFunc func) {
    FXMLLoader loader =
        new FXMLLoader(
            getClass()
                .getResource("/com/mc/tool/framework/operation/crossscreen/crossscreen.fxml"));
    controller = new CaesarMultiScreenController();
    if (func instanceof CaesarMultiScreenFunc) {
      controller.init(model, (CaesarMultiScreenFunc) func);
    } else {
      log.error("Error function type!", new Exception());
    }
    loader.setRoot(this);
    loader.setController(controller);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load crossscreen.fxml!", exception);
    }
  }

  @Override
  public Node getView() {
    return this;
  }

  @Override
  public OperationControllable getOperationControllable() {
    return controller;
  }
}
