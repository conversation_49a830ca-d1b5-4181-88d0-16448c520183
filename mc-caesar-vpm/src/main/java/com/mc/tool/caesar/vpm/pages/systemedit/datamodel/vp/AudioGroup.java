package com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp;

import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import lombok.Getter;
import lombok.Setter;

/**
 * AudioGroup.
 */
public class AudioGroup extends VisualEditGroup {
  @Getter
  @Setter
  private TxRxGroupData txRxGroupData;

  @Override
  public boolean isRx() {
    return true;
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.AUDIO_GROUP_CELL;
  }
}
