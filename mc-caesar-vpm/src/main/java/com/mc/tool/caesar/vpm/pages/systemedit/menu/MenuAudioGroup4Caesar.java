package com.mc.tool.caesar.vpm.pages.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarConTerminal;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMatrix;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp.AudioGroup;
import com.mc.tool.caesar.vpm.util.CaesarNamePredicate;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.MenuGroup;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javafx.beans.binding.BooleanBinding;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建音频镜像组菜单.
 */
@Slf4j
public class MenuAudioGroup4Caesar extends MenuItem {

  private final SystemEditControllable controllable;
  private TxRxGroupData unActiveTxRxGroup;

  /**
   * MenuAudioGroup4Caesar.
   */
  public MenuAudioGroup4Caesar(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(Bundle.NbBundle.getMessage("menu.group.audio"));
    this.setOnAction(event -> onAction());
    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public BooleanBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    binding.addSingleSelectionPredicate(node -> !(node instanceof CaesarConTerminal));
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal
            && ((CaesarConTerminal) node).getExtenderData().isVpConType());
    // TODO: 什么RX能创建音频组？
    binding.addSingleSelectionPredicate(
        node -> node instanceof CaesarConTerminal && !(node.getParent() instanceof CaesarMatrix));
    if (controllable instanceof CaesarSystemEditController) {
      CaesarSwitchDataModel dataModel =
          ((CaesarSystemEditController) controllable).getDeviceController().getDataModel();
      boolean hasTxRxGroupData = dataModel.getConfigMetaData().getUtilVersion().hasTxRxGroupData();
      binding.addSingleSelectionPredicate(
          node -> node instanceof CaesarConTerminal && !hasTxRxGroupData);
      if (hasTxRxGroupData) {
        unActiveTxRxGroup = dataModel.getConfigDataManager().getUnActiveTxRxGroup();
        binding.addSingleSelectionPredicate(
            node -> node instanceof CaesarConTerminal && unActiveTxRxGroup == null);
      }
    }
    return binding;
  }

  private void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn(
            "Cell's bindedobject is not VisualEditNode but {}", skin.getCell().getBindedObject());
      }
    }
    if (controllable instanceof CaesarSystemEditController
        && nodeList.stream().allMatch(item -> item instanceof CaesarConTerminal)) {
      CaesarDeviceController deviceController =
          ((CaesarSystemEditController) controllable).getDeviceController();
      List<String> existGroupNames =
          deviceController.getDataModel().getConfigDataManager().getActiveTxRxGroups().stream()
              .map(TxRxGroupData::getName).collect(Collectors.toList());
      Predicate<String> predicate = new CaesarNamePredicate(existGroupNames);
      String groupName = "AudioGroup";
      ApplicationBase applicationBase =
          InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      Optional<String> result =
          ViewUtility.getNameFromDialog(
              applicationBase.getMainWindow(), groupName, null, predicate);
      if (result.isPresent()) {
        groupName = result.get();
      } else {
        return;
      }
      AudioGroup audioGroup = controllable.addGroup(
          groupName, AudioGroup.class, nodeList.toArray(new VisualEditNode[0]));
      audioGroup.setTxRxGroupData(unActiveTxRxGroup);
      String finalGroupName = groupName;
      deviceController.execute(() -> {
        for (VisualEditNode node : nodeList) {
          if (node instanceof CaesarConTerminal) {
            CaesarConTerminal conTerminal = (CaesarConTerminal) node;
            conTerminal.getConsoleData().setGroupIndex(unActiveTxRxGroup.getOid() + 1);
          }
        }
        unActiveTxRxGroup.setName(finalGroupName);
        unActiveTxRxGroup.setType(TxRxGroupDataType.RX_AUDIO_GROUP);
        ((CaesarSystemEditController) controllable).getDeviceController()
            .addTxRxGroupData(unActiveTxRxGroup);
      });
    }
  }
}
