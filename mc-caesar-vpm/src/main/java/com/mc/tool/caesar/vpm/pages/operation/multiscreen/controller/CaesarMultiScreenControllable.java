package com.mc.tool.caesar.vpm.pages.operation.multiscreen.controller;

import com.mc.tool.caesar.vpm.pages.operation.multiscreen.datamodel.CaesarMultiScreenData;
import com.mc.tool.caesar.vpm.pages.systemedit.datamodel.CaesarMultiScreenFunc;
import com.mc.tool.framework.operation.crossscreen.controller.MultiScreenControllable;

/**
 * .
 */
public interface CaesarMultiScreenControllable
    extends MultiScreenControllable<CaesarMultiScreenData, CaesarMultiScreenFunc> {}
