package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.Valid;
import lombok.Setter;

/**
 * UpdateLayerResponse.
 */
@Setter
@JsonPropertyOrder({
    UpdateLayerResponse.JSON_PROPERTY_SEQ,
    UpdateLayerResponse.JSON_PROPERTY_LAYER
})
public class UpdateLayerResponse {
  public static final String JSON_PROPERTY_SEQ = "seq";
  private Integer seq;

  public static final String JSON_PROPERTY_LAYER = "layer";
  private VideoWallLayer layer;


  public UpdateLayerResponse seq(Integer seq) {
    this.seq = seq;
    return this;
  }

  /**
   * 大屏修改序号.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSeq() {
    return seq;
  }


  public UpdateLayerResponse layer(VideoWallLayer layer) {
    this.layer = layer;
    return this;
  }

  /**
   * Get layer.
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_LAYER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public VideoWallLayer getLayer() {
    return layer;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdateLayerResponse updateLayerResponse = (UpdateLayerResponse) o;
    return Objects.equals(this.seq, updateLayerResponse.seq)
        && Objects.equals(this.layer, updateLayerResponse.layer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(seq, layer);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("UpdateLayerResponse {\n");
    sb.append("    seq: ").append(toIndentedString(seq)).append("\n");
    sb.append("    layer: ").append(toIndentedString(layer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
