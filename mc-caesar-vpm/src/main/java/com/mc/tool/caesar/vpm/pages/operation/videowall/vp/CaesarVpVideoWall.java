package com.mc.tool.caesar.vpm.pages.operation.videowall.vp;

import com.google.common.collect.Lists;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.Vp7ConfigData;
import com.mc.tool.caesar.vpm.pages.operation.videowall.datamodel.CaesarVideoWallData;
import com.mc.tool.caesar.vpm.util.vp.VpOsdData;
import com.mc.tool.caesar.vpm.util.vp.VpResolution;
import com.mc.tool.caesar.vpm.util.vp.VpScreenData;
import com.mc.tool.caesar.vpm.util.vp.VpVideoData;
import com.mc.tool.caesar.vpm.util.vp.VpVideoWall;
import java.util.Arrays;
import java.util.List;
import javafx.geometry.Rectangle2D;

/**
 * .
 */
public class CaesarVpVideoWall implements VpVideoWall {
  // 常用显示器分辨率输出时序数据
  static final Vp7ConfigData.Vp7OutputResData[] DMT_TIMINGS = new Vp7ConfigData.Vp7OutputResData[] {
      // 分辨率, 时钟(Hz), {h_sync, h_back, h_disp, h_total, h_polarity}, {v_sync, v_back, v_disp, v_total, v_polarity}
      // 640x480 @ 60Hz
      createOutputResData(25175000L, 96, 48, 640, 800, 1, 2, 33, 480, 525, 1),
      // 800x600 @ 60Hz
      createOutputResData(40000000L, 128, 88, 800, 1056, 0, 4, 23, 600, 628, 0),
      // 848x480 @ 60Hz
      createOutputResData(33750000L, 112, 112, 848, 1088, 0, 8, 23, 480, 517, 0),
      // 1024x768 @ 60Hz
      createOutputResData(65000000L, 136, 160, 1024, 1344, 1, 6, 29, 768, 806, 1),
      // 1280x720 @ 60Hz
      createOutputResData(74250000L, 40, 220, 1280, 1650, 0, 5, 20, 720, 750, 0),
      // 1280x768 @ 60Hz
      createOutputResData(79500000L, 128, 192, 1280, 1664, 1, 7, 20, 768, 798, 0),
      // 1280x800 @ 60Hz
      createOutputResData(83500000L, 128, 200, 1280, 1680, 1, 6, 22, 800, 831, 0),
      // 1280x960 @ 60Hz
      createOutputResData(108000000L, 112, 312, 1280, 1800, 0, 3, 36, 960, 1000, 0),
      // 1280x1024 @ 60Hz
      createOutputResData(108000000L, 112, 248, 1280, 1688, 0, 3, 38, 1024, 1066, 0),
      // 1360x768 @ 60Hz
      createOutputResData(85500000L, 112, 256, 1360, 1792, 0, 6, 18, 768, 795, 0),
      // 1366x768 @ 60Hz
      createOutputResData(85500000L, 143, 213, 1366, 1792, 0, 3, 24, 768, 798, 0),
      // 1400x1050 @ 60Hz
      createOutputResData(121750000L, 144, 232, 1400, 1864, 1, 4, 32, 1050, 1089, 0),
      // 1440x900 @ 60Hz
      createOutputResData(106500000L, 152, 232, 1440, 1904, 1, 6, 25, 900, 934, 0),
      // 1600x900 @ 60Hz (RB) probably not supported
      // createOutputResData(108000000L, 80, 96, 1600, 1800, 0, 3, 96, 900, 1000, 0),
      // 1600x1200 @ 60Hz
      createOutputResData(162000000L, 192, 304, 1600, 2160, 0, 3, 46, 1200, 1250, 0),
      // 1680x1050 @ 60Hz
      createOutputResData(146250000L, 176, 280, 1680, 2240, 1, 6, 30, 1050, 1089, 0),
      // 1792x1344 @ 60Hz Unsupported
      // createOutputResData(204750000L, 200, 328, 1792, 2448, 1, 3, 46, 1344, 1394, 0),
      // 1856x1392 @ 60Hz Unsupported
      // createOutputResData(218250000L, 224, 352, 1856, 2528, 1, 3, 43, 1392, 1439, 0),
      // 1920x1080 @ 60Hz
      createOutputResData(148500000L, 44, 148, 1920, 2200, 0, 5, 36, 1080, 1125, 0),
      // 1920x1200 @ 60Hz
      createOutputResData(193250000L, 200, 336, 1920, 2592, 1, 6, 36, 1200, 1245, 0),
      // 1920x1440 @ 60Hz Unsupported
      // createOutputResData(234000000L, 32, 80, 1920, 2080, 0, 3, 56, 1440, 1500, 0),
      // 2048x1152 @ 60Hz
      createOutputResData(162000000L, 80, 96, 2048, 2250, 0, 3, 44, 1152, 1200, 0),
      // 2560x1600 @ 60Hz
      createOutputResData(348500000L, 280, 472, 2560, 3504, 1, 6, 49, 1600, 1658, 0),
      // 3840x2160 @ 60Hz (CEA-861)
      createOutputResData(594000000L, 88, 296, 3840, 4400, 0, 10, 72, 2160, 2250, 0),
      // 4096x2160 @ 60Hz CVT (RBv2)
      createOutputResData(556744000L, 32, 40, 4096, 4176, 0, 8, 6, 2160, 2226, 0)
  };
  
  private CaesarVideoWallData videoWallData;

  public CaesarVpVideoWall(CaesarVideoWallData item) {
    this.videoWallData = item;
  }

  @Override
  public String getName() {
    return videoWallData.getName().get();
  }

  @Override
  public int getColumns() {
    return videoWallData.getHorzCount();
  }

  @Override
  public int getRows() {
    return videoWallData.getVertCount();
  }

  @Override
  public int getRowHeight(int row) {
    if (videoWallData.isMultipleResolutionEnable()) {
      List<Integer> list = videoWallData.getLayoutData().getHeights();
      if (row >= 0 && row < list.size()) {
        return list.get(row);
      } else {
        return 0;
      }
    } else {
      return videoWallData.getResolutionHeight();
    }
  }

  @Override
  public int getColumnWidth(int column) {
    if (videoWallData.isMultipleResolutionEnable()) {
      List<Integer> list = videoWallData.getLayoutData().getWidths();
      if (column >= 0 && column < list.size()) {
        return list.get(column);
      } else {
        return 0;
      }
    } else {
      return videoWallData.getResolutionWidth();
    }
  }

  @Override
  public int getTotalWidth() {
    return videoWallData.getLayoutData().getTotalWidth();
  }

  @Override
  public int getTotalHeight() {
    return videoWallData.getLayoutData().getTotalHeight();
  }

  @Override
  public int getVideoCount() {
    return videoWallData.getVideos().size();
  }

  @Override
  public boolean showBgImg() {
    return videoWallData.getOsdData().isEnableBgImg();
  }

  @Override
  public int getBgImgWidth() {
    return videoWallData.getOsdData().getBgImgWidth();
  }

  @Override
  public int getBgImgHeight() {
    return videoWallData.getOsdData().getBgImgHeight();
  }

  @Override
  public VpVideoData getVideoData(int index) {
    if (index >= 0 && index < videoWallData.getVideos().size()) {
      return new CaesarVpVideoData(videoWallData.getVideos().get(index));
    } else {
      return null;
    }
  }

  @Override
  public VpScreenData getScreenData(int row, int column) {
    int index = row * getColumns() + column;
    if (index >= 0 && index < videoWallData.getScreens().size()) {
      List<Rectangle2D> screenAreas = Lists.newArrayList(videoWallData.getLayoutData().getScreenAreas());
      Rectangle2D rect = screenAreas.get(index);

      return new CaesarVpScreenData(videoWallData.getScreens().get(index),
          new VpResolution((int) rect.getWidth(), (int) rect.getHeight(), VpResolution.Type.VIDEO_2K), (int) rect.getMinX(),
          (int) rect.getMinY());
    } else {
      return null;
    }
  }

  @Override
  public VpOsdData getOsdData() {
    return new CaesarVpOsdData(videoWallData.getOsdData());
  }

  @Override
  public int getCompensationScaleThreshold() {
    return videoWallData.getCompensationScaleThreshold();
  }

  @Override
  public int getLeftCompensation() {
    return videoWallData.getLeftCompensation();
  }

  @Override
  public int getRightCompensation() {
    return videoWallData.getRightCompensation();
  }

  @Override
  public int getTopCompensation() {
    return videoWallData.getTopCompensation();
  }

  @Override
  public int getBottomCompensation() {
    return videoWallData.getBottomCompensation();
  }

  @Override
  public Vp6OutputData createVp6OutputData() {
    return null;
  }

  /**
   * 创建分辨率输出数据.
   */
  private static Vp7ConfigData.Vp7OutputResData createOutputResData(
      long clock,
      int horzSync, int horzBackPorch, int horzDisp, int horzTotal, int horzPolarity,
      int vertSync, int vertBackPorch, int vertDisp, int vertTotal, int vertPolarity) {
    Vp7ConfigData.Vp7OutputResData data = new Vp7ConfigData.Vp7OutputResData();
    data.clock.set(clock);

    data.horzOutput.sync.set(horzSync);
    data.horzOutput.backPorch.set(horzBackPorch);
    data.horzOutput.disp.set(horzDisp);
    data.horzOutput.total.set(horzTotal);
    data.horzOutput.polarity.set((byte) horzPolarity);

    data.vertOutput.sync.set(vertSync);
    data.vertOutput.backPorch.set(vertBackPorch);
    data.vertOutput.disp.set(vertDisp);
    data.vertOutput.total.set(vertTotal);
    data.vertOutput.polarity.set((byte) vertPolarity);

    return data;
  }
  
  @Override
  public Vp7ConfigData.Vp7OutputResData createVp7OutputData() {
    Vp7ConfigData.Vp7OutputResData result = new Vp7ConfigData.Vp7OutputResData();
    if (videoWallData.getOutputData().isEnable()) {
      result.clock.set(videoWallData.getOutputData().getClock());
      result.horzOutput.disp.set(videoWallData.getResolutionWidth());
      result.horzOutput.backPorch.set(videoWallData.getOutputData().getHorzBackPorch());
      result.horzOutput.total.set(videoWallData.getOutputData().getHorzFrontPorch() + result.horzOutput.disp.get()
          + videoWallData.getOutputData().getHorzBackPorch() + videoWallData.getOutputData().getHorzSync());
      result.horzOutput.polarity.set(videoWallData.getOutputData().horzPolarity.get() ? (byte) 1 : (byte) 0);
      result.horzOutput.sync.set(videoWallData.getOutputData().getHorzSync());

      result.vertOutput.disp.set(videoWallData.getResolutionHeight());
      result.vertOutput.backPorch.set(videoWallData.getOutputData().getVertBackPorch());
      result.vertOutput.total.set(videoWallData.getOutputData().getVertFrontPorch() + result.vertOutput.disp.get()
          + videoWallData.getOutputData().getVertBackPorch() + videoWallData.getOutputData().getVertSync());
      result.vertOutput.polarity.set(videoWallData.getOutputData().vertPolarity.get() ? (byte) 1 : (byte) 0);
      result.vertOutput.sync.set(videoWallData.getOutputData().getVertSync());
    } else {
      // 查找对应时序
      int index = Arrays.binarySearch(DMT_TIMINGS,
          createOutputResData(0, 0, 0, videoWallData.getResolutionWidth(), 0, 0, 0, 0, videoWallData.getResolutionHeight(), 0, 0),
          (o1, o2) -> {
            if (o1.horzOutput.disp.get() == o2.horzOutput.disp.get() && o1.vertOutput.disp.get() == o2.vertOutput.disp.get()) {
              return 0;
            } else if (o1.horzOutput.disp.get() < o2.horzOutput.disp.get()) {
              return -1;
            } else if (o1.horzOutput.disp.get() == o2.horzOutput.disp.get()
                && o1.vertOutput.disp.get() < o2.vertOutput.disp.get()) {
              return -1;
            } else {
              return 1;
            }
          });
      if (index >= 0) {
        result.horzOutput.sync.set(DMT_TIMINGS[index].horzOutput.sync.get());
        result.horzOutput.backPorch.set(DMT_TIMINGS[index].horzOutput.backPorch.get());
        result.horzOutput.disp.set(DMT_TIMINGS[index].horzOutput.disp.get());
        result.horzOutput.total.set(DMT_TIMINGS[index].horzOutput.total.get());
        result.horzOutput.polarity.set(DMT_TIMINGS[index].horzOutput.polarity.get());

        result.vertOutput.sync.set(DMT_TIMINGS[index].vertOutput.sync.get());
        result.vertOutput.backPorch.set(DMT_TIMINGS[index].vertOutput.backPorch.get());
        result.vertOutput.disp.set(DMT_TIMINGS[index].vertOutput.disp.get());
        result.vertOutput.total.set(DMT_TIMINGS[index].vertOutput.total.get());
        result.vertOutput.polarity.set(DMT_TIMINGS[index].vertOutput.polarity.get());
        result.clock.set(DMT_TIMINGS[index].clock.get());
      } else {
        // Calculate CVT-RB standard parameters based on resolution width and height
        result.horzOutput.disp.set((short) videoWallData.getResolutionWidth());
        result.horzOutput.backPorch.set((short) 80);
        result.horzOutput.sync.set((short) 32);
        result.horzOutput.total.set(
            (short) (result.horzOutput.sync.get() + result.horzOutput.backPorch.get() + result.horzOutput.disp.get() + 48));
        result.horzOutput.polarity.set((byte) 1);

        result.vertOutput.disp.set((short) videoWallData.getResolutionHeight());
        result.vertOutput.backPorch.set((short) 54);
        result.vertOutput.sync.set((short) 5);
        result.vertOutput.total.set(
            (short) (result.vertOutput.sync.get() + result.vertOutput.backPorch.get() + result.vertOutput.disp.get() + 3));
        result.vertOutput.polarity.set((byte) 0);
        result.clock.set((long) result.horzOutput.total.get() * result.vertOutput.total.get() * 60); // 默认刷新率为60
      }
    }

    return result;
  }

  @Override
  public Vp7ConfigData.Vp7TpgConfig createVp7TestFrameData() {
    Vp7ConfigData.Vp7TpgConfig result = new Vp7ConfigData.Vp7TpgConfig();
    result.mode.set((byte) videoWallData.getTestFrameData().getMode().getValue());
    result.color.set(
        (long) videoWallData.getTestFrameData().getRgbWithoutAlpha() + ((long) videoWallData.getTestFrameData().getAlpha()
            << 24));
    result.speed.set(videoWallData.getTestFrameData().getSpeed());
    return result;
  }
}
