package com.mc.tool.caesar.vpm.pages.matrixgrid;

import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class CaesarMatrixGridConfigView extends VBox {

  @Getter private CaesarMatrixGridConfigController controller;

  /** . */
  public CaesarMatrixGridConfigView() {
    try {
      controller =
          InjectorProvider.getInjector().getInstance(CaesarMatrixGridConfigController.class);
      URL location =
          getClass().getResource("/com/mc/tool/caesar/vpm/pages/matrixgrid/matrixgrid_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setResources(
          ResourceBundle.getBundle("com/mc/tool/caesar/vpm/pages/matrixgrid/Bundle"));
      loader.setController(controller);
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load matrixgrid/matrixgrid_view.fxml", exc);
    }
  }

  public void setEntity(Entity entity) {
    controller.setEntity(entity);
  }
}
