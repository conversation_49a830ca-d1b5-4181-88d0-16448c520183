package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.Bundle;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.FunctionKey.Cmd.Command;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.VpDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData.VideoWallData;
import com.mc.tool.caesar.api.interfaces.DataObject;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javafx.beans.binding.ListBinding;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class Param1List extends ListBinding<MacroParam> {
  private final ConfigDataManager dataManager;
  private final VpDataModel vpDataModel;
  private final ObjectProperty<DataObject> object;
  private ObjectProperty<MacroKeyItem> itemProperty;
  private WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * .
   *
   * @param object 宏归属的对象
   * @param model 数据模型
   */
  public Param1List(ObjectProperty<DataObject> object, CaesarSwitchDataModel model) {
    this.dataManager = model.getConfigDataManager();
    this.vpDataModel = model.getVpDataModel();
    this.object = object;
    model.addPropertyChangeListener(
        new String[] {ScenarioData.PROPERTY_DATA_CHANGED, UserData.PROPERTY_VIDEOWALL_RIGHTS},
        weakAdapter.wrap(
            (PropertyChangeListener) event -> PlatformUtility.runInFxThread(this::invalidate)));
  }

  /**
   * 设置当前macrokeyitem的属性.
   *
   * @param itemProperty 属性
   */
  public void setItemProperty(ObjectProperty<MacroKeyItem> itemProperty) {
    this.itemProperty = itemProperty;
    if (itemProperty.get() != null) {
      bind(itemProperty.get().getCmd());
    }
    itemProperty.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              if (oldValue != null) {
                unbind(oldValue.getCmd());
                invalidate();
              }
              if (newValue != null) {
                bind(newValue.getCmd());
                invalidate();
              }
            }));
  }

  protected MacroKeyItem getItem() {
    if (itemProperty != null) {
      return itemProperty.get();
    } else {
      return null;
    }
  }

  @Override
  protected ObservableList<MacroParam> computeValue() {
    MacroKeyItem item = getItem();
    return getList(object.get(), item, dataManager, vpDataModel);
  }

  protected static ObservableList<MacroParam> getList(DataObject dataObject, MacroKeyItem item,
                                                      ConfigDataManager dataManager,
                                                      VpDataModel vpDataModel) {
    if (item == null) {
      return FXCollections.emptyObservableList();
    }
    Command command = item.getCmd().get();
    if (command == null) {
      return FXCollections.emptyObservableList();
    }
    ObservableList<MacroParam> result = FXCollections.observableArrayList();
    switch (command) {
      case Connect:
      case ConnectVideo:
      case Disconnect:
        List<ConsoleData> consoles =
            new ArrayList<>(dataManager.getConsoles(CaesarConstants.Console.Status.ACTIVE));
        consoles.sort(Comparator.comparingInt(ConsoleData::getId));
        result.add(new MacroDataObjectParam(ConstantParam.CURRENT_CONDEVICE_VALUE));
        List<MacroDataObjectParam> collect =
            consoles.stream()
                .filter(pre -> !pre.isStatusVpcon())
                .map(MacroDataObjectParam::new)
                .collect(Collectors.toList());
        result.addAll(collect);
        break;
      case Push:
      case PushVideo:
      case Get:
      case GetVideo:
        List<ConsoleData> cons1 =
            new ArrayList<>(dataManager.getConsoles(CaesarConstants.Console.Status.ACTIVE));
        cons1.sort(Comparator.comparingInt(ConsoleData::getId));
        result.add(new MacroDataObjectParam(ConstantParam.CURRENT_CONDEVICE_VALUE));
        List<MacroDataObjectParam> multiViews1 =
            cons1.stream()
                .filter(pre -> !pre.isStatusVpcon())
                .filter(pre -> !pre.isMultiview())
                .map(MacroDataObjectParam::new)
                .collect(Collectors.toList());
        result.addAll(multiViews1);
        break;
      case ActiveVideoWallScenario:
        handleActiveVideoWallScenario(dataObject, vpDataModel, result);
        break;
      case PushVideoWallScenarioWindow:
        handlePushVideoWallScenarioWindow(dataObject, vpDataModel, result);
        break;
      case SWITCH_MULTIVIEW_LAYOUT:
        if (dataObject instanceof ConsoleData && !((ConsoleData) dataObject).isMultiview()) {
          break;
        }
        result.add(new MacroMultiviewParam(0, Bundle.multiviewLayout_full_screen()));
        result.add(new MacroMultiviewParam(1, Bundle.multiviewLayout_two_grid()));
        result.add(new MacroMultiviewParam(2, Bundle.multiviewLayout_four_grid()));
        result.add(new MacroMultiviewParam(3, Bundle.multiviewLayout_side_bar()));
        break;
      case SWITCH_MULTIVIEW_SIGNAL:
        if (dataObject instanceof ConsoleData && !((ConsoleData) dataObject).isMultiview()) {
          break;
        }
        result.add(new MacroMultiviewParam(0, Bundle.multiviewChannel(0)));
        result.add(new MacroMultiviewParam(1, Bundle.multiviewChannel(1)));
        result.add(new MacroMultiviewParam(2, Bundle.multiviewChannel(2)));
        result.add(new MacroMultiviewParam(3, Bundle.multiviewChannel(3)));
        break;
      case SWITCH_MULTIVIEW_PUSH:
      case SWITCH_MULTIVIEW_GET:
        List<ConsoleData> cons2 =
            new ArrayList<>(dataManager.getConsoles(CaesarConstants.Console.Status.ACTIVE));
        cons2.sort(Comparator.comparingInt(ConsoleData::getId));
        result.add(new MacroDataObjectParam(ConstantParam.CURRENT_CONDEVICE_VALUE));
        List<MacroDataObjectParam> multiViews2 =
            cons2.stream()
                .filter(pre -> !pre.isStatusVpcon())
                .filter(ConsoleData::isMultiview)
                .map(MacroDataObjectParam::new)
                .collect(Collectors.toList());
        result.addAll(multiViews2);
        break;
      default:
        break;
    }
    return result;
  }


  /**
   * 处理激活视频墙预案命令参数生成.
   */
  static void handleActiveVideoWallScenario(DataObject dataObject, VpDataModel vpDataModel,
                                            ObservableList<MacroParam> result) {
    processVideoWalls(dataObject, vpDataModel, result, (scenario, wallParam) -> wallParam.addChild(
        new MacroScenarioDataParam(scenario.getIndex(), scenario.getConfigName())));
  }

  /**
   * 处理推送视频墙预案窗口命令参数生成.
   */
  static void handlePushVideoWallScenarioWindow(DataObject dataObject, VpDataModel vpDataModel,
                                                ObservableList<MacroParam> result) {
    processVideoWalls(dataObject, vpDataModel, result, (scenario, wallParam) -> {
      // 过滤无效的视频墙预案
      if (scenario.getVideoWallData() == null || scenario.getVideoWallData().getVideoCnt() <= 0) {
        return;
      }

      // 创建预案参数并添加子窗口参数
      MacroScenarioDataParam param =
          new MacroScenarioDataParam(scenario.getIndex(), scenario.getConfigName());
      VideoWallData vwData = scenario.getVideoWallData();
      for (int j = 0; j < vwData.getVideoCnt(); j++) {
        param.addChild(new MacroScenarioWindowParam(
            scenario.getIndex(),
            scenario.getConfigName(),
            vwData.getCompleteVideoData(j).getName())); // 添加每个视频窗口的参数
      }
      if (!param.getChildren().isEmpty()) {
        wallParam.addChild(param); // 将有效预案参数添加到视频墙
      }
    });
  }

  /**
   * 核心视频墙处理逻辑.
   */
  static void processVideoWalls(DataObject dataObject,
                                VpDataModel vpDataModel,
                                ObservableList<MacroParam> result,
                                VideoWallProcessor processor) {
    for (int i = 0; i < VideoWallGroupData.GROUP_COUNT; i++) {
      VideoWallData videoWallData = vpDataModel.getVideoWallData(i);
      if (!isValidVideoWall(dataObject, i, videoWallData)) {
        continue;
      }

      MacroVideoWallParam wallParam = createVideoWallParam(i, videoWallData);
      processScenarios(vpDataModel.getVideoWallScenarios(i), wallParam, processor);

      if (!wallParam.getChildren().isEmpty()) {
        result.add(wallParam);
      }
    }
  }


  /**
   * 验证视频墙有效性及访问权限.
   */
  static boolean isValidVideoWall(DataObject dataObject, int index, VideoWallData videoWallData) {
    if (!videoWallData.isDataValid()) {
      return false;
    }

    if (dataObject instanceof UserData) {
      UserData user = (UserData) dataObject;
      return user.hasRightAdmin() || user.hasVideoWallAccess(index);
    }
    return true;
  }

  /**
   * 视频墙参数对象工厂方法.
   */
  static MacroVideoWallParam createVideoWallParam(int index, VideoWallData videoWallData) {
    return new MacroVideoWallParam(index, videoWallData.getName());
  }

  /**
   * 预案数据处理模板方法.
   */
  static void processScenarios(ScenarioData[] scenarios, MacroVideoWallParam wallParam,
                               VideoWallProcessor processor) {
    for (ScenarioData scenario : scenarios) {
      processor.processScenario(scenario, wallParam);
    }
  }

  /**
   * 视频墙预案处理接口（策略模式）.
   */
  interface VideoWallProcessor {
    /**
     * 处理单个预案数据.
     */
    void processScenario(ScenarioData scenario, MacroVideoWallParam wallParam);
  }
}
