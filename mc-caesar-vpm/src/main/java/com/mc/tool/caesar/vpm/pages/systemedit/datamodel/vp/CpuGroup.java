package com.mc.tool.caesar.vpm.pages.systemedit.datamodel.vp;

import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class CpuGroup extends VisualEditGroup {
  @Getter @Setter private TxRxGroupData txRxGroupData;

  @Override
  public boolean isTx() {
    return true;
  }
}
