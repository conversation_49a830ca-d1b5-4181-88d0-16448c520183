package com.mc.tool.caesar.vpm.pages.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData.MultiScreenLayout;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.datamodel.CaesarIrregularCrossScreenData;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.datamodel.CaesarIrregularCrossScreenData.CrossScreenMode;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.operation.videowall.datamodel.VideoData;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javafx.beans.property.ObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class CaesarIrregularCrossScreenFunc extends CrossScreenFunc {

  @Expose @Getter protected int multiScreenOid = -1;
  @Getter protected MultiScreenData deviceData;

  @Expose
  protected CaesarIrregularCrossScreenData crossScreenData = new CaesarIrregularCrossScreenData();

  private SimpleObservable resetObservable = new SimpleObservable();

  private boolean hasNameListener = false;

  /**
   * 增加一个名字修改监听器.
   *
   * @param deviceController 设备控制器
   */
  public synchronized void addNameListener(CaesarDeviceController deviceController) {
    if (!hasNameListener) {
      hasNameListener = true;
      // 监听名称修改
      this.nameProperty()
          .addListener(
              (obs, oldVal, newVal) -> {
                if (this.getDeviceData() == null
                    || this.getDeviceData().isStatusNew()
                    || !this.getDeviceData().isStatusActive()) {
                  return;
                }
                deviceController.execute(
                    () -> {
                      this.getDeviceData().setName(this.getName());
                      System.out.println("change name: " + this.getName());
                      try {
                        deviceController
                            .getDataModel()
                            .sendMultiscreenData(Collections.singletonList(this.getDeviceData()));
                      } catch (DeviceConnectionException | BusyException exception) {
                        log.warn("Fail to send multiscreen data!", exception);
                      }
                    });
              });
    }
  }

  public CaesarIrregularCrossScreenFunc() {
    crossScreenData.resetCapacity(16);
  }

  /**
   * 设置设备的异形跨屏数据.
   *
   * @param deviceData 跨屏数据.
   */
  public void setDeviceData(MultiScreenData deviceData) {
    this.deviceData = deviceData;
    if (deviceData != null) {
      multiScreenOid = deviceData.getOid();
    } else {
      multiScreenOid = -1;
    }
  }

  /**
   * .
   */
  public boolean canAddChild() {
    return getChildren().size()
            < crossScreenData.getRows().get() * crossScreenData.getColumns().get()
        && deviceData != null
        && deviceData.isStatusNew();
  }

  /**
   * 设置异形跨屏布局.
   *
   * @param row 行数
   * @param column 列数
   */
  public void setLayout(int row, int column) {
    crossScreenData.getRows().set(row);
    crossScreenData.getColumns().set(column);
    crossScreenData.resetCapacity(row * column);
    resetObservable.update();
  }

  public CaesarIrregularCrossScreenData getCaesarCrossScreenData() {
    return crossScreenData;
  }

  /**
   * 从设备的异形跨屏数据读取到当前数据.
   *
   * @param matrix 矩阵
   * @param controller 设备控制器
   */
  public void reload(CaesarMatrix matrix, CaesarDeviceController controller) {
    setName(deviceData.getName());
    addNameListener(controller);
    crossScreenData.getName().set(deviceData.getName());
    crossScreenData.getDisplayTime().set(deviceData.getUsFrame());
    crossScreenData.getMode().set(CrossScreenMode.IRREGULAR);
    int ctrlId = deviceData.getCtrlConId();
    ConfigDataManager dataManager = controller.getDataModel().getConfigDataManager();
    crossScreenData.getControlSource().set(findTerminalById(matrix, dataManager, ctrlId));
    for (int i = 0;
        i < Math.min(CaesarConstants.MultiScreen.MAX_CON, crossScreenData.getTargets().size());
        i++) {
      VisualEditTerminal terminalById =
          findTerminalById(matrix, dataManager, deviceData.getConInfo(i).getConId());
      crossScreenData.getTarget(i).set(terminalById);
    }
    crossScreenData.getVideos().clear();
    List<MultiScreenLayout> layouts = deviceData.getLayouts();
    for (int i = 0; i < Math.min(CaesarConstants.MultiScreen.MAX_CON, layouts.size()); i++) {
      ObjectProperty<VisualEditTerminal> target = crossScreenData.getTarget(i);
      if (target != null && target.get() != null) {
        MultiScreenLayout layout = layouts.get(i);
        VideoData data = new VideoData();
        data.getSource().set(crossScreenData.getTarget(i).get());
        data.getXpos().set(layout.getX1() * 100 / 2);
        data.getYpos().set(layout.getY1() * 100 / 2);
        data.getWidth().set((layout.getX2() - layout.getX1()) * 100 / 2);
        data.getHeight().set((layout.getY2() - layout.getY1()) * 100 / 2);
        crossScreenData.getVideos().add(data);
      } else {
        crossScreenData.getVideos().add(null);
      }
    }
    setLayout(deviceData.getVerticalNumber(), deviceData.getHorizontalNumber());
  }

  protected VisualEditTerminal findTerminalById(
      CaesarMatrix matrix, ConfigDataManager dataManager, int id) {
    if (id == 0) {
      return null;
    }
    ConsoleData consoleData = dataManager.getConsoleData4Id(id);
    if (consoleData == null) {
      return null;
    }
    return matrix.findTerminal(consoleData);
  }

  /** 把当前数据写入到设备数据. */
  public void commit() {
    deviceData.setName(nameProperty.get());
    deviceData.setHorizontalNumber(crossScreenData.getColumns().get());
    deviceData.setVerticalNumber(crossScreenData.getRows().get());
    deviceData.setUsFrame(crossScreenData.getDisplayTime().get());
    deviceData.setType(CrossScreenMode.IRREGULAR.ordinal());
    VisualEditTerminal controlTerminal = crossScreenData.getControlSource().get();
    if (controlTerminal instanceof CaesarConTerminal) {
      CaesarConTerminal conTerminal = (CaesarConTerminal) controlTerminal;
      if (conTerminal.getConsoleData() != null) {
        deviceData.setCtrlConId(conTerminal.getConsoleData().getId());
      } else {
        log.warn("Console is null!");
      }
    } else {
      log.warn("Terminal is not con!");
    }
    //
    List<ObjectProperty<VisualEditTerminal>> nonNullTargets =
        crossScreenData.getTargets().stream().filter(item -> item.get() != null)
            .collect(Collectors.toList());
    List<VideoData> nonNullVideos =
        crossScreenData.getVideos().stream().filter(Objects::nonNull).collect(Collectors.toList());
    int count = nonNullTargets.size();
    for (int i = 0; i < count; i++) {
      VisualEditTerminal terminal = crossScreenData.getTarget(i).get();
      if (terminal instanceof CaesarConTerminal) {
        CaesarConTerminal conTerminal = (CaesarConTerminal) terminal;
        if (conTerminal.getConsoleData() != null) {
          deviceData.setConInfoId(i, conTerminal.getConsoleData().getId());
        } else {
          deviceData.setConInfoId(i, 0);
        }
      } else {
        deviceData.setConInfoId(i, 0);
      }
      VideoData videoData = nonNullVideos.get(i);
      if (videoData != null) {
        MultiScreenLayout layout = new MultiScreenLayout();
        layout.setX1(videoData.getXpos().get() / 100 * 2);
        layout.setY1(videoData.getYpos().get() / 100 * 2);
        layout.setX2((videoData.getXpos().get() + videoData.getWidth().get()) / 100 * 2);
        layout.setY2((videoData.getYpos().get() + videoData.getHeight().get()) / 100 * 2);
        deviceData.setLayout(i, layout);
      }
    }
    for (int i = count; i < CaesarConstants.MultiScreen.MAX_CON; i++) {
      deviceData.setConInfoId(i, 0);
    }
  }

  @Override
  public void init() {
    super.init();

    getAllTerminalChild()
        .addListener(
            (ListChangeListener<VisualEditTerminal>)
                change -> {
                  while (change.next()) {
                    if (change.wasRemoved()) {
                      for (VisualEditTerminal item : change.getRemoved()) {
                        removeItem(item);
                      }
                    }

                    if (change.wasAdded()) {
                      for (VisualEditTerminal item : change.getAddedSubList()) {
                        addItem(item);
                        if (crossScreenData.getControlSource().get() == null) {
                          crossScreenData.getControlSource().set(item);
                        }
                      }
                    }
                  }
                });
  }

  protected void addItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreenData.getTargets()) {
      if (property.get() == null) {
        property.set(item);
        if (!crossScreenData.getConnections().containsKey(item)) {
          SeatConnection connection = new SeatConnection();
          connection.setRx(item);
          crossScreenData.getConnections().put(item, connection);
        }
        break;
      }
    }
  }

  protected void removeItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }

    for (ObjectProperty<VisualEditTerminal> property : crossScreenData.getTargets()) {
      if (property.get() == item) {
        property.set(null);
      }
    }
    crossScreenData.getConnections().remove(item);
  }

  @Override
  public CrossScreenObject getCrossScreenData() {
    return crossScreenData;
  }

  @Override
  public void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn) {
    if (fromRow < 0
        || fromRow >= getMaxRow()
        || fromColumn < 0
        || fromColumn >= getMaxColumn()
        || toRow < 0
        || toRow >= getMaxRow()
        || toColumn < 0
        || toColumn >= getMaxColumn()) {
      return;
    }

    int fromIndex = getIndex(fromRow, fromColumn);
    int toIndex = getIndex(toRow, toColumn);
    ObjectProperty<VisualEditTerminal> from = crossScreenData.getTarget(fromIndex);
    VisualEditTerminal target = from.get();
    from.set(null);
    crossScreenData.insertTarget(target, toIndex);
  }

  protected int getIndex(int row, int column) {
    return row * getMaxColumn() + column;
  }

  @Override
  public Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal) {
    int index = crossScreenData.indexOfTarget(terminal);
    if (index < 0) {
      return new Pair<>(-1, -1);
    } else {
      return new Pair<>(index / getMaxColumn(), index % getMaxColumn());
    }
  }

  @Override
  public boolean addScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public boolean removeScenario(CrossScreenObject object) {
    return false;
  }

  @Override
  public ObservableList<? extends CrossScreenObject> getScenarios() {
    return null;
  }

  @Override
  public int getMaxRow() {
    return crossScreenData.getRows().get();
  }

  @Override
  public int getMaxColumn() {
    return crossScreenData.getColumns().get();
  }

  @Override
  public boolean isScreenMovable() {
    return deviceData != null && deviceData.isStatusNew();
  }

  @Override
  public boolean isChildIndexChangable() {
    return false;
  }

  @Override
  public SimpleObservable getResetObservable() {
    return resetObservable;
  }
}
