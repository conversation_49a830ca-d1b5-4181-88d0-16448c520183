package com.mc.tool.caesar.vpm.view;

import com.google.inject.Module;
import com.mc.tool.caesar.vpm.CaesarModule;
import com.mc.tool.caesar.vpm.pages.extenderosdupdate.CaesarExtenderOsdUpdatePage;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.view.FrameView;
import javafx.scene.Scene;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;

/**
 * .
 */
public class CaesarView extends FrameView {

  @Override
  public Module getGuiceModule() {
    return new CaesarModule();
  }

  @Override
  protected void initScene(Scene scene) {
    super.initScene(scene);

    KeyCombination kcvisible =
        new KeyCodeCombination(
            KeyCode.O,
            KeyCombination.CONTROL_DOWN,
            KeyCombination.ALT_DOWN,
            KeyCombination.SHIFT_DOWN);
    KeyCombination kchide =
        new KeyCodeCombination(
            KeyCode.P,
            KeyCombination.CONTROL_DOWN,
            KeyCombination.ALT_DOWN,
            KeyCombination.SHIFT_DOWN);
    scene
        .getAccelerators()
        .put(
            kcvisible,
            () -> {
              ApplicationBase applicationBase =
                  InjectorProvider.getInjector().getInstance(ApplicationBase.class);
              if (applicationBase != null) {
                applicationBase
                    .getViewManager()
                    .setPageVisible(CaesarExtenderOsdUpdatePage.NAME, true);
              }
            });
    scene
        .getAccelerators()
        .put(
            kchide,
            () -> {
              ApplicationBase applicationBase =
                  InjectorProvider.getInjector().getInstance(ApplicationBase.class);
              if (applicationBase != null) {
                applicationBase
                    .getViewManager()
                    .setPageVisible(CaesarExtenderOsdUpdatePage.NAME, false);
              }
            });
  }
}
