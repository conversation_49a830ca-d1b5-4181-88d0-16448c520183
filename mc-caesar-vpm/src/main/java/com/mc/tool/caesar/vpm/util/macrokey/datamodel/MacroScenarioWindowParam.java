package com.mc.tool.caesar.vpm.util.macrokey.datamodel;

import java.util.Collection;
import java.util.Collections;
import lombok.Data;

/**
 * .
 */
@Data
public class MacroScenarioWindowParam implements MacroParam {
  private int index;
  private String scenarioName;
  private String windowName;

  MacroScenarioWindowParam(int index, String scenarioName, String windowName) {
    this.index = index;
    this.scenarioName = scenarioName;
    this.windowName = windowName;
  }

  @Override
  public String getFullName() {
    return String.format("[%d] %s (%s)", index, scenarioName, windowName);
  }

  @Override
  public String getSimpleName() {
    return windowName;
  }

  @Override
  public Collection<MacroParam> getChildren() {
    return Collections.emptyList();
  }

  @Override
  public void addChild(MacroParam param) {}

  @Override
  public void removeChild(MacroParam param) {}

  @Override
  public boolean hasOid() {
    return false;
  }

  @Override
  public int getOid() {
    return 0;
  }
}
