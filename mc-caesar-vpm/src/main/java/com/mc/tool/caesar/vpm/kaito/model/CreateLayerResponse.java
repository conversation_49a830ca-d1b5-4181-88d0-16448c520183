package com.mc.tool.caesar.vpm.kaito.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import javax.validation.Valid;
import lombok.Setter;

/**
 * CreateLayerResponse.
 */
@Setter
@JsonPropertyOrder({
    CreateLayerResponse.JSON_PROPERTY_SEQ,
    CreateLayerResponse.JSON_PROPERTY_LAYERS
})
public class CreateLayerResponse {
  public static final String JSON_PROPERTY_SEQ = "seq";
  private Integer seq;

  public static final String JSON_PROPERTY_LAYERS = "layers";
  private List<VideoWallLayer> layers = null;


  public CreateLayerResponse seq(Integer seq) {
    this.seq = seq;
    return this;
  }

  /**
   * 大屏修改序号.
   **/
  @Nullable
  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public Integer getSeq() {
    return seq;
  }


  public CreateLayerResponse layers(List<VideoWallLayer> layers) {
    this.layers = layers;
    return this;
  }

  /**
   * .
   */
  public CreateLayerResponse addLayersItem(VideoWallLayer layersItem) {
    if (this.layers == null) {
      this.layers = new ArrayList<>();
    }
    this.layers.add(layersItem);
    return this;
  }

  /**
   * 新增的图层.
   **/
  @Nullable
  @Valid
  @JsonProperty(JSON_PROPERTY_LAYERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public List<VideoWallLayer> getLayers() {
    return layers;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateLayerResponse createLayerResponse = (CreateLayerResponse) o;
    return Objects.equals(this.seq, createLayerResponse.seq)
        && Objects.equals(this.layers, createLayerResponse.layers);
  }

  @Override
  public int hashCode() {
    return Objects.hash(seq, layers);
  }


  @Override
  public String toString() {
    return "CreateLayerResponse {\n"
        + "    seq: " + toIndentedString(seq) + "\n"
        + "    layers: " + toIndentedString(layers) + "\n" + "}";
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
