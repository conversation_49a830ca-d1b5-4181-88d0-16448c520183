package com.mc.tool.caesar.vpm.pages.operation.rearrange;

import javafx.scene.control.Skin;
import org.controlsfx.control.ListSelectionViewEx;

/**
 * .
 */
public class RearrangeDialogListSelectionView<T> extends ListSelectionViewEx<T> {

  private int maxSize;

  public RearrangeDialogListSelectionView(int maxSize) {
    super();
    this.maxSize = maxSize;
  }

  @Override
  protected Skin<ListSelectionViewEx<T>> createDefaultSkin() {
    return new RearrangeListSelectionViewSkin<>(this, maxSize);
  }
}
