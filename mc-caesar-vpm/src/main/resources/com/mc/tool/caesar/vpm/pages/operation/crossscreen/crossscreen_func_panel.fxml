<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane?>
<?import com.mc.tool.caesar.vpm.pages.operation.videowall.view.CaeasrVideoWallVideoSourceTree?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.RadioButton?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.ToggleGroup?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root id="root" prefHeight="720" prefWidth="1280"
  stylesheets="@crossscreen_func_panel.css"
  type="com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane"
  xmlns:fx="http://javafx.com/fxml/1">

  <toolBoxContent>
    <FXCollections fx:factory="observableArrayList">
      <Button id="menu-btn" fx:id="saveScenarioBtn" onAction="#onSaveScenario"
        styleClass="image-button" text="%toolbar.save_scenario" visible="false" managed="false"/>
      <Button id="switch-btn" onAction="#onSwitch" styleClass="image-button"
        text="%toolbar.meeting_room"/>
    </FXCollections>
  </toolBoxContent>

  <sourceListContent>
    <FXCollections fx:factory="observableArrayList">
      <HBox alignment="CENTER_LEFT"
        prefHeight="23.0" styleClass="titleBox">
        <Label text="%cross_screen.source_list_title" styleClass="titleLabel"/>
      </HBox>
      <CaeasrVideoWallVideoSourceTree fx:id="sourceTree" VBox.vgrow="ALWAYS"/>
      <VBox fx:id="videoPreviewContainer" minHeight="139"/>
      <Region minHeight="1"/>
    </FXCollections>
  </sourceListContent>

  <propertyContent>
    <FXCollections fx:factory="observableArrayList">
      <Region minHeight="1" styleClass="seperator"/>
      <HBox alignment="CENTER_LEFT" prefHeight="23.0" styleClass="right-title">
        <Label text="%cross_screen.setting"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
        <Label text="%cross_screen.name"/>
        <TextField fx:id="crossScreenName"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <VBox styleClass="property-box">
        <Label text="%cross_screen.mode"/>
        <RadioButton text="%cross_screen.mode.auto" fx:id="autoMode">
          <toggleGroup>
            <ToggleGroup fx:id="modeGroup"/>
          </toggleGroup>
        </RadioButton>
        <RadioButton text="%cross_screen.mode.manual" toggleGroup="$modeGroup" fx:id="manualMode"/>
      </VBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
        <RadioButton text="%cross_screen.mode.linkage" fx:id="linkageMode"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
        <Label text="%cross_screen.control"/>
        <ComboBox fx:id="controlCombo"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox styleClass="property-box">
        <Label text="%cross_screen.display"/>
        <TextField fx:id="displayTimeText" prefWidth="50"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <VBox styleClass="property-box">
        <Label text="%cross_screen.rx_list"/>
        <TableView fx:id="rxList">
          <columns>
            <TableColumn text="%cross_screen.rx_list.index" fx:id="rxListIndexColumn"
              sortable="false"/>
            <TableColumn text="%cross_screen.rx_list.name" fx:id="rxListNameColumn"
              sortable="false"/>
          </columns>
          <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
          </columnResizePolicy>
        </TableView>
      </VBox>
      <Region VBox.Vgrow="ALWAYS"/>
      <HBox alignment="center_right" id="button-box">
        <Button text="%cross_screen.commit" styleClass="common-button" fx:id="commitButton"
          onAction="#onCommit"/>
        <Button text="%cross_screen.cancel" styleClass="common-button" fx:id="cancelButton"
          onAction="#onCancel"/>
      </HBox>

    </FXCollections>
  </propertyContent>
</fx:root>
