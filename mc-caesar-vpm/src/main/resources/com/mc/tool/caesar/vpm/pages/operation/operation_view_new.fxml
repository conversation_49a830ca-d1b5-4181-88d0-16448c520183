<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<fx:root id="root" prefHeight="720" prefWidth="1280"
  stylesheets="@operation_view_new.css" type="javafx.scene.layout.AnchorPane"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <HBox fx:id="mainContainer" AnchorPane.topAnchor="0"
      AnchorPane.bottomAnchor="0" AnchorPane.leftAnchor="0"
      AnchorPane.rightAnchor="0"/>
    <ComboBox fx:id="functionCombobox" prefWidth="150.0"
      AnchorPane.topAnchor="8" AnchorPane.leftAnchor="10" id="function-combobox"/>
  </children>
</fx:root>
