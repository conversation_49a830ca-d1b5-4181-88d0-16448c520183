@import "/com/mc/tool/framework/common.css";

#main-container {
  -fx-padding: 10 0 10 10;
}

#root {
  -fx-background-color: #ffffff;
}

#screen-combobox {
  -fx-border-radius: 12px;
}

#separator {
  -fx-background-color: #cccccc;
}

#menu-btn {
  /*-fx-graphic: url("../menu_normal.png");*/
  -fx-text-fill: #333333;
}

#menu-btn:hover {
  /*-fx-graphic: url("../menu_hover.png");*/
  -fx-text-fill: #f08519;
}

#show-btn-container {
  -fx-border-width: 0 0 0 1;
  -fx-border-color: #cccccc;
}

#show-btn-sub-container {
  -fx-background-color: #e6e6e6;
}

.image-button {
  -fx-background-radius: 0;
  -fx-border-style: null;
  -fx-background-color: null;
  -fx-background-repeat: no-repeat;
}

#show-property-btn {
  -fx-graphic: url("show_property.png");
}

.separator {
  -fx-background-color: #cccccc;
}

.right-panel {
  -fx-padding: 0 0 0 0;
}

.right-title {
  -fx-background-color: #e6e6e6;
  -fx-alignment: center-left;
  -fx-padding: 0 0 0 5;
}

#hide-btn-container {
  -fx-padding: 0;
}

#hide-property-btn {
  -fx-graphic: url("hide_property.png");
}


#icon-btn {
  -fx-graphic: url("icon_btn.png");
}

#property-btn {
  -fx-graphic: url("property_btn.png");
}

#zoomin-btn {
  -fx-background-image: url("zoomin_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#zoomin-btn:hover {
  -fx-background-image: url("zoomin_hover.png");
}

#zoomin-btn:disabled {
  -fx-background-image: url("zoomin_disable.png");
}

#zoomout-btn {
  -fx-background-image: url("zoomout_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#zoomout-btn:hover {
  -fx-background-image: url("zoomout_hover.png");
}

#zoomout-btn:disabled {
  -fx-background-image: url("zoomout_disable.png");
}

#zoomin-btn:disabled {
  -fx-background-image: url("zoomin_disable.png");
}

#restore-btn {
  -fx-background-image: url("restore_normal.png");
  -fx-background-repeat: no-repeat;
  -fx-background-position: center;
}

#restore-btn:hover {
  -fx-background-image: url("restore_hover.png");
}

#restore-btn:disabled {
  -fx-background-image: url("restore_disable.png");
}

#graph-tool-bar {
  -fx-spacing: 40;
  -fx-background-color: #f7f7f7;
}

#source-list-title-container {
  -fx-background-color: #cccccc;
}

#source-list-title {
  -fx-label-padding: 0 0 0 10;
}

#function-source {
  -fx-border-width: 1px;
  -fx-border-color: #cccccc;
}

#function-content {
  -fx-border-width: 1px;
  -fx-border-color: #cccccc;
}

.property-box {
  -fx-alignment: center_left;
  -fx-padding: 12px;
  -fx-spacing: 4px;
}

#tool-box {
  -fx-border-width: 1px;
  -fx-border-color: #cccccc;
  -fx-padding: 10;
  -fx-spacing: 10px;
}