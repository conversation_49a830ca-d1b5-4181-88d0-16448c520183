config_decoder_group.btn=组配置
edit_decoder_group_dialog.name_label=解码组名称：
edit_decoder_group_dialog.croppable_check=解码卡是否可裁剪
assign_permissions_dialog.croppable_check=是否裁剪
assign_permissions_dialog.yes=是
assign_permissions_dialog.no=否
edit_decoder_group_dialog.select_rx=请选择RX
assign_permissions_selection_view.source_header=未分配权限的信号源
assign_permissions_selection_view.target_header=可上屏的信号源
new_decode_card_dialog.title=添加解码卡
new_decode_card_dialog.header_text=选择解码卡绑定关系
new_decode_card_dialog.delete=删除
new_decode_card_dialog.add_input_card=增加解码卡
new_decode_card_dialog.delete_input_card=删除解码卡
new_decode_card_dialog.input_card=输入卡
create_decoder_group.btn=创建解码组
decoder_group=解码组
host_label=全光拼接主机
delete_decoder_group.btn=删除解码组
splicing_screen_delete_dialog.header=请选择全光拼接屏
splicing_screen_delete_dialog.title=删除全光拼接屏组
edit_decoder_group_dialog.title=创建解码组
edit_decoder_group_dialog.type_requirement=组内的RX分辨率类型必须一致
assign_permissions_dialog.decoder_group_configuration=解码组配置
assign_permissions_dialog.name_label=名称：
splicing_screen_input_dialog.header=请输入全光拼接屏信息
splicing_screen_input_dialog.ip_address=IP地址
splicing_screen_input_dialog.ip_format_error=IP格式错误
splicing_screen_input_dialog.name=名称
name_label=名称
host_type_label=主机类型
kaito_input_list=全光输入卡列表
index_column_text=序号
caesar_card_name_column_text=C卡名称
caesar_card_id_column_text=C卡ID
caesar_card_port_column_text=C卡端口
nowa_card_sn_column_text=N卡SN
nowa_card_id_column_text=N卡ID
nowa_card_port_column_text=N卡端口
splicing_screen_input_dialog.pid=诺瓦接入方ID
splicing_screen_input_dialog.port=端口
splicing_screen_input_dialog.port_format_error=端口的格式错误，请输入一个在1~65536范围内的数字。
splicing_screen_input_dialog.secret_key=诺瓦secretkKey
splicing_screen_input_dialog.title=创建全光拼接屏
splicing_screen_input_dialog.type=诺瓦主机类型
splicingscreen_page.title=拼接屏
toolbar.delete_splicing_screen=删除全光拼接屏组
toolbar.new_decoder=添加解码卡
toolbar.new_splicing_screen=创建全光拼接屏
rx_list.label=RX列表
rx_list.index=序号
rx_list.name=RX名称
rx_list.port=RX端口
rx_list.input_id=拼接器输入ID
rx_list.slot_id=拼接器槽位ID