vp.status.error=无法通讯
ActionType.reboot=重启主机
ActionType.reset=恢复出厂设置
AllowLogin=允许用户ACL
AllowScan=允许TX轮询
ConnectType.full=完全连接
ConnectType.private=私有连接
ConnectType.share_full=共享模式(操作)
ConnectType.share_video=共享模式(视频)
ConnectType.video=视频连接
ConnectionFilter.full=完全连接
ConnectionFilter.private=私有连接
ConnectionFilter.video=视频连接
ForceLogin=强制登录
ForceScan=强制TX轮询
Goto_btn.macro=宏控制
Goto_btn.right=权限管理
Goto_btn.scan=轮询顺序配置
InterfaceStatus=接口状态
MenuDisconnect=断开连接
SystemEditPage.title=系统编辑
alert.reboot.text=是否确认重启？
alert.reboot.title=重启
alert.reboot_alert.text=请重新连接主机设备，IP地址已更改为：
alert.reboot_alert.title=重启
alert.reset.text=是否确认回复出厂设置？
alert.reset.title=恢复出厂设置
allowPrivate=允许私有连接
conName=RX连接
cpuName=TX连接
extenderName=外设
extenderPort=端口
extender_status.activate_extender_binding=激活扩展器绑定
extender_status.dvimode=DVI模式
extender_status.edid_status=EDID状态
extender_status.edidexist=有EDID
extender_status.edidvalid=EDID有效
extender_status.extender_binding=扩展器绑定
extender_status.extender_binding2=外设绑定
extender_status.has_input=有输入
extender_status.hdmimode=HDMI模式
extender_status.interface_count=接口数目
extender_status.interface_type=接口类型
extender_status.invalid=无效
extender_status.keyboardonline=键盘在线
extender_status.mouseonline=鼠标在线
extender_status.name=接口状态
extender_status.no_input=无输入
extender_status.offline=离线
extender_status.online=在线
extender_status.resolution_type=分辨率类型
extender_status.screenonline=屏幕在线
extender_status.udiskonline=U盘在线
extender_status.usb_type=设备
extender_status.usbcableonline=USB线在线
extender_status.valid=有效
extender_status.video_cable_status=视频线状态
extender_status.video_input_status=视频输入状态
extender_status.videoinput=信号源在线
forcePrivate=强制私有连接
host.ipAddress=IP地址
host.macAddress=MAC地址
host.name=主机名称
host.netMask=子网掩码
host.offline=不在线
host.online=在线
host.status=状态
id=ID
losFrame=LOS帧
matrixType=主机类型
menu.videowall.reuse=从离线视频墙创建
menu.rebind=绑定设置
menu.usb=USB端口绑定
menu.usb.create_extender_fail=无法创建新的外设！
menu.usb.dialog_title=USB端口绑定
menu.usb.port_used=端口已被使用！
mode.full=完全连接
mode.private=私有连接
mode.shared=共享模式
mode.video=视频连接
mouseSpeed=鼠标速度
name=设备
osdTspy=OSD透明度(0-255)
rxNum=RX数量
scanTime=轮询时间
serial=序列号
suspendEnable=悬浮窗开启
suspendPos=悬浮窗位置
suspendTime=悬浮窗显示时间
txNum=TX数量
usbConType=U盘使能
version=版本
virtual=虚拟拔出
vp.input=输入{0}ID
vp.output=输出{0}名称
type=类型
gridInterface=级联接口
localPort=本地端口
remoteMatrix=对端主机
remotePort=对端端口
used=占用
unused=未占用
usedStatus=使用状态
vp.status=状态
vp.status.normal=正常
resolution=分辨率
menu.scenariogroup=创建预案组
analog_audio=模拟音频
gridmatrix.name=名称
gridmatrix.connection=连接主机
channel2Port=通道2端口
channel1Port=通道1端口
hdmiSelection=HDMI选择
hdmiSelection.auto=自动
hdmiSelection.hdmi1=HDMI1
hdmiSelection.hdmi2=HDMI2
txVideoQp=图像量化因子
txTouchEnable=触摸屏使能
txTouchEnable.alert.title=警告
txTouchEnable.alert.content=应用该选项将导致设备重启，是否确认？
offset=TX偏移设置
txOffset.dialog.title=TX偏移设置
txOffset.dialog.activateSetting=激活TX偏移设置
txOffset.dialog.deviceName=设备名称:
txOffset.dialog.totalResolution=多屏工作站总分辨率:
txOffset.dialog.resolution1=分辨率-1:
txOffset.dialog.resolution2=分辨率-2:
txOffset.dialog.offsetX=偏移X:
txOffset.dialog.offsetY=偏移Y:
txOffset.activated=激活
txOffset.unactivated=未激活
menu.grouping.tx=创建TX分组
menu.grouping.rx=创建RX分组
menu.grouping.selectable=可选
menu.grouping.selected=已选
menu.grouping.tx.name=TX分组名称
menu.grouping.rx.name=RX分组名称
menu.grouping.multiscreen=创建预案分组
menu.grouping.multiscreen.name=预案组名称
menu.grouping.crossscreen=创建跨屏分组
menu.grouping.crossscreen.name=跨屏组名称
menu.grouping.videowall=创建视频墙
menu.grouping.videowall.name=视频墙名称
menu.grouping.same_name_error=不允许使用重复名称
menu.grouping.illegal_name_len=名称长度非法
changeId.alert.title=提示
changeId.alert.content=修改ID会导致重新刷新数据，是否继续？(外设数量多时，刷新时间可能会长)
txAudioTrigger=音频触发
txEventEnable=事件联动
txTriggerHoldTime=触发保持时间(音频)
txHighCompressionRatio=高压缩率
multiviewLayout=四画面布局
extender_status.special_ext_sub_type=类型备注
multiviewOutputMode=输出模式
vp.channel=通道
vp.ip=IP地址
vp.netmask=子网掩码
vp.gateway=网关
vp.mac=MAC地址
rxOsdMenu=OSD菜单
peer_tx.name=对端TX
extUartBaudRate=波特率
extDoubleDpEnable=DP模式
extIcronEnable=USB透传
dpModeConfirm.title=双DP模式确认
dpModeConfirm.content=更改双DP模式设备将自动重启，确认要继续吗？
menu.group.audio=创建音频镜像组
type.audio_group=音频镜像组
menu.audio_delete.alert=音频组被视频墙占用，不能删除
warning=警告
