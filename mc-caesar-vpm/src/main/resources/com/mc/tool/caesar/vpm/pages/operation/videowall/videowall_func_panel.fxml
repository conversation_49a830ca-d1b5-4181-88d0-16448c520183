<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.caesar.vpm.pages.operation.videowall.view.CaeasrVideoWallVideoSourceTree?>
<?import com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.ToggleButton?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.PropertySheet?>
<fx:root id="root" prefHeight="720" prefWidth="1280"
  stylesheets="@videowall_func_panel.css"
  type="com.mc.tool.caesar.vpm.pages.operation.view.CaesarOperationFuncPane"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">

  <toolBoxContent>
    <FXCollections fx:factory="observableArrayList">
      <Button id="publish-btn" fx:id="publishBtn" onAction="#onPublish"
        styleClass="image-button" text="%toolbar.publish"/>
      <ToggleButton id="pre-window" text="%toolbar.pre_window" styleClass="image-button"
        fx:id="preWindowBtn"/>
      <Button id="save-btn" fx:id="saveScenarioBtn" onAction="#onSaveScenario"
        styleClass="image-button" text="%toolbar.save_scenario"/>
      <Button id="saveas-btn" fx:id="saveAsScenarioBtn" onAction="#onSaveAsScenario"
        styleClass="image-button" text="%toolbar.saveas_scenario"/>
      <Button id="config-btn" fx:id="configBtn" onAction="#onConfig"
        styleClass="image-button" text="%toolbar.config"/>
      <Button id="screen-btn" mnemonicParsing="false" onAction="#onSceenMode"
        styleClass="image-button" text="%toolbar.videowall"/>
      <Button id="switch-btn" onAction="#onSwitch" styleClass="image-button"
        text="%toolbar.meeting_room"/>
    </FXCollections>
  </toolBoxContent>

  <sourceListContent>
    <FXCollections fx:factory="observableArrayList">
      <VBox fx:id="txVbox" VBox.vgrow="ALWAYS">
        <HBox id="source-list-title-container" alignment="CENTER_LEFT"
          prefHeight="23.0">
          <Label id="source-list-title" text="%source_list_title"/>
        </HBox>
        <CaeasrVideoWallVideoSourceTree fx:id="sourceTree" VBox.vgrow="ALWAYS"/>
        <VBox fx:id="videoPreviewContainer" minHeight="139"/>
        <Region minHeight="1"/>
      </VBox>
      <VBox fx:id="rxVbox"
        VBox.vgrow="ALWAYS">
        <children>
          <HBox id="source-list-title-container" alignment="CENTER_LEFT"
            prefHeight="23.0">
            <children>
              <Label id="source-list-title" text="%vpcon_tableview.title"/>
            </children>
          </HBox>
          <TableView fx:id="tableView" prefHeight="200.0" VBox.vgrow="ALWAYS" id="vpcon-table">
            <columns>
              <TableColumn fx:id="cellIndex" prefWidth="43.0" text="%vpcon_tableview.index"/>
              <TableColumn fx:id="cellName" minWidth="1.0"
                prefWidth="73.0" text="%vpcon_tableview.name"/>
              <TableColumn fx:id="cellDpi" minWidth="9.0"
                prefWidth="83.0" text="%vpcon_tableview.dpi"/>
            </columns>
          </TableView>
          <HBox alignment="CENTER" prefHeight="40" fx:id="autoArrangeBox">
            <Button mnemonicParsing="false" onAction="#autoArrange"
              text="%button.auto_arrange" styleClass="common-button" fx:id="autoArrangeBtn"/>
          </HBox>
        </children>
      </VBox>
    </FXCollections>
  </sourceListContent>

  <propertyContent>
    <FXCollections fx:factory="observableArrayList">
      <VBox fx:id="txPropertyBox">
        <Region minHeight="1" styleClass="seperator"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text="%property.logic_layout"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="logicLayoutPropertySheet"/>
        <Region minHeight="1" styleClass="seperator"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text="%property.video_window"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <TableView fx:id="windowsTable">
          <columns>
            <TableColumn text="%property.windows_name" fx:id="windowNameColumn"/>
            <TableColumn text="%property.windows_source" fx:id="windowSourceColumn"/>
          </columns>
          <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
          </columnResizePolicy>
        </TableView>
        <Region minHeight="1" styleClass="seperator"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="%property.video_layout"/>
          </children>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="videoLayoutPropertySheet"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="%property.audio_config"/>
          </children>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="audioGroupPropertySheet"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="%property.test_screen"/>
          </children>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="testScreenPropertySheet"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <children>
            <Label text="%property.config"/>
          </children>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="configPropertySheet"/>
      </VBox>
      <VBox fx:id="rxPropertyBox">
        <Region minHeight="1" styleClass="seperator"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text="%property.layout_properties"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="layoutPropertySheet"/>

        <Region minHeight="1" styleClass="seperator"/>
        <HBox minHeight="20" prefHeight="20" styleClass="right-title">
          <Label text="%property.output"/>
        </HBox>
        <Region minHeight="1" styleClass="seperator"/>
        <PropertySheet fx:id="outputPropertySheet"/>
      </VBox>
    </FXCollections>
  </propertyContent>
</fx:root>
