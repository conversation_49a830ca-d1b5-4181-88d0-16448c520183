<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root id="main-container" stylesheets="@matrixgrid_view.css" type="VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <TableView fx:id="tableView" VBox.vgrow="ALWAYS">
      <columns>
        <TableColumn fx:id="matrixCol" text="%table.columns.master"/>
        <TableColumn fx:id="activateCol" text="%table.columns.activate"/>
        <TableColumn fx:id="deviceCol" text="%table.columns.device"/>
        <TableColumn fx:id="hostCol" text="%table.columns.host"/>
        <TableColumn fx:id="portCol" text="%table.columns.port"/>
        <TableColumn fx:id="masterCol" text="%table.columns.master"/>
        <TableColumn fx:id="connectCol" text="%table.columns.connect"/>
      </columns>
    </TableView>
    <HBox id="tool-box">
      <children>
        <Button fx:id="configBtn" mnemonicParsing="false" styleClass="common-button"
          text="%config-btn" HBox.hgrow="ALWAYS" onAction="#onConfig"/>
      </children>
    </HBox>
  </children>
</fx:root>
