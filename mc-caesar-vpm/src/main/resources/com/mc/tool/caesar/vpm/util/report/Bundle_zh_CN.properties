CreateReportWizardPanel.name=选择内容
CreateReportWizardPanel.selectall=<html><b>选择所有
ReportConstants.Content.Assignment=分配
ReportConstants.Content.ConAcls=访问控制
ReportConstants.Content.ConDevices=CON管控端
ReportConstants.Content.ConFavorites=收藏夹
ReportConstants.Content.ConMacros=宏
ReportConstants.Content.CpuDevices=CPU接入端
ReportConstants.Content.ExtUnits=扩展单元
ReportConstants.Content.MatrixView=主机视图
ReportConstants.Content.System=系统
ReportConstants.Content.User=用户
ReportConstants.Content.UserAcls=访问控制
ReportConstants.Content.UserFavorites=收藏夹
ReportConstants.Content.UserMacros=宏
ReportGenerator.access=登录
ReportGenerator.date=日期:
ReportGenerator.dateandtime=日期与时间
ReportGenerator.device=设备:
ReportGenerator.favorites=收藏夹
ReportGenerator.favorites.missing=没有收藏设定
ReportGenerator.fullAccess=ACL -操作模式
ReportGenerator.fullAccess.missing=没有配置操作模式权限
ReportGenerator.info=配置信息:
ReportGenerator.macros=宏
ReportGenerator.macros.missing=没有设定宏
ReportGenerator.matrixgrid=主机级联网
ReportGenerator.msc=多屏幕控制
ReportGenerator.msc.missing=没有设定多屏幕控制
ReportGenerator.msc.onlineonly=不允许在离线状态编辑
ReportGenerator.name=配置名称:
ReportGenerator.network=网络
ReportGenerator.network.general=通用
ReportGenerator.network.interface1=网络接口1
ReportGenerator.network.interface2=网络接口2
ReportGenerator.network.ldap=LDAP
ReportGenerator.network.snmp=SNMP
ReportGenerator.network.syslog=系统日志
ReportGenerator.network.syslog.server1=Syslog服务器 1
ReportGenerator.network.syslog.server2=Syslog服务器 2
ReportGenerator.network.trap.receiver1=Trap接收器 1
ReportGenerator.network.trap.receiver2=Trap接收器 2
ReportGenerator.switch=切换
ReportGenerator.systemdata=系统数据
ReportGenerator.systemdata.automaticId=自动ID
ReportGenerator.systemdata.general=通用
ReportGenerator.systemdata.osd=OSD数据 (CPU)
ReportGenerator.title=配置报告
ReportGenerator.vcon=虚拟CON接收端设备
ReportGenerator.vcon.missing=虚拟CON管控端未定义
ReportGenerator.vcpu=虚拟CPU接入端设备
ReportGenerator.vcpu.missing=虚拟CPU接入端未定义
ReportGenerator.videoAccess=ACL - 视频模式
ReportGenerator.videoAccess.missing=没有配置视频的访问权利
ReportPlugin.wizard.title=配置报告
ReportWizardIterator.name=保存报告
SystemConfigData.SystemData.Device=设备
SystemConfigData.SystemData.Name=名称
SystemConfigData.SystemData.Info=信息
SystemConfigData.SystemData.ForceBits.AutoSave=自动保存
SystemConfigData.SystemData.ForceBits.ComEcho=允许COM数据回传
SystemConfigData.SystemData.ForceBits.LanEcho=允许LAN口数据回传
SystemConfigData.SystemData.ForceBits.Redundancy=允许冗余
SystemConfigData.SystemData.ForceBits.Synchronize=同步
SystemConfigData.SystemData.ForceBits.EchoOnly=只回传
SystemConfigData.SystemData.MasterIP=主用主机IP地址
SystemConfigData.SystemData.ForceBits.RemoveSlave=移除I/O板卡 
SystemConfigData.SystemData.ForceBits.OnlineConfig=在线配置
SystemConfigData.AutoIdData.ForceBits.AutoConfig=循序自动配置
SystemConfigData.AutoIdData.AutoId.RealCpuId=真实的CPU接入端ID
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=虚拟的CPU接入端ID
SystemConfigData.AutoIdData.AutoId.RealConId=真实的CON管控端ID
SystemConfigData.AutoIdData.AutoId.VirtualConId=虚拟的CON管控端ID
DisplayData.H_Speed=鼠标水平速度[1/x]
DisplayData.V_Speed=鼠标垂直速度[1/x]
DisplayData.C_Speed=双击时间[毫秒]
DisplayData.Keyboard=键盘布局
DisplayData.VideoMode=视频模式
SystemConfigData.AccessData.ForceBits.UserAccess=允许新用户
SystemConfigData.AccessData.ForceBits.ConAccess=允许新的CON管控端
SystemConfigData.AccessData.ForceBits.CpuDisconnct=自动断开
SystemConfigData.AccessData.TimeoutDisplay=OSD超时[秒]
SystemConfigData.AccessData.TimeoutLogout=自动退出[分钟]
SystemConfigData.SwitchData.ForceBits.CpuWatch=允许视频共享
SystemConfigData.SwitchData.ForceBits.CpuConnect=强制连接
SystemConfigData.SwitchData.ForceBits.ConDisconnect=强制断开
SystemConfigData.SwitchData.TimeoutDisconnect=CPU接入端超时[分钟]
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=键盘鼠标连接
SystemConfigData.SwitchData.TimeoutShare=释放时间[秒]
SystemConfigData.SwitchData.ForceBits.FKeySingle=单步骤宏控制
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.Address=IP地址
SystemConfigData.NetworkData.Netmask=子网掩码
SystemConfigData.NetworkData.Gateway=网关
SystemConfigData.NetworkData.MacAddress=MAC地址
SystemData.InternalLogLevel=日志级别
SystemData.InternalLogLevel.Debug=调试
SystemData.InternalLogLevel.Info=信息
SystemData.InternalLogLevel.Notice=通知
SystemData.InternalLogLevel.Warning=警告
SystemData.InternalLogLevel.Error=错误
SystemConfigData.SyslogData.Port=端口
SystemConfigData.SyslogData.SysLevel=日志级别
SystemConfigData.SyslogData.SysLevel.Debug=调试
SystemConfigData.SyslogData.SysLevel.Info=信息
SystemConfigData.SyslogData.SysLevel.Notice=通知
SystemConfigData.SyslogData.SysLevel.Warning=警告
SystemConfigData.SyslogData.SysLevel.Error=错误
SystemConfigData.NetworkData.NetworkBits.Syslog=系统日志
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP代理
SystemConfigData.SnmpData.SnmpBits.Trap=允许过滤
SystemConfigData.SnmpData.Port=端口
SystemConfigData.SnmpData.SnmpBits.Status=状态
SystemConfigData.SnmpData.SnmpBits.Temperature=温度
SystemConfigData.SnmpData.SnmpBits.InsertBoard=插入I/O板卡
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=移除I/O板卡
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=无效I/O板卡
SystemConfigData.SnmpData.SnmpBits.InsertExtender=插入扩展器
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=移除扩展器
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=切换命令
SystemConfigData.SnmpData.SnmpBits.FanTray1=风扇槽1
SystemConfigData.SnmpData.SnmpBits.FanTray2=风扇槽2
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=电源供应器1
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=电源供应器2
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=电源供应器3
SystemConfigData.LdapData.Address=LDAP服务器
SystemConfigData.LdapData.Port=端口
SystemConfigData.LdapData.BaseDN=基本DN
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.SntpData.TimeZone=时区
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.MatrixGridData.ForceBits.Grid=允许主机级联
ControlGroupData.Arrangement=排列
ControlGroupData.Manual=手动
ExtenderData.Port=端口
ExtenderData.RDPort=冗余端口
ExtenderData.ID=ID
ExtenderData.Name=名称
CpuData.ID=ID
CpuData.Name=名称
CpuData.Status.Virtual=虚拟设备
CpuData.Status.AllowPrivate=允许私有模式
CpuData.Status.ForcePrivate=强制私有模式
ConsoleData.ID=ID
ConsoleData.Name=名称
ConsoleData.ConsoleVirtual=操控台分配
ConsoleData.Status.AllowLogin=允许用户 ACL
ConsoleData.Status.ForceLogin=强制登录
ConsoleData.Status.LOSFrame=LOS架构
ConsoleData.Status.ForceScan=强制CPU接入端轮询
ConsoleData.ScanTime=轮询时间[sec]
ConsoleData.Status.ForceMacro=展示宏列表
ConsoleData.Status.OsdDisabled=OSD已禁用
UserData.Name=名字
UserData.FullName=全名
UserData.Rights.ADMIN=管理员
UserData.Rights.SUPER=超级用户
UserData.Rights.POWER=权限用户
UserData.Rights.LDAP=LDAP 用户
UserData.Rights.FTP=FTP
