<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<fx:root fx:id="root" type="javafx.scene.layout.BorderPane" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1">
  <bottom>
    <HBox prefHeight="41.0" BorderPane.alignment="CENTER">
      <children>
        <Region prefHeight="40.0" prefWidth="200.0" HBox.hgrow="ALWAYS"/>
        <Button fx:id="applyButton" mnemonicParsing="false" prefWidth="100.0" text="Button"/>
        <Button fx:id="cancelButton" mnemonicParsing="false" prefWidth="100.0" text="Button">
          <HBox.margin>
            <Insets left="20.0"/>
          </HBox.margin>
        </Button>
      </children>
    </HBox>
  </bottom>
  <center>
    <ScrollPane fx:id="formPane" BorderPane.alignment="CENTER"/>
  </center>
</fx:root>
