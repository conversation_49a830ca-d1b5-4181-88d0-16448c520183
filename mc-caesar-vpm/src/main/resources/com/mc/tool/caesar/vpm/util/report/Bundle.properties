CreateReportWizardPanel.name=Define Content
CreateReportWizardPanel.selectall=<html><b>Select All
ReportConstants.Content.Assignment=Assignment
ReportConstants.Content.ConAcls=Access Control
ReportConstants.Content.ConDevices=CON Devices
ReportConstants.Content.ConFavorites=Favorites
ReportConstants.Content.ConMacros=Macros
ReportConstants.Content.CpuDevices=CPU Devices
ReportConstants.Content.ExtUnits=EXT Units
ReportConstants.Content.MatrixView=Matrix View
ReportConstants.Content.System=System
ReportConstants.Content.User=User
ReportConstants.Content.UserAcls=Access Control
ReportConstants.Content.UserFavorites=Favorites
ReportConstants.Content.UserMacros=Macros
ReportGenerator.access=Access
ReportGenerator.date=Date:
ReportGenerator.dateandtime=Date and Time
ReportGenerator.device=Device:
ReportGenerator.favorites=Favorites
ReportGenerator.favorites.missing=No favorites set
ReportGenerator.fullAccess=ACL - Full Access
ReportGenerator.fullAccess.missing=No full access rights configured
ReportGenerator.info=Configuration Info:
ReportGenerator.macros=Macros
ReportGenerator.macros.missing=No macros set
ReportGenerator.matrixgrid=Matrix Grid
ReportGenerator.msc=Multi-Screen Control
ReportGenerator.msc.missing=Multi-Screen Control not configured
ReportGenerator.msc.onlineonly=Not available in offline config
ReportGenerator.name=Configuration Name:
ReportGenerator.network=Network
ReportGenerator.network.general=General
ReportGenerator.network.interface1=Network Interface 1
ReportGenerator.network.interface2=Network Interface 2
ReportGenerator.network.ldap=LDAP
ReportGenerator.network.snmp=SNMP
ReportGenerator.network.syslog=Syslog
ReportGenerator.network.syslog.server1=Syslog Server 1
ReportGenerator.network.syslog.server2=Syslog Server 2
ReportGenerator.network.trap.receiver1=Trap Receiver 1
ReportGenerator.network.trap.receiver2=Trap Receiver 2
ReportGenerator.switch=Switch
ReportGenerator.systemdata=System Data
ReportGenerator.systemdata.automaticId=Automatic ID
ReportGenerator.systemdata.general=General
ReportGenerator.systemdata.osd=OSD Data (CPU)
ReportGenerator.title=Configuration Report
ReportGenerator.vcon=Virtual CON Devices
ReportGenerator.vcon.missing=Virtual CON Devices not assigned
ReportGenerator.vcpu=Virtual CPU Devices
ReportGenerator.vcpu.missing=Virtual CPU Devices not assigned
ReportGenerator.videoAccess=ACL - Video Access
ReportGenerator.videoAccess.missing=No video access rights configured
ReportPlugin.wizard.title=Configuration Report
ReportWizardIterator.name=Save Report
SystemConfigData.SystemData.Device=Device
SystemConfigData.SystemData.Name=Name
SystemConfigData.SystemData.Info=Info
SystemConfigData.SystemData.ForceBits.AutoSave=Auto Save
SystemConfigData.SystemData.ForceBits.ComEcho=Enable COM Echo
SystemConfigData.SystemData.ForceBits.LanEcho=Enable LAN Echo
SystemConfigData.SystemData.ForceBits.Redundancy=Enable Redundancy
SystemConfigData.SystemData.ForceBits.Synchronize=Synchronize
SystemConfigData.SystemData.ForceBits.EchoOnly=Echo Only
SystemConfigData.SystemData.MasterIP=Master IP Address
SystemConfigData.SystemData.ForceBits.RemoveSlave=Remove I/O Boards
SystemConfigData.SystemData.ForceBits.OnlineConfig=Online Configuration
SystemConfigData.AutoIdData.ForceBits.AutoConfig=Enable Auto Config
SystemConfigData.AutoIdData.AutoId.RealCpuId=ID Real CPU Device
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=ID Virtual CPU Device
SystemConfigData.AutoIdData.AutoId.RealConId=ID Real CON Device
SystemConfigData.AutoIdData.AutoId.VirtualConId=ID Virtual CON Device
DisplayData.H_Speed=Horizontal Mouse Speed [1/x]
DisplayData.V_Speed=Vertical Mouse Speed [1/x]
DisplayData.C_Speed=Double Click Time [ms]
DisplayData.Keyboard=Keyboard Layout
DisplayData.VideoMode=Video Mode
SystemConfigData.AccessData.ForceBits.UserAccess=Enable New User
SystemConfigData.AccessData.ForceBits.ConAccess=Enable New CON
SystemConfigData.AccessData.ForceBits.CpuDisconnct=Auto Disconnect
SystemConfigData.AccessData.TimeoutDisplay=OSD Timeout [sec]
SystemConfigData.AccessData.TimeoutLogout=Auto Logout [min]
SystemConfigData.SwitchData.ForceBits.CpuWatch=Enable Video Sharing
SystemConfigData.SwitchData.ForceBits.CpuConnect=Force Connect
SystemConfigData.SwitchData.ForceBits.ConDisconnect=Force Disconnect
SystemConfigData.SwitchData.TimeoutDisconnect=CPU Timeout [min]
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=Keyboard Mouse Connect
SystemConfigData.SwitchData.TimeoutShare=Release Time [sec]
SystemConfigData.SwitchData.ForceBits.FKeySingle=Macro Single Step
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.Address=IP Address
SystemConfigData.NetworkData.Netmask=Subnet Mask
SystemConfigData.NetworkData.Gateway=Gateway
SystemConfigData.NetworkData.MacAddress=MAC Address
SystemData.InternalLogLevel=Log Level
SystemData.InternalLogLevel.Debug=Debug
SystemData.InternalLogLevel.Info=Info
SystemData.InternalLogLevel.Notice=Notice
SystemData.InternalLogLevel.Warning=Warning
SystemData.InternalLogLevel.Error=Error
SystemConfigData.SyslogData.Port=Port
SystemConfigData.SyslogData.SysLevel=Log Level
SystemConfigData.SyslogData.SysLevel.Debug=Debug
SystemConfigData.SyslogData.SysLevel.Info=Info
SystemConfigData.SyslogData.SysLevel.Notice=Notice
SystemConfigData.SyslogData.SysLevel.Warning=Warning
SystemConfigData.SyslogData.SysLevel.Error=Error
SystemConfigData.NetworkData.NetworkBits.Syslog=Syslog
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP Agent
SystemConfigData.SnmpData.SnmpBits.Trap=Enable Traps
SystemConfigData.SnmpData.Port=Port
SystemConfigData.SnmpData.SnmpBits.Status=Status
SystemConfigData.SnmpData.SnmpBits.Temperature=Temperature
SystemConfigData.SnmpData.SnmpBits.InsertBoard=Insert I/O Board
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=Remove I/O Board
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=Invalid I/O Board
SystemConfigData.SnmpData.SnmpBits.InsertExtender=Insert Extender
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=Remove Extender
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=Switch Command
SystemConfigData.SnmpData.SnmpBits.FanTray1=Fan Tray 1
SystemConfigData.SnmpData.SnmpBits.FanTray2=Fan Tray 2
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=Power Supply 1
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=Power Supply 2
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=Power Supply 3
SystemConfigData.LdapData.Address=LDAP Server
SystemConfigData.LdapData.Port=Port
SystemConfigData.LdapData.BaseDN=Base DN
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.SntpData.TimeZone=Time Zone
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.MatrixGridData.ForceBits.Grid=Matrix Grid Enabled
ControlGroupData.Arrangement=Arrangement
ControlGroupData.Manual=Manual
ExtenderData.Port=Port
ExtenderData.RDPort=Redundant Port
ExtenderData.ID=ID
ExtenderData.Name=Name
CpuData.ID=ID
CpuData.Name=Name
CpuData.Status.Virtual=Virtual Device
CpuData.Status.AllowPrivate=Allow Private
CpuData.Status.ForcePrivate=Force Private
ConsoleData.ID=ID
ConsoleData.Name=Name
ConsoleData.ConsoleVirtual=CON Assigned
ConsoleData.Status.AllowLogin=Allow User ACL
ConsoleData.Status.ForceLogin=Force Login
ConsoleData.Status.LOSFrame=LOS Frame
ConsoleData.Status.ForceScan=Force CPU Scan
ConsoleData.ScanTime=Scan Time [sec]
ConsoleData.Status.ForceMacro=Show Macro List
ConsoleData.Status.OsdDisabled=OSD Disabled
UserData.Name=Name
UserData.FullName=Full Name
UserData.Rights.ADMIN=Administrator
UserData.Rights.SUPER=Super User
UserData.Rights.POWER=Power User
UserData.Rights.LDAP=LDAP User
UserData.Rights.FTP=FTP
