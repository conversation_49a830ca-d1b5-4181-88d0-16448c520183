<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="VBox" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
  stylesheets="@hotkeyconfiguration_view.css">
  <children>
    <TabPane fx:id="tabPane" prefHeight="700.0" prefWidth="1100.0" tabClosingPolicy="UNAVAILABLE"
      VBox.vgrow="ALWAYS">
      <VBox.margin>
        <Insets/>
      </VBox.margin>
    </TabPane>
  </children>
</fx:root>
