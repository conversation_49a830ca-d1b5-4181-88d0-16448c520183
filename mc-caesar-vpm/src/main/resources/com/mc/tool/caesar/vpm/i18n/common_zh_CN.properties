#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)
systemedit.switching.ip=PDU IP
CaesarOfficePage.title=场景
alert.can_not_save_scenario=现在不能保存预案！
alert.device_busy=设备正忙，请稍后再试。
alert.reboot_alert.text=请重新连接主机设备，IP地址已更改为：
alert.reboot_alert.title=重启
alert.task.message=正在重新连接...
caesar_menu.configuration=配置
caesar_menu.download=下载配置
caesar_menu.refresh=刷新
caesar_menu.save=保存在线配置
caesar_menu.upload=上传配置
con_hotkey.apply=应用
con_hotkey.cancel=取消
con_hotkey.con_hotkey_management=轮询热键设置
con_hotkey.con_list=管控端列表
con_hotkey.copy_btn=复制当前轮询热键列表到其他管控端
con_hotkey.table.id=ID
con_hotkey.table.name=名称
con_right.con_cpu_right_management=管控端权限管理
con_right.con_list=管控端列表
connect_device.connecting=正在连接{0}
connect_device.error.message=无法连接到{0}！
connect_device.error.title=连接失败
connect_device.load_error=加载失败！
connect_device.loading=正在加载{0}...
connect_device.wrong_username_password=密码账号错误或者没有登录权限!
copy_cpu_right_dialog.con_cpu_right.source_header.title=可用管控端列表
copy_cpu_right_dialog.con_cpu_right.target_header.title=复制配置管控端列表
copy_cpu_right_dialog.con_cpu_right.title=复制权限配置到其他管控端
copy_cpu_right_dialog.user_cpu_right.source_header.title=可用用户组列表
copy_cpu_right_dialog.user_cpu_right.target_header.title=复制配置用户列表
copy_cpu_right_dialog.user_cpu_right.title=复制权限配置到其他用户
copy_hotkey_dialog.con_hotkey.source_header.title=可用管控端列表
copy_hotkey_dialog.con_hotkey.target_header.title=复制配置管控端列表
copy_hotkey_dialog.con_hotkey.title=复制当前轮询热键列表到其他管控端
copy_macro_dialog.con_source_header=可用管控端列表
copy_macro_dialog.con_target_header=要复制配置的管控端列表
copy_macro_dialog.title=复制宏配置
copy_macro_dialog.user_source_header=可用用户列表
copy_macro_dialog.user_target_header=要复制配置的用户列表
cpu_right.apply=应用
cpu_right.cancel=取消
cpu_right.full_access=完全访问
cpu_right.menu.full=分配操作模式权限
cpu_right.menu.no=分配无权限模式
cpu_right.menu.video=分配视频权限模式
cpu_right.name=名称
cpu_right.no_access=无权限模式
cpu_right.tip=使用按键F（完全访问）、V（视频模式）和N（无权限模式）进行权限管理
cpu_right.user_cpu_right_management=用户权限管理
cpu_right.user_list=用户列表
cpu_right.video_access=视频访问
cross_screen.cancel=取消
cross_screen.column=列：
cross_screen.commit=提交
cross_screen.commit.warn_content=提交后将无法修改管控端的位置与控制管控端，是否继续提交？
cross_screen.commit.warn_title=提示
cross_screen.control=控制
cross_screen.display=红框提示(s)
cross_screen.mode=跨屏模式
cross_screen.mode.auto=自动跨屏
cross_screen.mode.manual=手动跨屏
cross_screen.name=名称
cross_screen.row=行：
cross_screen.rx_list=跨屏管控端
cross_screen.rx_list.index=序号
cross_screen.rx_list.name=名称
cross_screen.setting=设置
cross_screen.source_list_title=信号源列表
export.config=设备配置
export.graph=系统图
export.report=报告
export.status=系统状态
export.status_and_log=系统状态+日志
export.status.fail=保存失败!
export.status.ok=保存成功!
export_report.empty_MessageNotify=选择为空!
export_report.empty_file_path=文件路径为空！
export_report.file_path=文件路径：
export_report.filechoose_title=保存报告
export_report.page1_title=选择内容
export_report.save_success=保存报告成功！
export_report.wizard_title=配置报告
hostconfiguration.activate_tab=激活配置
hostconfiguration.general_apply_btn=应用
hostconfiguration.general_cancel_btn=取消
hostconfiguration.general_tab=通用配置
hostconfiguration.grid_tab=级联配置
hostconfiguration.network_apply_btn=应用
hostconfiguration.network_cancel_btn=取消
hostconfiguration.network_tab=网络配置
hostconfiguration.redundancy_apply_btn=应用
hostconfiguration.redundancy_cancel_btn=取消
hostconfiguration.redundancy_tab=双机冗余
hostconfiguration.snmp_apply_btn=应用
hostconfiguration.snmp_cancel_btn=取消
hostconfiguration.snmp_tab=SNMP配置
hostconfiguration.syslog_tab=系统日志
hotkey_selection_view.source_header=可用接入端轮询列表
hotkey_selection_view.target_header=轮询热键列表
macro_key.apply=应用
macro_key.cancel=取消
macro_key.con_access_block_title=管控端宏列表配置
macro_key.con_source_list_title=管控端列表
macro_key.copy_macro_list=复制宏列表
macro_key.copy_to_other_rx=复制宏配置至其他管控端
macro_key.copy_to_other_user=复制宏配置至其他用户
macro_key.current_con_device=当前管控端
macro_key.current_user=当前用户
macro_key.delete_macro_list=删除宏列表
macro_key.delete_macro_list.delete_all=删除所有
macro_key.delete_macro_list.delete_confirm_content=需要删除已选择的按键还是所有按键？
macro_key.delete_macro_list.delete_confirm_title=删除宏
macro_key.delete_macro_list.delete_current=删除已选
macro_key.disconnected_cpu=断开接入端
macro_key.function=功能
macro_key.index=序号
macro_key.logout=退出
macro_key.macro=宏
macro_key.name=名称
macro_key.paste_macro_list=粘贴宏列表
macro_key.user_access_block_title=用户宏列表配置
macro_key.user_source_list_title=用户列表
matrixgrid.bind.alert.content.text=连接的端口5656已被使用
matrixgrid.bind.alert.title.text=提示
matrixgrid.connect=连接
matrixgrid.matrix=主机
matrixgrid.wizard.hostconfig.delete=删除
matrixgrid.wizard.hostconfig.duplicate=重复IP地址
matrixgrid.wizard.hostconfig.matrix=主机
matrixgrid.wizard.hostconfig.name_dulplicate=名称重复
matrixgrid.wizard.hostconfig.name_empty=名称为空
matrixgrid.wizard.hostconfig.name_format_error=名称有误
matrixgrid.wizard.hostconfig.name_too_long=名称太长
matrixgrid.wizard.hostconfig.port_error=端口数错误
matrixgrid.wizard.hostconfig.unaccessable=无法连接
matrixgrid.wizard.hostconfig.uncomplete=数据不完全
matrixgrid.wizard.hostconfig.valid=有效
matrixgrid.wizard.hostconfig.validate=校验
matrixgrid.wizard.indicator.activate=激活主机配置
matrixgrid.wizard.indicator.gridname=级联名称
matrixgrid.wizard.indicator.gridsystem=级联系统
matrixgrid.wizard.indicator.host=主机配置
matrixgrid.wizard.indicator.idprocess=ID处理
matrixgrid.wizard.indicator.prepare=前期准备
offline_manager.delete_btn=删除
offline_manager.device=设备
offline_manager.empty_alert.text=请选择需要删除的项！
offline_manager.empty_alert.title=提示
offline_manager.ext_name=外设
offline_manager.name=设备
offline_manager.title=离线设备管理
page.con_cpu_right=管控端权限
page.con_hotkey_configuration=管控端轮询热键配置
page.con_macro_view=管控端宏配置
page.refreshing=正在刷新...
page.switch=切换
page.switch_to_operation=是否切换到系统操作页面？
page.user_cpu_right=用户权限
page.user_group_cpu_right=用户组权限
page.user_macro_view=用户宏配置
report.assignment=分配
report.con_acl=访问控制
report.con_devices=CON管控端
report.con_favorites=收藏夹
report.con_macros=宏
report.cpu_devices=CPU接入端
report.ext_units=扩展单元
report.matrix_view=主机视图
report.select_all=选择全部
report.system=系统
report.user=用户
report.user_acl=访问控制
report.user_favorites=收藏夹
report.user_macros=宏
save_config.alert.download_complete=下载完成
save_config.alert.download_failed=下载失败
save_config.alert.title=信息
save_config.alert.upload_complete=上传完成
save_config.alert.upload_failed=上传失败
save_config.download_title=下载
save_config.page1.tips1=步骤
save_config.page1.tips2=1.连接
save_config.page1.tips3=2.选择配置文件
save_config.page1.tips4=2.选择配置槽位
save_config.page2.address=主机名/IP地址
save_config.page2.errorMsg.text1=用户没有管理员权限,不允许上传。
save_config.page2.errorMsg.text2=用户不存在
save_config.page2.errorMsg.text3=连接失败
save_config.page2.errorMsg.text4=主机忙
save_config.page2.errorMsg.text5=用户名或密码错误
save_config.page2.password=密码
save_config.page2.user=用户
save_config.page2.verify=验证
save_config.page3.activate_alert.text=激活配置文件，主机必须重启，你真的确认激活配置吗？
save_config.page3.activate_alert.title=激活配置
save_config.page3.activate_checkbox=上传后激活配置(主机将会重启！)
save_config.page3.addressCol=IP地址
save_config.page3.download=下载
save_config.page3.downloadBtn=下载
save_config.page3.fileCol=文件
save_config.page3.infoCol=信息
save_config.page3.nameCol=名称
save_config.page3.title=选择配置文件
save_config.page3.uploadBtn=上传
save_config.page3.versionCol=版本
save_config.upload_title=上传
searchForDevices.title=查找设备
toolbar.meeting_room=会议室布局
toolbar.save_scenario=保存预案
user_group.name=名称
user_group_right.delete_button=删除用户组
user_group_right.edit_button=编辑用户组
user_group_right.new_button=新建用户组
user_group_right.user_group_cpu_right_management=用户组权限管理
user_group_right.user_group_list=用户组列表
videowall.check.vp6_screen_layer_limit=一个屏幕内的图层数必须不超过{0}!
videowall.check.vp6_layer_limit=一个VP6内的图层数必须不超过{0}!
videowall.check.vp6_window_limit=2K窗口数最多{0}个，4K/DHDMI窗口数最多{1}个!
videowall.check.vp7_screen_layer_limit={1}接口模式下，一个屏幕内的图层数必须不超过{0}!
videowall.check.vp7_window_limit=2K/DHDMI/4K30信号最多{0}个，4K60信号最多{1}个!
videowall.check.vp7_invalid_screen=当前接口模式为{0},此屏幕无效!
videowall.mode=模式调用方式
videowall.mode.auto=自动发布
videowall.mode.manual=手动发布
videowall.output=输出
videowall.output.clock=时钟
videowall.output.enable=使能
videowall.output.horz_active_video_time=水平分辨率
videowall.output.horz_back_porch=水平后沿
videowall.output.horz_front_porch=水平前沿
videowall.output.horz_polarity=水平极性
videowall.output.horz_sync=水平同步
videowall.output.vert_active_video_time=垂直分辨率
videowall.output.vert_back_porch=垂直后沿
videowall.output.vert_front_porch=垂直前沿
videowall.output.vert_polarity=垂直极性
videowall.output.vert_sync=垂直同步
videowall.scenario.limit_error=最多只能保存{0}个预案！
wizard_pane.next_btn=下一步
wizard_pane.previous_btn=上一步
export.status.saving=正在保存状态
systemedit.master=主
systemedit.slave=从
systemedit.gridline=级联线
matrixgrid.wizard.hostconfig.port_too_much=端口总数过多
multiscreen.row=行
multiscreen.column=列
multiscreen.layout=RX布局
toolbar.save_as_scenario=另存为预案
multiscreen.connection.full=完全连接
multiscreen.connection.video=视频连接
multiscreen.rx_list=组合管控端
multiscreen.video_conn=视频连接
multiscreen.full_conn=完全连接
systemedit.list.index=序号
systemedit.list.id=ID
systemedit.list.device=设备 
systemedit.list.port=端口
systemedit.list.type=类型
systemedit.list.serial=序列号
systemedit.exporting=正在导出外设信息
systemedit.exportsuccess=导出成功
systemedit.exporterror=导出失败
systemedit.importing=正在导入外设信息
systemedit.importsuccess=导入成功
systemedit.importerror=导入失败
systemedit.importerror.formaterror=部分行的格式有误
systemedit.importerror.typeerror=部分项不存在或者类型不匹配
systemedit.importerror.nameerror=部分项的名称长度不符合规范
systemedit.importerror.iderror=部分项的ID范围错误或者重复
systemedit.importerror.unknownerror=未知错误
systemedit.title.success=成功
systemedit.title.fail=失败
systemedit.switch=切换到主机
systemedit.topological=级联拓扑图
videowall.check.vpcon_4k_window_limit=4K窗口数必须不超过{0}!
videowall.check.vpcon_2k_window_limit=2K窗口数必须不超过{0}!
videowall.not_created=未创建
videowall.bgimg.uploadpath=背景图路径：
videowall.bgimg.selectbtn=选择
videowall.bgimg.upload=上传
videowall.bgimg.cancel=取消
videowall.bgimg.title=上传背景图
cross_screen.fail_to_create=由于没有足够的空间，无法创建跨屏组！
connect_device.error_version=不支持主机版本{0}，请升级到新版本的VPM软件。
check.try=正在初始化数据，请稍后刷新。
user.videowallright.selected=已授权视频墙
user.videowallright.unselected=可选视频墙
cross_screen.mode.irregular=异形跨屏
systemedit.switching.index=序号
systemedit.switching.id=ID
systemedit.switching.device=设备
systemedit.switching.port=端口
systemedit.switching.mode=开关机模式
systemedit.switching.outlet=电源插座
systemedit.switching.operation=操作
systemedit.switching.poweron=开机
systemedit.switching.poweroff=关机
systemedit.switching.reboot=重启
systemedit.switching.poweron.alert=是否对关机PC进行远程开机？
systemedit.switching.poweroff.alert=是否强制关机？
systemedit.switching.reboot.alert=是否强制重启？
systemedit.switching.networkwake=网卡唤醒
systemedit.switching.pcpowercontrol=控制PC电源
systemedit.switching.disable=禁用
systemedit.switching.editPduIpDialog.title=设置PDU IP
systemedit.speed.index=序号
systemedit.speed.name=名称
systemedit.speed.speed=速度
videowall.logiclayout.enable=使能
videowall.logiclayout.row=逻辑屏幕行数
videowall.logiclayout.column=逻辑屏幕列数
systemedit.page.selector.graph=拓扑
systemedit.page.selector.list=列表
systemedit.page.selector.switch=开关机
systemedit.page.selector.speed=速率设置
systemedit.exporting.optModule=正在导出光模块信息
txGroupsManager.title=无效TXRX分组管理
txGroupsManager.selectAll=全选
page.saving=正在保存...
videowall.check.vpcon_offline=VP6无法通讯!
hostconfiguration.event_tab=事件配置
hostconfiguration.event_apply_btn=应用
hostconfiguration.event_cancel_btn=取消
videowall.check.screen_duplicate=大屏存在重复屏幕
multiscreen.connection.disconnect=断开连接
cross_screen.mode.linkage=四画面联动模式
enable=使能
systemedit.page.selector.offline=离线设备
name=名称
offline_manager.delete_selected=删除选中项
offline_manager.delete_checked=删除勾选项
offline_manager.delete_checked.alert=确定删除勾选的项吗？
check.matrix_version=主控FPGA版本不匹配，请升级。
videowall.audio_group.dialog.title=请选择音频组
menu_disconnect=断开

