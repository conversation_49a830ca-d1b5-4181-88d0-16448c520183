<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TreeTableColumn?>
<?import javafx.scene.control.TreeTableView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<fx:root stylesheets="@extenderupdate_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <HBox alignment="CENTER_RIGHT" minHeight="40" prefHeight="40">
    <Button id="delTxgroup-btn" onAction="#onShowTxgroupManager" styleClass="image-button"
      text="%ExtenderUpdate.txgroup">
      <HBox.margin>
        <Insets right="10.0"/>
      </HBox.margin>
    </Button>
    <Button id="offline-btn" onAction="#onShowOfflineManager" styleClass="image-button"
      text="%ExtenderUpdate.offline">
      <HBox.margin>
        <Insets right="10.0"/>
      </HBox.margin>
    </Button>
  </HBox>
  <Region minHeight="1" styleClass="seperator"/>
  <VBox id="main-container" VBox.Vgrow="ALWAYS">
    <TreeTableView fx:id="tableView" VBox.Vgrow="ALWAYS" xmlns:fx="http://javafx.com/fxml">
      <columns>
        <TreeTableColumn fx:id="nameCol" prefWidth="150" text="%ExtenderUpdate.nameCol.text"/>
        <TreeTableColumn fx:id="deviceCol" prefWidth="75.0" text="%ExtenderUpdate.deviceCol.text"/>
        <TreeTableColumn fx:id="portCol" prefWidth="40" text="%ExtenderUpdate.portCol.text"/>
        <TreeTableColumn fx:id="typeCol" prefWidth="80" text="%ExtenderUpdate.typeCol.text"/>
        <TreeTableColumn fx:id="serialCol" prefWidth="80.0" text="%ExtenderUpdate.serialCol.text"/>
        <TreeTableColumn fx:id="hardwareVerCol" prefWidth="50.0"
          text="%ExtenderUpdate.hardwareVerCol.text"/>
        <TreeTableColumn fx:id="currentVersionCol" prefWidth="81.0"
          text="%ExtenderUpdate.currentVersionCol.text"/>
        <TreeTableColumn fx:id="currentDateCol" prefWidth="80"
          text="%ExtenderUpdate.currentDateCol.text"/>
        <TreeTableColumn fx:id="updateVersionCol" prefWidth="80"
          text="%ExtenderUpdate.updateVersionCol.text"/>
        <TreeTableColumn fx:id="updateDateCol" prefWidth="80"
          text="%ExtenderUpdate.updateDateCol.text"/>
        <TreeTableColumn fx:id="updateCol" prefWidth="120" text="%ExtenderUpdate.updateDate.text"/>
        <TreeTableColumn fx:id="progressCol" prefWidth="120"
          text="%ExtenderUpdate.progressCol.text"/>
      </columns>
      <columnResizePolicy>
        <TreeTableView fx:constant="CONSTRAINED_RESIZE_POLICY"/>
      </columnResizePolicy>
      <VBox.margin>
        <Insets/>
      </VBox.margin>
    </TreeTableView>
    <HBox id="tool-box">
      <Button fx:id="loadButton" styleClass="common-button" text="%ExtenderUpdate.load_file"/>
      <Label fx:id="updateFilePathLabel" maxWidth="500">
        <font>
          <Font size="17.0"/>
        </font>
      </Label>
      <Region HBox.hgrow="ALWAYS"/>
      <Label text="%ExtenderUpdate.countName"/>
      <Label fx:id="countNumLabel" text="0"/>
      <Button fx:id="selectDiffVerButton" styleClass="common-button"
        text="%ExtenderUpdate.selectDiffVerButton.text"/>
      <Button fx:id="selectAllButton" styleClass="common-button"
        text="%ExtenderUpdate.seleteAllButton.text"/>
      <Button fx:id="updateButton" styleClass="common-button" text="%ExtenderUpdate.update_btn"/>
      <Button fx:id="cancelButton" styleClass="common-button" text="%ExtenderUpdate.cancel_btn"/>
    </HBox>
    <HBox id="update-log-title-box">
      <Label id="update-log-title" text="%update_log"/>
    </HBox>

    <ListView fx:id="loglist" prefHeight="200">
    </ListView>
  </VBox>
</fx:root>
