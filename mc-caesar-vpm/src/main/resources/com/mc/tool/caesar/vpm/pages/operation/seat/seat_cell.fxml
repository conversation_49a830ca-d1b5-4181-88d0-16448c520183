<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.VBox?>
<VBox alignment="CENTER" stylesheets="@seat_cell.css" xmlns="http://javafx.com/javafx/8"
  xmlns:fx="http://javafx.com/fxml/1" fx:id="root" spacing="10" id="root">
  <children>
    <Label id="screen-pic" fx:id="screenPic" prefWidth="210" prefHeight="170"/>
    <Label id="screen-name" fx:id="screenName" alignment="CENTER" prefWidth="100"
      textAlignment="CENTER"/>
  </children>
</VBox>
