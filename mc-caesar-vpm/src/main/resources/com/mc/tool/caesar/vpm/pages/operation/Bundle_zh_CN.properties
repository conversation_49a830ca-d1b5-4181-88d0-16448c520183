Index=索引
Layout.columns=屏幕列数
Layout.enable_multi_res=使能多分辨率
Layout.fps=帧数
Layout.multi_res=多分辨率
Layout.multi_res_config=多分辨率配置
Layout.resHeight=单分辨率高度
Layout.resWidth=单分辨率宽度
Layout.rows=屏幕行数
OperationPage.title=系统操作
Video.alpha=OSD透明度
Video.bgColor=背景颜色
test_frame.custom_color=自定义颜色
test_frame.speed=速度
test_frame.alpha=透明度
test_frame.mode=模式
audio.output=音频输出
Video.height=OSD高度
Video.left=OSD位置[横坐标]
Video.osdColor=OSD颜色
Video.show_logo=显示LOGO
Video.top=OSD位置[纵坐标]
Video.width=OSD宽度
rearrangeDialog.arrange.arrangeButton.checkText=错误输入(请输入数字，不大于选中的RX数量，且保证大于-1的数唯一)
rearrangeDialog.arrange.arrangeButton.text=自动重新排列
rearrangeDialog.arrange.msg=请检查显示器显示数字的顺序是否与下表一致，-1表示没有显示，如果不一致，修改下表的内容，使之与与显示器保持一致，然后点击【自动重新排列】按钮排列屏幕
rearrangeDialog.guide.headerText=步骤
rearrangeDialog.guide.label1=1. 选择需要使用的单元
rearrangeDialog.guide.label2=2. 排列显示单元
rearrangeDialog.selector.label=向导(选择需要使用的单元)
videoLayout.cpu=TX
videoLayout.height=高度
videoLayout.name=名称
videoLayout.transparence=透明度
videoLayout.width=宽度
videoLayout.xpos=横坐标
videoLayout.ypos=纵坐标
signalCut.title=信号裁剪
signalCut.noFreeClipData=没有空闲的裁剪数据
signalCut.add=添加裁剪
signalCut.edit=编辑
signalCut.delete=删除
Video.enableBgImg=显示背景图
Video.bgimgwidth=背景图宽度
Video.bgimgheight=背景图高度
Video.disableSyncData=关闭数据同步
Video.enableRedundancy=使能冗余
Layout.compensationScaleThreshold=拼缝补偿放大阈值
Layout.leftCompensation=左侧拼缝补偿像素
Layout.rightCompensation=右侧拼缝补偿像素
Layout.topCompensation=顶部拼缝补偿像素
Layout.bottomCompensation=底部拼缝补偿像素
