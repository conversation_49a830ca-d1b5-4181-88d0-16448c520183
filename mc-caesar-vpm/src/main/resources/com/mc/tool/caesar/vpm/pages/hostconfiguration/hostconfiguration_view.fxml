<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" stylesheets="@hostconfiguration_view.css"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <TabPane prefHeight="500.0" prefWidth="1100.0" tabClosingPolicy="UNAVAILABLE"
      VBox.vgrow="ALWAYS" fx:id="tabPane">
      <tabs>
        <Tab text="%hostconfiguration.general_tab">
          <content>
            <VBox id="main-container">
              <children>
                <ScrollPane fx:id="generalPane" VBox.vgrow="ALWAYS">
                  <VBox.margin>
                    <Insets/>
                  </VBox.margin>
                </ScrollPane>
                <HBox id="tool-box">
                  <children>
                    <Button fx:id="generalCommitButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.general_apply_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                    <Button fx:id="generalReloadButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.general_cancel_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                  </children>
                  <VBox.margin>
                    <Insets/>
                  </VBox.margin>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
        <Tab text="%hostconfiguration.network_tab">
          <content>
            <VBox id="main-container" prefHeight="200.0" prefWidth="100.0">
              <children>
                <ScrollPane fx:id="networkPane" VBox.vgrow="ALWAYS">
                  <VBox.margin>
                    <Insets/>
                  </VBox.margin>
                </ScrollPane>
                <HBox id="tool-box">
                  <children>
                    <Button fx:id="networkCommitButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.network_apply_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                    <Button fx:id="networkReloadButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.network_cancel_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                  </children>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
        <Tab text="%hostconfiguration.snmp_tab">
          <content>
            <VBox id="main-container" prefHeight="200.0" prefWidth="100.0">
              <children>
                <ScrollPane fx:id="snmpPane" VBox.vgrow="ALWAYS">
                  <VBox.margin>
                    <Insets/>
                  </VBox.margin>
                </ScrollPane>
                <HBox id="tool-box">
                  <children>
                    <Button fx:id="snmpCommitButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.snmp_apply_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                    <Button fx:id="snmpReloadButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.snmp_cancel_btn">
                      <HBox.margin>
                        <Insets/>
                      </HBox.margin>
                    </Button>
                  </children>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
        <Tab text="%hostconfiguration.redundancy_tab">
          <content>
            <VBox id="main-container" prefHeight="200.0" prefWidth="100.0">
              <children>
                <ScrollPane fx:id="redundancyPane" VBox.vgrow="ALWAYS"/>
                <HBox id="tool-box">
                  <children>
                    <Button fx:id="redundancyCommitButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.redundancy_apply_btn"/>
                    <Button fx:id="redundancyReloadButton" mnemonicParsing="false"
                      styleClass="common-button" text="%hostconfiguration.redundancy_cancel_btn"/>
                  </children>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
        <Tab text="%hostconfiguration.event_tab">
          <content>
            <VBox id="main-container" prefHeight="200.0" prefWidth="100.0">
              <children>
                <ScrollPane fx:id="eventPane" VBox.vgrow="ALWAYS"/>
                <HBox id="tool-box">
                  <children>
                    <Button fx:id="eventCommitButton" mnemonicParsing="false"
                            styleClass="common-button" text="%hostconfiguration.event_apply_btn"/>
                    <Button fx:id="eventReloadButton" mnemonicParsing="false"
                            styleClass="common-button" text="%hostconfiguration.event_cancel_btn"/>
                  </children>
                </HBox>
              </children>
            </VBox>
          </content>
        </Tab>
        <Tab fx:id="activateConfigTap" text="%hostconfiguration.activate_tab"/>
      </tabs>
      <VBox.margin>
        <Insets/>
      </VBox.margin>
    </TabPane>
  </children>
</fx:root>
