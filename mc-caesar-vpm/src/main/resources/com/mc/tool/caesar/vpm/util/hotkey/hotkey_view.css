
#main-container {
  -fx-padding: 10px;
  -fx-spacing: 10px;
}

#main-panel {
  -fx-spacing: 5px;
}

#hotkey-table-box {
  -fx-padding: 5px;
  -fx-spacing: 5px;
}

.title-box {
  -fx-min-height: 30px;
  -fx-max-height: 30px;
  -fx-background-color: #f2f2f2;
  -fx-alignment: center;
}

.border-panel {
  -fx-border-width: 1px;
  -fx-border-color: #cccccc;
}

#tool-box {
  -fx-alignment: center_right;
  -fx-spacing: 10px;
}

#upButton {
  -fx-graphic: url("upBtn_normal.png");
}

#upButton:hover {
  -fx-graphic: url("upBtn_hover.png");
}

#upButton:disabled {
  -fx-graphic: url("upBtn_disabled.png");
}

#downButton {
  -fx-graphic: url("downBtn_normal.png");
}

#downButton:hover {
  -fx-graphic: url("downBtn_hover.png");
}

#downButton:disabled {
  -fx-graphic: url("downBtn_disabled.png");
}