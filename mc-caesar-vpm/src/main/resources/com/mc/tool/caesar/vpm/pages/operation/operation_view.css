#root {
  -fx-background-color: #ffffff;
}

#show-btn-container {
  -fx-border-width: 0 0 0 1;
  -fx-border-color: #cccccc;
}

#show-btn-sub-container {
  -fx-background-color: #e6e6e6;
}

.image-button {
  -fx-background-radius: 0;
  -fx-border-style: null;
  -fx-background-color: null;
  -fx-background-repeat: no-repeat;
}

#show-property-btn {
  -fx-graphic: url("show_property.png");
}

.seperator {
  -fx-background-color: #cccccc;
}

#icon-btn {
  -fx-graphic: url("icon_btn.png");
}

#property-btn {
  -fx-graphic: url("property_btn.png");
}

.right-panel {
  -fx-padding: 0 0 0 0;
}

.right-title {
  -fx-background-color: #e6e6e6;
  -fx-alignment: center-left;
  -fx-padding: 0 0 0 5;
}

#icon-grid-pane {
  -fx-grid-lines-visible: true;
}

#icon-grid-pane Label {
  -fx-label-padding: 0 0 0 10;
  -fx-min-height: 28;
}

#hide-btn-container {
  -fx-padding: 0;
}

#hide-property-btn {
  -fx-graphic: url("hide_property.png");
}

#hide-property-btn {
  -fx-graphic: url("hide_property.png");
}

#header-toolbox {
  -fx-spacing: 30;
  -fx-padding: 0 10 0 0;
}

#function-title {
  -fx-label-padding: 0 0 0 9;
}

#seperator {
  -fx-background-color: #cccccc;
}

#body-pane {
  -fx-padding: 0 0 10 10;
}

#source-list-title-container {
  -fx-background-color: #cccccc;
}

#source-list-title {
  -fx-label-padding: 0 0 0 10;
}

#menu-btn {
  -fx-graphic: url("menu_normal.png");
  -fx-text-fill: #333333;
}

#menu-btn:hover {
  -fx-graphic: url("menu_hover.png");
  -fx-text-fill: #f08519;
}

#config-btn {
  -fx-graphic: url("config_normal.png");
  -fx-text-fill: #333333;
}

#config-btn:hover {
  -fx-graphic: url("config_hover.png");
  -fx-text-fill: #f08519;
}

#switch-btn {
  -fx-graphic: url("switch_normal.png");
  -fx-text-fill: #333333;
}

#switch-btn:hover {
  -fx-graphic: url("switch_hover.png");
  -fx-text-fill: #f08519;
}

#screen-btn {
  -fx-graphic: url("display_mode.png");
  -fx-text-fill: #333333;
}

#screen-btn:hover {
  -fx-graphic: url("display_mode_hover.png");
  -fx-text-fill: #f08519;
}

#function-source {
  -fx-border-size: 1px;
  -fx-border-color: #cccccc;
}

#function-content {
  -fx-border-width: 1 1 1 0;
  -fx-border-color: #cccccc;
}


#left-arrow {
  -fx-background-image: url("left_arrow_normal.png");
}

#left-arrow:hover {
  -fx-background-image: url("left_arrow_hover.png");
}

#left-arrow:disable {
  -fx-background-image: url("left_arrow_disable.png");
}

#right-arrow {
  -fx-background-image: url("right_arrow_normal.png");
}

#right-arrow:hover {
  -fx-background-image: url("right_arrow_hover.png");
}

#right-arrow:disable {
  -fx-background-image: url("right_arrow_disable.png");
}


.grid-view {
  -fx-cell-width: 71;
  -fx-cell-height: 118;
  -fx-horizontal_alignment: center;
}


.list-cell {
  -fx-cell-size: 32;
  -fx-background-color: white;
}

.list-cell:selected {
  -fx-background-color: #e6e6e6;
  -fx-text-fill: black;
}