<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Tab?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.layout.VBox?>
<fx:root stylesheets="@offlinemanager_view.css" type="javafx.scene.layout.VBox"
  xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
  <TabPane fx:id="tabPane">
    <tabs>
      <Tab id="EXTENDER" closable="false" text="%offline_manager.ext">
        <TableView fx:id="extenderTableView">
        </TableView>
      </Tab>
      <Tab id="CPU" closable="false" text="%offline_manager.cpu">
        <TableView fx:id="cpuTableView">
          <columns>
            <TableColumn fx:id="cpuExtenderTableColumn" text="%offline_manager.cpu.ext"/>
          </columns>
        </TableView>
      </Tab>
      <Tab id="CON" closable="false" text="%offline_manager.con">
        <TableView fx:id="conTableView">
          <columns>
            <TableColumn fx:id="conExtenderTableColumn" text="%offline_manager.con.ext"/>
          </columns>
        </TableView>
      </Tab>
    </tabs>
  </TabPane>
</fx:root>
