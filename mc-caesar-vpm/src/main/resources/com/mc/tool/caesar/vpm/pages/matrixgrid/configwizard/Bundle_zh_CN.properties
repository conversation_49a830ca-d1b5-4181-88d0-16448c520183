activate.info1=1. 选择配置槽位，保存修改过后的配置.
activate.info2=2. 按下"激活主机级联”按钮，所有级联主机将会重启，这个过程需要花费几分钟的时间
activate.info3=3. 按下“完成”按钮去关闭配置向导，主机级联即可使用"
autoid.info1=执行以下步骤：
autoid.info2=可选合并和不合并ID，打钩级联主机，即可保留用于合并过程的外设ID。
autoid.info3=注意：主机手动合并多台级联主机ID后必须更改重复的ID，否则导致系统异常
autoid.table.autoid=保持外设ID
autoid.table.category=类别
autoid.table.ip=IP地址
autoid.table.name=设备
gridname.gridname=级联名称
gridname.info1=级联名称是级联网络所有主机的统一名称
gridname.info2=执行以下步骤：
gridname.info3=1.输入级联名称
gridsystem.info1=执行以下步骤：
gridsystem.info2=1.每台级联主机中键入一个独特的主机名称
gridsystem.info3=2.检查每台主机的IP地址和端口
gridsystem.info4=3.按下"验证"按钮，验证主机数据
gridsystem.table.category=类别
gridsystem.table.ip=IP地址
gridsystem.table.name=主机名称
gridsystem.table.port=端口
gridsystem.table.valid=状态
gridsystem.table.validate=校验
hostconfig.add-matrix=添加新主机到级联
hostconfig.info1_1=1. 添加新主机到级联
hostconfig.info1_2=2. 填写每个主机的内容（包括IP地址，用户名，密码）
hostconfig.info1_3=3. 按下验证按钮去检查网络是否可用
hostconfig.info2_1=1. 按下删除按钮从级联中移除主机并且照常进行
hostconfig.table.category=类别
hostconfig.table.delete=删除
hostconfig.table.ip=IP地址
hostconfig.table.pwd=密码
hostconfig.table.user=用户名
hostconfig.table.valid=状态
hostconfig.table.validate=校验
hostconfig.title1=\ · 从级联中添加主机
hostconfig.title2=\ · 从级联中移除主机
prepare.check1=1. 所有主机必须安装v3.0的固件版本或更高
prepare.check2=2. 所有主机运行在相同的TCP/IP网络内
prepare.check3=3. 对所有主机下载配置
prepare.info1=配置向导将会指导配置或修改主机级联，
prepare.info2=主机级联配置完成后方可接入级联线
prepare.info3=确保满足以下需求：
activate.table.file=文件
activate.table.info=信息
activate.table.ip=IP地址
activate.table.name=名称
activate.table.version=版本
activate.log=日志信息
activate.activate=激活级联主机
activate.savelog=保存日志
