ActionType.reboot=Reboot
ActionType.reset=Factory Reset
AllowLogin=AllowLogin
AllowScan=AllowTXscan
ConnectType.full=Full
ConnectType.private=Private
ConnectType.share_full=Share(Full)
ConnectType.share_video=Share(Video)
ConnectType.video=Video
ConnectionFilter.full=Full Connection
ConnectionFilter.private=Private Connection
ConnectionFilter.video=Video Connection
ForceLogin=ForceLogin
ForceScan=ForceTXscan
Goto_btn.macro=MacroControl
Goto_btn.right=RightsManagement
Goto_btn.scan=ScanConfiguration
InterfaceStatus=InterfaceStatus
MenuDisconnect=Disconnect
SystemEditPage.title=SystemEdit
alert.reboot.text=Are you sure to reboot?
alert.reboot.title=Reboot
alert.reboot_alert.text=Please reconnect to the host device. The IP address has been changed to:
alert.reboot_alert.title=Reboot
alert.reset.text=Are you sure to reply factory Settings?
alert.reset.title=Factory Reset
allowPrivate=AllowPrivateConnect
conName=RX
cpuName=TX
extenderName=Extender
extenderPort=Port
extender_status.activate_extender_binding=Activate Extender Binding
extender_status.dvimode=DVI Mode
extender_status.edid_status=EDID Status
extender_status.edidexist=EDID Exists
extender_status.edidvalid=EDID Valid
extender_status.extender_binding=Extender Binding
extender_status.extender_binding2=Extender Binding
extender_status.has_input=Has input
extender_status.hdmimode=HDMI Mode
extender_status.interface_count=Interface Count
extender_status.interface_type=Interface Type
extender_status.invalid=Invalid
extender_status.keyboardonline=Keyboard Online
extender_status.mouseonline=Mouse Online
extender_status.name=Interface Status
extender_status.no_input=No input
extender_status.offline=Offline
extender_status.online=Online\r\n
extender_status.resolution_type=Resolution Type
extender_status.screenonline=Screen Online
extender_status.udiskonline=UDisk Online
extender_status.usb_type=Device
extender_status.usbcableonline=USB Cable Online
extender_status.valid=Valid
extender_status.video_cable_status=Video Cable Status
extender_status.video_input_status=Video Input Status
extender_status.videoinput=Video Online
forcePrivate=ForcePrivateConnect
host.ipAddress=IP Address
host.macAddress=MAC Address
host.name=HostName
host.netMask=Netmask
host.offline=Offline
host.online=Online
host.status=Status
id=ID
losFrame=LosFrame
matrixType=Matrix Type
menu.videowall.reuse=Create from offline Video Wall
menu.rebind=Binding Setting
menu.usb=USB Port Binding
menu.usb.create_extender_fail=Fail to create new extender!
menu.usb.dialog_title=USB Port Binding
menu.usb.port_used=Port has been used!
mode.full=FullAccess
mode.private=PrivateAccess
mode.shared=SharedAccess
mode.video=VideoAccess
mouseSpeed=Mouse Speed
name=Device
osdTspy=Osd Transparency(0-255)
rxNum=The number of RX
scanTime=ScanTime
serial=Serial
suspendEnable=Suspend Enable
suspendPos=Suspend Pos
suspendTime=Suspend Time
txNum=The number of TX
usbConType=usbDiskEnable
version=Version
virtual=Virtual Out
vp.input=Input {0} ID
vp.output=Output {0} Name
type=Type
gridInterface=Grid Interface
localPort=Local Port
remoteMatrix=Remote Matrix
remotePort=Remote Port
used=Used
unused=Unused
usedStatus=Use Status
vp.status=Status
vp.status.error=Can't communicate.
vp.status.normal=Normal
resolution=Resolution
menu.scenariogroup=Create Scenario Group
analog_audio=Analog Audio
gridmatrix.name=Name
gridmatrix.connection=Connected Matrix 
channel1Port=Channel 1 Port
channel2Port=Channel 2 Port
hdmiSelection=HDMI Selection
hdmiSelection.auto=Auto
hdmiSelection.hdmi1=HDMI1
hdmiSelection.hdmi2=HDMI2
txVideoQp=video QP
txTouchEnable=touchScreenEnable
txTouchEnable.alert.title=Warning
txTouchEnable.alert.content=Applying this option will cause the device to reboot, are you sure?
offset=TX Offset Settings
txOffset.dialog.title=TX Offset Settings
txOffset.dialog.activateSetting=Activate TX Offset Settings
txOffset.dialog.deviceName=Device Name:
txOffset.dialog.totalResolution=Multiscreen Workstation Total Resolution:
txOffset.dialog.resolution1=Resolution-1:
txOffset.dialog.resolution2=Resolution-2:
txOffset.dialog.offsetX=Offset X:
txOffset.dialog.offsetY=Offset Y:
txOffset.activated=Activated
txOffset.unactivated=Unactivated
menu.grouping.tx=Create a TX group
menu.grouping.rx=Create a RX group
menu.grouping.selectable=Selectable
menu.grouping.selected=Selected
menu.grouping.tx.name=TX group name
menu.grouping.rx.name=RX group name
menu.grouping.multiscreen=Create Scenario Group
menu.grouping.multiscreen.name=Scenario group name
menu.grouping.crossscreen=Create CrossScreen Group
menu.grouping.crossscreen.name=CrossScreen group name
menu.grouping.videowall=Create VideoWall
menu.grouping.videowall.name=Videowall name
menu.grouping.same_name_error=The same name is not allowed
menu.grouping.illegal_name_len=Name length is illegal
changeId.alert.title=Alert
changeId.alert.content=Changing the ID will cause the data to be refreshed. Do you want to continue?(Refresh time may be long if the number of Extender is large)
txAudioTrigger=Audio Trigger
txEventEnable=Event Enable
txTriggerHoldTime=Audio trigger hold time
txHighCompressionRatio=High Compression Ratio
multiviewLayout=MultiviewLayout
extender_status.special_ext_sub_type=TypeRemark
multiviewOutputMode=OutputMode
vp.channel=Channel
vp.ip=IP Address
vp.netmask=Netmask
vp.gateway=Gateway
vp.mac=MAC
rxOsdMenu=OSD Menu
peer_tx.name=Peer TX
extIcronEnable=IcronEnable
extUartBaudRate=BaudRate
extDoubleDpEnable=DP Mode
dpModeConfirm.title=Dual DP mode confirmation
dpModeConfirm.content=The device will automatically restart when changing to dual DP mode. Are you sure you want to proceed?
menu.group.audio=CreateAudioGroup
type.audio_group=AudioGroup
menu.audio_delete.alert=The audio group is occupied by the video wall and cannot be deleted.
warning=Warning
