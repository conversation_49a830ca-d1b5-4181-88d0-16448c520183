autoid.info1=Do the following:
autoid.info2=Optionally merge and unmerge IDs, and tick the cascading host to preserve the peripheral IDs used in the merge process.
autoid.info3=Note: The duplicate ID must be changed after the host manually merges multiple cascaded host IDs, otherwise it will cause a system exception
autoid.table.autoid=Keep the Extender ID
autoid.table.category=category
autoid.table.ip=IP Address
autoid.table.name=Device
activate.info1=1. Select the configuration slot and save the modified configuration.
activate.info2=2. Press the "Activate Host Cascade" button and all the cascaded hosts will be restarted. This process will take several minutes
activate.info3=3. Press the "Finish" button to close the configuration wizard and host cascading can be used.
activate.table.file=File
activate.table.name=Name
activate.table.info=Info
activate.table.ip=IP Address
activate.table.version=Version
activate.log=Log Info
activate.savelog=Save Log
activate.activate=Activate cascading hosts
gridname.info1=A cascaded name is the uniform name of all the hosts in a cascaded network
gridname.gridname=cascade Name
gridname.info2=Do the following:
gridname.info3=1. Input cascade name
gridsystem.info1=Do the following:
gridsystem.info2=1. Type a unique host name for each cascaded host
gridsystem.info3=2. Check IP address and port of each host
gridsystem.info4=3. Press the "Verify" button to verify the host data
gridsystem.table.category=Category
gridsystem.table.ip=IP Address
gridsystem.table.name=Host Name
gridsystem.table.port=Port
gridsystem.table.valid=Status
gridsystem.table.validate=verify
hostconfig.add-matrix=Add a new host to the cascade
hostconfig.info1_1=1. Add a new host to the cascade
hostconfig.info1_2=2. Fill in the contents of each host (including IP address, user name, password)
hostconfig.info1_3=3. Press the Verify button to check whether the network is available
hostconfig.info2_1=1. Press the Delete button to remove the host from the cascade and proceed as usual
hostconfig.table.category=Category
hostconfig.table.delete=Delete
hostconfig.table.ip=IP Address
hostconfig.table.pwd=Password
hostconfig.table.user=User name
hostconfig.table.valid=Status
hostconfig.table.validate=Verify
hostconfig.title1=\ · Add hosts from the cascade
hostconfig.title2=\ · Remove hosts from the cascade
prepare.check1=1. All hosts must install firmware version v3.0 or higher
prepare.check2=2. All hosts run on the same TCP/IP network
prepare.check3=3. Download the configuration for all hosts
prepare.info1=The configuration wizard will guide you through configuring or modifying the host cascade,
prepare.info2=The host can access the cascade line after the cascade configuration is completed
prepare.info3=Ensure that the following requirements are met: