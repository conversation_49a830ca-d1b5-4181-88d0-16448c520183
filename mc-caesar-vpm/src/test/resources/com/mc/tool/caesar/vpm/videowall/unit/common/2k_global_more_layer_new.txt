osd:[t(200) l(200) w(80) h(400) c(a4ff) bc(80000000) a(63) id(0x0 0x1 0x2 0x3 0x0 0x0 0x0 0x0)]
input(iw ih ow oh):[0(1920 1080 1920 1080) 1(1920 1080 1920 1080) 2(1920 1080 1920 1080) 3(0 0 0 0) 4(1920 1080 1920 1080) 5(1920 1080 1920 1080) 6(0 0 0 0) 7(0 0 0 0) ]
vert_cut(start_line, video_src):[0(0 0) 1(555 0) 2(0 1) 3(555 1) 4(0 2) 5(554 2) 6(0 4) 7(555 4) 8(0 5) 9(555 5) 10(0 0) 11(0 0) 12(0 0) 13(0 0) 14(0 0) 15(0 0) 16(0 0) 17(0 0) 18(0 0) 19(0 0) ]
horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[0(0 959 1 0) 1(960 1919 1 0) 2(0 959 1 1) 3(960 1919 1 1) 4(0 959 1 2) 5(960 1919 1 2) 6(0 959 0 3) 7(0 959 1 4) 8(960 1919 1 4) 9(0 959 0 5) 10(0 959 1 6) 11(960 1919 1 6) 12(0 959 0 7) 13(0 959 1 8) 14(960 1919 1 8) 15(0 959 0 9) 16(0 0 0 20) 17(0 0 0 20) 18(0 0 0 20) 19(0 0 0 20) ]
output(oport olayer iw ih ow oh):[0(0 0 960 555 1920 1080) 1(1 0 960 555 1920 1080) 2(2 0 960 525 1920 1020) 3(3 0 960 525 1920 1020) 4(0 2 960 555 1920 1080) 5(1 2 960 555 1920 1080) 6(2 2 960 525 1920 1022) 7(0 4 960 554 1920 1080) 8(1 4 960 554 1920 1080) 9(2 4 960 526 1920 1024) 10(0 1 960 555 1920 1080) 11(1 1 960 555 1920 1080) 12(2 1 960 525 1920 1021) 13(0 3 960 555 1920 1080) 14(1 3 960 555 1920 1080) 15(2 3 960 525 1920 1023) 16(0 6 0 0 0 0) 17(0 6 0 0 0 0) 18(0 6 0 0 0 0) 19(0 6 0 0 0 0) ]
layer(start_line start_px w h a):16[0(0 0 1920 1080 127) 1(0 0 1920 1080 127) 2(0 0 1920 1080 127) 3(0 0 1920 1080 127) 4(0 0 1920 1080 127) 6(0 0 1920 1080 127) 7(0 0 1920 1080 127) 8(0 0 1920 1080 127) 9(0 0 1920 1080 127) 10(0 0 1920 1080 127) 12(0 0 1920 1020 127) 13(0 0 1920 1021 127) 14(0 0 1920 1022 127) 15(0 0 1920 1023 127) 16(0 0 1920 1024 127) 18(0 0 1920 1020 127) ]
