package com.mc.tool.caesar.vpm.gui.pages.operation.videowall;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import javafx.scene.control.Button;
import javafx.scene.control.ToggleButton;
import javafx.stage.Stage;
import org.controlsfx.property.editor.NumericField;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.testfx.api.FxRobot;

/**
 * .
 */
@RunWith(MockitoJUnitRunner.class)
public class BgImgTest extends AppGuiTestBase {

  @Mock private FileDialogueFactory fileDialogueFactory;

  @Mock private FileDialogue fileDialogue;

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder -> binder.bind(FileDialogueFactory.class).toInstance(fileDialogueFactory));
    when(fileDialogueFactory.createFileDialogue()).thenReturn(fileDialogue);
    super.start(stage);
  }

  @Test
  public void uploadTest() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      rightClickOn(GuiTestConstants.VP6_DEMO_NAME);
      clickOn(GuiTestConstants.MENU_CREATE_VIDEO_WALL);
      clickOn("#" + CaesarOperationPageNew.NAME);
      clickOn(GuiTestConstants.TOOLBAR_VIDEOWALL_SHOW_MODE);

      Button button = lookup(GuiTestConstants.BGIMG_UPLOAD_PROPERTY).lookup(".button").query();
      scrollToShow(button);
      clickOn(button);

      FxRobot window = targetWindow(GuiTestConstants.WINDOW_BGIMG_UPLOAD);
      Button selectBtn = window.lookup(GuiTestConstants.BGIMG_UPLOAD_SELECT_BTN).queryButton();
      ToggleButton uploadBtn = window.lookup(GuiTestConstants.BGIMG_UPLOAD_UPLOAD_BTN).query();
      // 没有选择文件，不能上传
      Assert.assertTrue(uploadBtn.isDisabled());
      Assert.assertFalse(selectBtn.isDisabled());

      File imgFile =
          copyResourceToTemp(
              "com/mc/tool/caesar/vpm/videowall/bgimg_upload_3840_2160.bmp", "test", ".bmp");
      when(fileDialogue.showOpenDialog(any())).thenReturn(imgFile);
      clickOn(selectBtn);
      Assert.assertFalse(uploadBtn.isDisabled());
      clickOn(uploadBtn);
      Thread.sleep(1000);

      Button finishBtn = window.lookup(GuiTestConstants.BUTTON_FINISH).queryButton();
      while (finishBtn.isDisabled()) {
        Thread.sleep(10);
      }
      clickOn(finishBtn);
      // 检查数据
      NumericField bgImgWidth = window.lookup(GuiTestConstants.BGIMG_WIDTH_PROPERTY).query();
      NumericField bgImgHeight = window.lookup(GuiTestConstants.BGIMG_HEIGHT_PROPERTY).query();
      Assert.assertEquals("3840", bgImgWidth.getText());
      Assert.assertEquals("2160", bgImgHeight.getText());

      imgFile.delete();
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException | InterruptedException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
