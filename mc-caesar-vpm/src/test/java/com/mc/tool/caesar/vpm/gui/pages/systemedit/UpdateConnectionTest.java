package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.tool.caesar.api.DemoSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.device.CaesarDemoDataModelFactory;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.framework.view.FrameView;
import java.io.File;
import java.io.IOException;
import javafx.stage.Stage;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * .
 */
public class UpdateConnectionTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Override
  public void start(Stage stage) throws Exception {
    FrameView.setModule(
        binder ->
            binder.bind(CaesarDemoDataModelFactory.class).toInstance(new UpdateDemoCreater()));
    super.start(stage);
  }

  @Test
  @Ignore
  public void testFrequentChangeConnection() {
    try {
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }

      clickOn(GuiTestConstants.CON_01);

      Thread.sleep(1000000);
    } catch (IOException | InterruptedException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  static class UpdateDemoCreater implements CaesarDemoDataModelFactory {

    @Override
    public DemoSwitchDataModel create() {
      return new UpdateDemoModel();
    }

    @Override
    public DemoSwitchDataModel create(boolean onlyConfig) {
      return new UpdateDemoModel(onlyConfig);
    }
  }

  static class UpdateDemoModel extends DemoSwitchDataModel {
    private boolean tic = false;

    public UpdateDemoModel() {
      super();
    }

    public UpdateDemoModel(boolean onlyConfig) {
      super(onlyConfig);
    }

    @Override
    public void reloadCpuConsoleMatrix() throws ConfigException {
      super.reloadCpuConsoleMatrix();
      CpuData firstCpu = this.getConfigData().getCpuData(1);
      ConsoleData firstConsole = this.getConfigData().getConsoleData(1);
      if (tic) {
        firstCpu.setConsoleData(null);
        firstConsole.setCpuData(null);
        commit();
      } else {
        firstCpu.setConsoleData(firstConsole);
        firstConsole.setCpuData(firstCpu);
        commit();
      }
      tic = !tic;
    }
  }
}
