package com.mc.tool.caesar.vpm.gui.pages.operation.videowall;

import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import java.io.File;
import java.io.IOException;
import javafx.scene.input.KeyCode;
import org.controlsfx.property.editor.NumericField;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class OsdTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testAlphaValue() {
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      rightClickOn(GuiTestConstants.VP6_DEMO_NAME);
      clickOn(GuiTestConstants.MENU_CREATE_VIDEO_WALL);
      clickOn("#" + CaesarOperationPageNew.NAME);
      clickOn(GuiTestConstants.TOOLBAR_VIDEOWALL_SHOW_MODE);
      NumericField field = lookup(GuiTestConstants.OSD_ALPHA_PROPERTY).query();
      // 超过最大值
      writeOsdAlpha("1.1");
      Assert.assertEquals("1.0", field.getText());
      // 小于最小值
      writeOsdAlpha("-10");
      Assert.assertEquals("0.0", field.getText());

      // 超过精度
      writeOsdAlpha("0.55555");
      Assert.assertEquals("0.56", field.getText());

      writeOsdAlpha("0.544444");
      Assert.assertEquals("0.54", field.getText());
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  private void writeOsdAlpha(String value) {
    clickOn(GuiTestConstants.OSD_ALPHA_PROPERTY);
    type(KeyCode.END);
    eraseText(10);
    write(value);
    type(KeyCode.ENTER);
  }
}
