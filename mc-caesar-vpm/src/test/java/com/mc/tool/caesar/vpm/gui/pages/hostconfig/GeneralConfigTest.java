package com.mc.tool.caesar.vpm.gui.pages.hostconfig;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.caesar.vpm.pages.hostconfiguration.CaesarHostConfigurationPage;
import java.io.File;
import java.io.IOException;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class GeneralConfigTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void simpleTest() {
    try {
      clickOn("#" + CaesarHostConfigurationPage.NAME);

      Button commitBtn = lookup(GuiTestConstants.GENERAL_FORM_COMMIT_BTN).queryButton();
      Button cancelBtn = lookup(GuiTestConstants.GENERAL_FORM_CANCEL_BTN).queryButton();
      // 检查hostname长度
      checkTextFieldName(
          GuiTestConstants.GENERAL_FORM_HOST_NAME, CaesarConstants.NAME_LEN, commitBtn, cancelBtn);
      // 检查devicename
      checkTextFieldName(
          GuiTestConstants.GENERAL_FORM_DEVICE_NAME,
          CaesarConstants.MATRIX_DEVICE_LEN,
          commitBtn,
          cancelBtn);
      // 检查info
      checkTextFieldName(
          GuiTestConstants.GENERAL_FORM_INFO,
          CaesarConstants.MATRIX_INFO_LEN,
          commitBtn,
          cancelBtn);

      // 用户osd
      checkUserOsd(commitBtn, cancelBtn);

      // 检查checkbox勾选
      checkCheckBox(GuiTestConstants.GENERAL_FORM_ALLOW_CON_OSD, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_NEW_USER_FOR_CPU, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_NEW_CON_FOR_CPU, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FROM_AUTO_DISCONNECT_WHEN_OSD, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_ALLOW_VIDEO_SHARE, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_FULL_WITH_VIDEO_SHARE, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_FULL_WITH_VIDEO_DISCONNECT, commitBtn, cancelBtn);

      checkCheckBox(GuiTestConstants.GENERAL_FORM_ALLOW_UART, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_ALLOW_COM_ECHO, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_ALLOW_LAN_ECHO, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_FORCE_PUSH_GET, commitBtn, cancelBtn);

      checkCheckBox(GuiTestConstants.GENERAL_FORM_DEBUG_LEVEL, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_INFO_LEVEL, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_NOTICE_LEVEL, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_WARNING_LEVEL, commitBtn, cancelBtn);
      checkCheckBox(GuiTestConstants.GENERAL_FORM_ERROR_LEVEL, commitBtn, cancelBtn);
      // 关联checkbox
      CheckBox fullWithVideoDisconnectCheckBox =
          lookup(GuiTestConstants.GENERAL_FORM_FULL_WITH_VIDEO_DISCONNECT).query();
      CheckBox allowVideoShareCheckBox =
          lookup(GuiTestConstants.GENERAL_FORM_ALLOW_VIDEO_SHARE).query();
      // 必须同时勾选
      Assert.assertFalse(fullWithVideoDisconnectCheckBox.isSelected());
      Assert.assertFalse(allowVideoShareCheckBox.isSelected());
      clickOn(fullWithVideoDisconnectCheckBox);
      Assert.assertTrue(fullWithVideoDisconnectCheckBox.isSelected());
      Assert.assertTrue(allowVideoShareCheckBox.isSelected());
      clickOn(cancelBtn);
      // 必须同时勾选
      CheckBox fullWithVideoShareCheckBox =
          lookup(GuiTestConstants.GENERAL_FORM_FULL_WITH_VIDEO_SHARE).query();
      Assert.assertFalse(fullWithVideoShareCheckBox.isSelected());
      Assert.assertFalse(allowVideoShareCheckBox.isSelected());
      clickOn(fullWithVideoShareCheckBox);
      Assert.assertTrue(fullWithVideoShareCheckBox.isSelected());
      Assert.assertTrue(allowVideoShareCheckBox.isSelected());
      clickOn(cancelBtn);
      // 互斥
      Assert.assertFalse(fullWithVideoDisconnectCheckBox.isSelected());
      Assert.assertFalse(fullWithVideoShareCheckBox.isSelected());
      clickOn(fullWithVideoDisconnectCheckBox);
      clickOn(fullWithVideoShareCheckBox);
      Assert.assertFalse(fullWithVideoDisconnectCheckBox.isSelected());
      Assert.assertTrue(fullWithVideoShareCheckBox.isSelected());
      clickOn(fullWithVideoDisconnectCheckBox);
      Assert.assertTrue(fullWithVideoDisconnectCheckBox.isSelected());
      Assert.assertFalse(fullWithVideoShareCheckBox.isSelected());
      clickOn(cancelBtn);

      checkTextFieldRange(
          GuiTestConstants.GENERAL_FORM_TIMEOUT_DISPLAY, 0, 0x7fffffff, commitBtn, cancelBtn);
      checkTextFieldRange(
          GuiTestConstants.GENERAL_FORM_TIMEOUT_LOGOUT, -1, 0x7fffffff, commitBtn, cancelBtn);
      checkTextFieldRange(
          GuiTestConstants.GENERAL_FORM_TIMEOUT_DISCONNECT, 0, 0x7fffffff, commitBtn, cancelBtn);
      checkTextFieldRange(
          GuiTestConstants.GENERAL_FORM_AUTO_REJECT_TIME, 0, 127, commitBtn, cancelBtn);
      checkTimeoutShare(commitBtn, cancelBtn);

      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      tempFile.delete();
    } catch (IOException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  @Test
  public void testGridStatus() {
    // 级联状态下不能修改名称
    try {
      clickOn("#" + CaesarHostConfigurationPage.NAME);
      TextField textField = lookup(GuiTestConstants.GENERAL_FORM_HOST_NAME).query();
      Assert.assertTrue(textField.isDisable());
      textField = lookup(GuiTestConstants.GENERAL_FORM_DEVICE_NAME).query();
      Assert.assertTrue(textField.isDisable());

      // 还原状态时
      clickOn(GuiTestConstants.GENERAL_FORM_INFO);
      type(KeyCode.END);
      eraseText(10);
      clickOn(GuiTestConstants.GENERAL_FORM_CANCEL_BTN);
      textField = lookup(GuiTestConstants.GENERAL_FORM_HOST_NAME).query();
      Assert.assertTrue(textField.isDisable());
      textField = lookup(GuiTestConstants.GENERAL_FORM_DEVICE_NAME).query();
      Assert.assertTrue(textField.isDisable());

      File tempFile = loadResourceStatus("com/mc/tool/caesar/vpm/36_hw1.2_grid_full.status");
      tempFile.delete();
    } catch (IOException e) {
      e.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }

  private void checkUserOsd(Button commitBtn, Button cancelBtn) {
    CheckBox forceUserLoginOsdCheckBox =
        lookup(GuiTestConstants.GENERAL_FORM_FORCE_USER_LOGIN_OSD).query();
    CheckBox allowUserOsdCheckBox = lookup(GuiTestConstants.GENERAL_FORM_ALLOW_USER_OSD).query();
    boolean oldValue = forceUserLoginOsdCheckBox.isSelected();
    // 检查值同步
    Assert.assertEquals(forceUserLoginOsdCheckBox.isSelected(), allowUserOsdCheckBox.isSelected());
    clickOn(forceUserLoginOsdCheckBox);
    Assert.assertEquals(!oldValue, allowUserOsdCheckBox.isSelected());
    clickOn(forceUserLoginOsdCheckBox);
    Assert.assertEquals(oldValue, allowUserOsdCheckBox.isSelected());
    clickOn(allowUserOsdCheckBox);
    Assert.assertEquals(!oldValue, forceUserLoginOsdCheckBox.isSelected());
    clickOn(allowUserOsdCheckBox);
    Assert.assertEquals(oldValue, forceUserLoginOsdCheckBox.isSelected());

    clickOn(forceUserLoginOsdCheckBox);
    Assert.assertFalse(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    clickOn(cancelBtn);
    Assert.assertEquals(oldValue, forceUserLoginOsdCheckBox.isSelected());
  }

  private void checkCheckBox(String checkboxSelector, Button commitBtn, Button cancelBtn) {
    CheckBox checkBox = lookup(checkboxSelector).query();
    scrollToShow(checkBox);
    clickOn(checkBox);
    Assert.assertFalse(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    clickOn(cancelBtn);
    boolean oldValue = checkBox.isSelected();
    Assert.assertEquals(oldValue, checkBox.isSelected());
  }

  private void checkTextFieldName(
      String textFieldSelector, int len, Button commitBtn, Button cancelBtn) {
    TextField textField = lookup(textFieldSelector).query();
    clickOn(textField);
    eraseText(40);
    Assert.assertFalse(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    StringBuilder builder = new StringBuilder();
    for (int i = 0; i < len + 1; i++) {
      builder.append('a');
    }
    write(builder.toString());
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());
    clickOn(cancelBtn);
    String oldName = textField.getText();
    Assert.assertEquals(oldName, textField.getText());
  }

  private void checkTextFieldRange(
      String textFieldSelector, long min, long max, Button commitBtn, Button cancelBtn) {
    TextField textField = lookup(textFieldSelector).query();
    scrollToShow(textField);
    clickOn(textField);
    eraseText(20);
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());

    String newValue = min + "";
    write(min + "");
    String oldVale = textField.getText();
    Assert.assertEquals(newValue.equals(oldVale), commitBtn.isDisabled());
    Assert.assertEquals(newValue.equals(oldVale), cancelBtn.isDisabled());

    newValue = (min - 1) + "";
    eraseText(20);
    write((min - 1) + "");
    if (min == 0) {
      // 最小值为0时不能输入负号
      Assert.assertNotEquals(newValue, textField.getText());
    } else {
      Assert.assertTrue(commitBtn.isDisabled());
      Assert.assertFalse(cancelBtn.isDisabled());
    }

    newValue = max + "";
    eraseText(20);
    write(max + "");
    Assert.assertEquals(newValue.equals(oldVale), commitBtn.isDisabled());
    Assert.assertEquals(newValue.equals(oldVale), cancelBtn.isDisabled());

    eraseText(20);
    write((max + 1) + "");
    Assert.assertTrue(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());

    clickOn(cancelBtn);
    Assert.assertEquals(oldVale, textField.getText());
  }

  private void checkTimeoutShare(Button commitBtn, Button cancelBtn) {
    CheckBox checkBox = lookup(GuiTestConstants.GENERAL_FORM_TIMEOUT_SHARE_ENABLE).query();
    TextField textField = lookup(GuiTestConstants.GENERAL_FORM_TIMEOUT_SHARE).query();
    scrollToShow(checkBox);
    Assert.assertEquals(!checkBox.isSelected(), textField.isDisabled());

    clickOn(checkBox);
    Assert.assertEquals(!checkBox.isSelected(), textField.isDisabled());
    Assert.assertFalse(commitBtn.isDisabled());
    Assert.assertFalse(cancelBtn.isDisabled());

    if (textField.isDisabled()) {
      clickOn(checkBox);
    }

    checkTextFieldRange(
        GuiTestConstants.GENERAL_FORM_TIMEOUT_SHARE, 0, 0x7fffffff, commitBtn, cancelBtn);
  }
}
