package com.mc.tool.caesar.vpm.gui.pages.systemedit;

import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.GuiTestConstants;
import com.mc.tool.caesar.vpm.gui.AppGuiTestBase;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.File;
import java.io.IOException;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class ModelTest extends AppGuiTestBase {

  @Override
  public void beforeAll() {}

  @Test
  public void testParentError() {
    // 即使node的parent错误的，也能恢复
    try {
      clickOn(GuiTestConstants.RESTORE_GRAPH);

      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      CaesarEntity entity = (CaesarEntity) app.getEntityMananger().getAllEntity().iterator().next();
      VisualEditTerminal terminal = entity.getVisualEditModel().getAllTerminals().iterator().next();
      terminal.setParent(null);
      terminal = null;
      entity = null;
      // 关闭页面
      clickOn(GuiTestConstants.BUTTON_LIST_MENU);
      clickOn(GuiTestConstants.MENU_CLOSE_ALL);
      // 重新加载
      System.setProperty("use-status-model", "false");
      File tempFile =
          loadResourceStatus("com/mc/tool/caesar/vpm/update/single_36_hw1.2_sample.status");
      loadFileStatus(tempFile);

      clickOn(GuiTestConstants.RESTORE_GRAPH);
      for (int i = 0; i < 10; i++) {
        clickOn(GuiTestConstants.ZOOM_OUT_GRAPH);
      }
      // 创建组合
      String name = terminal.getName();
      rightClickOn(name);
      clickOn(GuiTestConstants.MENU_GROUP);
      lookup("Group0").query();

      tempFile.delete();
    } catch (IOException exception) {
      exception.printStackTrace();
      Assert.fail("Expect no exception!");
    }
  }
}
