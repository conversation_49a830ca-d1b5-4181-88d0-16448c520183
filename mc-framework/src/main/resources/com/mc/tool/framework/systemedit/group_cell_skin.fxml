<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<HBox xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
      stylesheets="@group_cell_skin.css" styleClass="group" minHeight="35" maxHeight="35" minWidth="116" maxWidth="116"
      alignment="CENTER">
  <Region minWidth="16" maxWidth="16" minHeight="16" maxHeight="16" fx:id="childrenConnector" styleClass="parent-connector"/>
  <HBox HBox.Hgrow="ALWAYS" alignment="CENTER" fx:id="mainContainer">
    <Region minWidth="5" maxWidth="5"/>
    <HBox HBox.Hgrow="ALWAYS" alignment="CENTER" fx:id="nameLabelContainer">
      <Label textAlignment="center" fx:id="nameLabel"/>
    </HBox>
    <Label fx:id="icon"/>
    <Region minWidth="5" maxWidth="5" fx:id="parentConnector"/>
  </HBox>
</HBox>