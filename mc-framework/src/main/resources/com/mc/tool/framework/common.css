
* {
    MC_COLOR: #f08519;
    LEFT_PANEL_COLOR: #e6e6e6;
    SEPERATOR_COLOR: #999999;

    -fx-accent: #f0ba84;
}

.image-button {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}

.check-box .box {
    -fx-background-color: white;
    -fx-border-color: #b3b3b3;
    -fx-border-radius: 2px;
    -fx-pref-height: 13;
    -fx-pref-width: 13;
}

.check-box:hover .box {
    -fx-border-color: #f08519;
}

.check-box:selected .mark {
    -fx-background-image: url("/com/mc/tool/framework/img/checkbox_mark.png");
    -fx-background-color: transparent;
    -fx-background-position: left;
    -fx-background-repeat: no-repeat;
    -fx-shape: none;
    -fx-pref-width: 9;
    -fx-pref-height: 7;
}

.radio-button .radio {
    -fx-background-color: white;
    -fx-border-color: #b3b3b3;
    -fx-border-radius: 2px;
    -fx-pref-height: 13;
    -fx-pref-width: 13;
    -fx-padding: 0;
}

.radio-button:hover .radio {
    -fx-border-color: #f08519;
}

.radio-button:selected .dot {
    -fx-background-image: url("/com/mc/tool/framework/img/checkbox_mark.png");
    -fx-background-color: transparent;
    -fx-background-position: center;
    -fx-background-repeat: no-repeat;
    -fx-shape: none;
    -fx-pref-width: 9;
    -fx-pref-height: 7;
}

.list-view {
    -fx-border-style: null;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-background-color: transparent;
}

.scroll-pane {
    -fx-border-style: null;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-background-color: transparent;
}

.scroll-pane > .corner {
    -fx-background-color: #f0f0f0;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-bar:horizontal, .scroll-bar:vertical {
    -fx-background-color: #f0f0f0;
}

.scroll-bar:horizontal .thumb, .scroll-bar:vertical .thumb {
    -fx-background-color: #d9d9d9;
    -fx-background-radius: 6;
}

.scroll-bar:horizontal .thumb:hover, .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #cccccc;
}

.form-content-box {
    -fx-background-color: transparent;
}

/* Style for tab panel. */
.tab-pane {
    -fx-tab-min-width: 90px;
    -fx-tab-min-height: 28px;
    -fx-background-color: white;
}

.tab-pane {
    -fx-tab-min-height: 40px;
}

.tab-pane > .tab-header-area {
    -fx-padding: 0;
}

.tab-pane .tab-header-background {
    -fx-background-color: #cccccc, white;
    -fx-background-insets: 0, 0 0 1 0;
}

.tab-pane .tab {
    -fx-background-color: white;
    -fx-background-image: null;
}

.tab-pane .tab .tab-label {
    -fx-font-size: 12;
    -fx-text-fill: black;
}

.tab-pane .tab:selected {
    -fx-background-color: MC_COLOR, white;
    -fx-background-insets: 0, 0 0 2 0;
    -fx-background-image: null;
}

.tab-pane .tab:selected .focus-indicator {
    -fx-border-width: 0;
}

.tab-pane .tab:selected .tab-label {
    -fx-text-fill: MC_COLOR;
}

.tab-pane .tab:hover .tab-label {
    -fx-text-fill: MC_COLOR;
}

.table-view {
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-color: #e6e6e6;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-background-color: transparent;
    -fx-padding: 0;
}

.table-view .column-header-background {
    -fx-pref-height: 36;
    -fx-background-color: #e6e6e6;
}

.table-view .column-header {
    -fx-background-color: #e6e6e6, #cccccc, #e6e6e6;
    -fx-background-insets: 0, 10 0 10 0, 0 1 0 0;
    -fx-shape: none;
    -fx-background-image: none;
}

.table-view .column-header .label {
    -fx-text-fill: #333333;
    -fx-font-weight: normal;
    -fx-text-alignment: left;
    -fx-alignment: center-left;
    -fx-label-padding: 0 0 0 5;
}

.table-view .column-header .arrow {
    -fx-background-color: #cfcfcf;
}


.table-view .column-header-background .filler {
    -fx-background-color: #e6e6e6;
}

.table-view .table-row-cell .table-cell {
    -fx-alignment: center_left;
}

/* tree table view */

.tree-table-view {
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-color: #e6e6e6;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-background-color: transparent;
    -fx-padding: 0;
}

.tree-table-view .column-header-background {
    -fx-pref-height: 36;
    -fx-background-color: #e6e6e6;
}

.tree-table-view .column-header {
    -fx-background-color: #e6e6e6, #cccccc, #e6e6e6;
    -fx-background-insets: 0, 10 0 10 0, 0 1 0 0;
    -fx-shape: none;
    -fx-background-image: none;
}

.tree-table-view .column-header .label {
    -fx-text-fill: #333333;
    -fx-font-weight: normal;
    -fx-text-alignment: left;
    -fx-alignment: center-left;
    -fx-label-padding: 0 0 0 5;
}

.tree-table-view .column-header .arrow {
    -fx-background-color: #cfcfcf;
}


.tree-table-view .column-header-background .filler {
    -fx-background-color: #e6e6e6;
}

.tree-table-view .tree-table-row-cell {
    -fx-cell-size: 36px;
}

.tree-table-view .tree-table-row-cell .tree-table-cell {
    -fx-alignment: center_left;
}

.tree-table-view .tree-table-row-cell:odd {
    -fx-background-color: white;
}

.tree-table-view .tree-table-row-cell:even {
    -fx-background-color: #fafafa;
}

.tree-table-view .tree-table-row-cell:focused {
    -fx-background-color: -fx-focus-color, -fx-cell-focus-inner-border, -fx-control-inner-background;
    -fx-background-insets: 0, 1, 2;
}

.tree-table-view .tree-table-row-cell > .tree-disclosure-node {
    -fx-padding: 12 8 8 8;
}

.tree-table-view .tree-table-row-cell > .tree-disclosure-node > .arrow {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-padding: 0;
    -fx-shape: null;
    -fx-background-image: url("/com/mc/tool/framework/img/arrow.png");
    -fx-background-position: center;
    -fx-background-repeat: no-repeat;
    -fx-pref-width: 8;
    -fx-pref-height: 8;
}

/* tree view */

.tree-view {
    -fx-border-style: null;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-background-color: transparent;
    -fx-padding: 0;
    -fx-fixed-cell-size: 32px;
}

.tree-view .tree-cell {
}

.tree-view .tree-cell > .tree-disclosure-node {
    -fx-padding: 10 8 8 8;
}

.tree-view .tree-cell:odd {
    -fx-background-color: white;
}

.tree-view .tree-cell:even {
    -fx-background-color: #fafafa;
}

.tree-view .tree-cell:selected {
    -fx-background-color: #e6e6e6;
}

.tree-view .tree-cell > .tree-disclosure-node > .arrow {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-padding: 0;
    -fx-shape: null;
    -fx-background-image: url("/com/mc/tool/framework/img/arrow.png");
    -fx-background-position: center;
    -fx-background-repeat: no-repeat;
    -fx-pref-width: 8;
    -fx-pref-height: 8;
}

/* propertysheet */

.property-sheet {
    -fx-background-color: white;
}

.property-sheet .scroll-pane {
    -fx-background-color: white;
}

.property-sheet .scroll-pane .viewport {
    -fx-background-color: white;
}

.property-sheet .property-pane {
    -fx-background-color: white;
    -fx-grid-lines-visible: true;
    -fx-padding: 0;
    -fx-hgap: 0;
    -fx-vgap: 0;
}

.property-sheet .property-pane Line {
}

.property-sheet .property-pane Label {
    -fx-label-padding: 0 0 0 10;
}

.property-sheet .property-pane TextField, .property-sheet .property-pane NumericField {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}


.master-detail-pane .split-pane {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-padding: 0;
}

.master-detail-pane .split-pane:horizontal > .split-pane-divider {
    -fx-background-color: #cccccc;
    -fx-pref-width: 1;
}

.menu-item:focused > .label {
    -fx-text-fill: black;
}

.menu-bar > .container > .menu-button:hover {
    -fx-background-color: transparent;
    -fx-border-size: 1;
    -fx-border-color: #4d4d4d;
    -fx-border-radius: 2;
}

.menu-bar > .container > .menu-button:hover, .menu-bar > .container > .menu-button:focused, .menu-bar > .container > .menu-button:showing {
    -fx-background-color: transparent;
    -fx-border-size: 1;
    -fx-border-color: #4d4d4d;
    -fx-border-radius: 2;
}

.common-button {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-color: #b2b2b2;
    -fx-border-radius: 2px;
    -fx-background-color: #f2f2f2;
    -fx-background-repeat: no-repeat;
}

.common-button:hover {
    -fx-background-color: #e6e6e6;
}

.combo-box {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-radius: 4;
    -fx-border-color: #cfcfcf;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
    -fx-skin: "com.mc.tool.framework.controller.SafeComboBoxSkin";
}

.combo-box .arrow-button {
    -fx-padding: 0 8 0 3;
    -fx-background-radius: 0;
    -fx-border-style: null;
}

.combo-box .arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
}

.choice-box {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-radius: 4;
    -fx-border-color: #cfcfcf;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}

.choice-box .arrow-button {
    -fx-padding: 0 8 0 3;
    -fx-background-radius: 0;
    -fx-border-style: null;
}

.choice-box .arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
}

.color-picker {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-radius: 4;
    -fx-border-color: #cfcfcf;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}

.color-picker .arrow-button {
    -fx-padding: 0 8 0 3;
    -fx-background-radius: 0;
    -fx-border-style: null;
}

.color-picker .arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
}

.property-combo {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-border-size: 0;
    -fx-border-radius: 0;
    -fx-border-color: null;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}

.property-combo .arrow-button {
    -fx-padding: 0 3 0 3;
    -fx-background-color: #e6e6e6;
    -fx-background-radius: 0;
    -fx-border-style: null;
}

.property-combo .arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
}

.property-color-picker {
    -fx-border-style: null;
    -fx-border-size: 0;
    -fx-border-radius: 0;
    -fx-border-color: null;
}

.property-spinner {
    -fx-background-color: white;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
}

.property-spinner:focused,
.property-spinner:contains-focus {
    -fx-background-color: white;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
}

.spinner:focused, .spinner:contains-focus {
    -fx-background-color: -fx-shadow-highlight-color, -fx-text-box-border, -fx-control-inner-background;
    -fx-background-insets: 0, 1, 2;
    -fx-background-radius: 3, 2, 2;
}

.spinner > .text-field {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
}

.spinner .increment-arrow-button {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
}

.spinner .decrement-arrow-button {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
}

.spinner .increment-arrow-button .increment-arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
    -fx-rotate: 180;
}

.spinner .decrement-arrow-button .decrement-arrow {
    -fx-background-image: url("/com/mc/tool/framework/img/combo_arrow.png");
    -fx-background-repeat: no-repeat;
    -fx-background-size: contain;
    -fx-background-position: center;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-shape: none;
    -fx-background-color: transparent;
    -fx-padding: 2 3 2 4;
}

.text-field:focused {
    -fx-background-color: -fx-shadow-highlight-color, -fx-text-box-border, -fx-control-inner-background;
    -fx-background-insets: 0, 1, 2;
    -fx-background-radius: 3, 2, 2;
    -fx-prompt-text-fill: transparent;
}