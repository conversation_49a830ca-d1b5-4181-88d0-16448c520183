#root {
    -fx-background-color: #ffffff;
}


#header-toolbox {
    -fx-spacing: 30;
    -fx-padding: 0 10 0 0;
}

#function-title {
    -fx-label-padding: 0 0 0 9;
}

#seperator {
    -fx-background-color: #cccccc;
}

#body-pane {
    -fx-padding: 10;
}

#source-list-title-container {
    -fx-background-color: #cccccc;
}

#source-list-title {
    -fx-label-padding: 0 0 0 10;
}

#save-btn {
    -fx-graphic: url("menu_normal.png");
    -fx-text-fill: #333333;
}

#save-btn:hover {
    -fx-graphic: url("menu_hover.png");
    -fx-text-fill: #f08519;
}

#saveas-btn {
    -fx-graphic: url("menu_normal.png");
    -fx-text-fill: #333333;
}

#saveas-btn:hover {
    -fx-graphic: url("menu_hover.png");
    -fx-text-fill: #f08519;
}

#config-btn {
    -fx-graphic: url("config_normal.png");
    -fx-text-fill: #333333;
}

#config-btn:hover {
    -fx-graphic: url("config_hover.png");
    -fx-text-fill: #f08519;
}

#switch-btn {
    -fx-graphic: url("switch_normal.png");
    -fx-text-fill: #333333;
}

#switch-btn:hover {
    -fx-graphic: url("switch_hover.png");
    -fx-text-fill: #f08519;
}

#function-source {
    -fx-border-size: 1px;
    -fx-border-color: #cccccc;
}

#function-content {
    -fx-border-width: 1 1 1 0;
    -fx-border-color: #cccccc;
}


#left-arrow {
    -fx-background-image: url("left_arrow_normal.png");
}

#left-arrow:hover {
    -fx-background-image: url("left_arrow_hover.png");
}

#left-arrow:disable {
    -fx-background-image: url("left_arrow_disable.png");
}

#right-arrow {
    -fx-background-image: url("right_arrow_normal.png");
}

#right-arrow:hover {
    -fx-background-image: url("right_arrow_hover.png");
}

#right-arrow:disable {
    -fx-background-image: url("right_arrow_disable.png");
}

#sourceListStyleBtn {
    -fx-graphic: url("list.png");
}

#sourceListStyleBtn:hover {
    -fx-graphic: url("list_hover.png");
}

#sourceListStyleBtn:selected {
    -fx-graphic: url("snapshot.png");
}

#sourceListStyleBtn:selected:hover {
    -fx-graphic: url("snapshot_hover.png");
}


.grid-view {
    -fx-cell-width: 71;
    -fx-cell-height: 118;
    -fx-horizontal_alignment: center;
}


.list-cell {
    -fx-cell-size: 32;
    -fx-background-color: white;
}

.list-cell:selected {
    -fx-background-color: #e6e6e6;
    -fx-text-fill: black;
}