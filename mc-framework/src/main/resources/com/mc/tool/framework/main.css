@import "common.css";

.root {
    -fx-background-color: #ffffff;
}

* {
    HEADER-COLOR: #333333;
    -fx-font-family: "Microsoft YaHei";
    -fx-font-size: 12;
}

#header {
    -fx-background-color: HEADER-COLOR;
}

#header-seperator {
    -fx-background-color: SEPERATOR_COLOR;
}

#left-panel-seperator {
    -fx-background-color: #cccccc;
}

/* Style for menu. */
#menu-container {
    -fx-pref-height: 45;
    -fx-min-height: 45;
    -fx-background-color: HEADER-COLOR;
    -fx-alignment: center-left;
    -fx-padding: 0 0 0 8;
}

.menu-bar-separator {
    -fx-pref-width: 16;
    -fx-background-color: #fc9631, #dc7307;
    -fx-background-insets: 11 8 11 7, 11 7 11 8;
}

.menu-bar {
    -fx-background-color: HEADER-COLOR;
}

.menu-bar .menu .label {
    -fx-font-size: 14;
    -fx-text-fill: white;
    -fx-label-padding: 0;
}

.menu-bar .menu .menu-item .label {
    -fx-text-fill: black;
}


/* Style for object list panel */
#device-view .object-logo {
    -fx-background-image: url("./img/device_list_logo.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#monitor-view .object-logo {
    -fx-background-image: url("./img/monitor_list_logo.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#leftPanel {
    -fx-background-color: LEFT_PANEL_COLOR;
}

#rightPanel {
    -fx-background-color: white;
}

#hide-show-menu-btn {
    -fx-graphic: url("./img/hide_menu_btn.png");
    -fx-pref-width: 10;
    -fx-pref-height: 36;
    -fx-padding: 0;
    -fx-background-color: #f2f2f2;
    -fx-border-size: 1;
    -fx-border-radius: 2;
    -fx-border-color: #cccccc;
    -fx-border-style: solid outside;
}

#hide-show-menu-btn:hover {
    -fx-background-color: #e6e6e6;
}

.status-bar {
    -fx-background-color: white;
}

.seperator {
    -fx-background-color: #cccccc;
}

#start-image {
    -fx-image: url("./img/start_image.png");
}

.start-button {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-color: #b2b2b2;
    -fx-border-radius: 2px;
    -fx-background-color: #f2f2f2;
    -fx-background-repeat: no-repeat;
}

.start-button:hover {
    -fx-background-color: #e6e6e6;
}

#start-button-container {
    -fx-spacing: 10px;
}