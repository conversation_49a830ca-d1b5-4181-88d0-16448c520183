
.tab *.tab-label {
    -fx-rotate: 90;
}

.tab-pane {
    -fx-padding: 0;
}

.tab-pane .tab {
    -fx-background-color: #cccccc;
    -fx-background-image: null;
    -fx-background-radius: 0;
    -fx-background-insets: 0;
    -fx-padding: 3em 0.5em 3em 0.5em;
}

.tab-pane .tab-header-background {
    -fx-background-color: #cccccc, white;
    -fx-background-insets: 0, 0 0 1 0;
}

.tab-pane > .tab-header-area {
    -fx-padding: 0;
}