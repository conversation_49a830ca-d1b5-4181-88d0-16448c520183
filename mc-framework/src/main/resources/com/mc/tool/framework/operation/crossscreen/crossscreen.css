@import "../scenario_list.css";

#screen-list-container {
    -fx-background-color: white;
}

#zoomin-btn {
    -fx-background-image: url("zoomin_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#zoomin-btn:hover {
    -fx-background-image: url("zoomin_hover.png");
}

#zoomin-btn:disable {
    -fx-background-image: url("zoomin_disable.png");
}

#zoomout-btn {
    -fx-background-image: url("zoomout_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#zoomout-btn:hover {
    -fx-background-image: url("zoomout_hover.png");
}

#zoomout-btn:disable {
    -fx-background-image: url("zoomout_disable.png");
}

#zoomin-btn:disable {
    -fx-background-image: url("zoomin_disable.png");
}

#restore-btn {
    -fx-background-image: url("restore_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#restore-btn:hover {
    -fx-background-image: url("restore_hover.png");
}

#restore-btn:disable {
    -fx-background-image: url("restore_disable.png");
}

#preview-btn {
    -fx-background-image: url("preview_normal.png");
    -fx-background-repeat: no-repeat;
    -fx-background-position: center;
}

#preview-btn:hover {
    -fx-background-image: url("preview_hover.png");
}

#preview-btn:disable {
    -fx-background-image: url("preview_disable.png");
}


#graph-tool-bar {
    -fx-spacing: 40;
    -fx-background-color: #f7f7f7;
}


.seperator {
    -fx-background-color: #f0f0f0;
}

#screen-list {
    -fx-alignment: center;
}