.image-button {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-background-color: null;
    -fx-background-repeat: no-repeat;
}

#search-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/search_normal.png");
    -fx-text-fill: #333333;
}

#refresh-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/refresh_normal.png");
    -fx-text-fill: #333333;
}

#search-btn:selected {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/search_hover.png");
    -fx-text-fill: #f08519;
}

#search-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/search_hover.png");
    -fx-text-fill: #f08519;
}

#refresh-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/refresh_hover.png");
    -fx-text-fill: #f08519;
}

.seperator {
    -fx-background-color: #cccccc;
}

#preview-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/preview_normal.png");
}

#preview-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/preview_hover.png");
}

#preview-btn:selected {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/preview_hover.png");
}

#zoomin-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/zoomin_normal.png");
}

#zoomin-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/zoomin_hover.png");
}

#zoomout-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/zoomout_normal.png");
}

#zoomout-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/zoomout_hover.png");
}

#restore-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/restore_normal.png");
}

#restore-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/restore_hover.png");
}

#switch-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/switch_normal.png");
}

#switch-btn:hover {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/switch_hover.png");
}

#graph-bottom-toolbar {
    -fx-spacing: 20;
}

#hide-property-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/hide_property.png");
}

#show-property-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/show_property.png");
}

#property-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/property_btn.png");
}

#icon-btn {
    -fx-graphic: url("/com/mc/tool/framework/systemedit/icon_btn.png");
}

.right-title {
    -fx-background-color: #e6e6e6;
    -fx-alignment: center-left;
    -fx-padding: 0 0 0 5;
}

#show-btn-container {
    -fx-border-width: 0 0 0 1;
    -fx-border-color: #cccccc;
}

#show-btn-sub-container {
    -fx-background-color: #e6e6e6;
}

#hide-btn-container {
    -fx-padding: 0;
}

#icon-grid-pane {
    -fx-grid-lines-visible: true;
}

#icon-grid-pane Label {
    -fx-label-padding: 0 0 0 10;
    -fx-min-height: 28;
}