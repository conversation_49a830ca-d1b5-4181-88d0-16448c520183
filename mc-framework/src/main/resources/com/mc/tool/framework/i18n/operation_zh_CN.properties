#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)
button.auto_arrange=自动单元排列
menu.align=对齐
menu.align.bottom=靠下
menu.align.horz_center=横向居中
menu.align.left=靠左
menu.align.right=靠右
menu.align.top=靠上
menu.align.vert_center=纵向居中
menu.audio_rx=音频RX
menu.center=居中
menu.delete=删除
menu.fit_screen=适配屏幕
menu.full_screen=全屏
menu.left_bottom_cornor=左下角
menu.left_top_cornor=左上角
menu.moveto=移动到
menu.right_bottom_cornor=右下角
menu.right_top_cornor=右上角
menu.save_scenario=保存场景
menu.saveas_scenario=另存为场景
menu.setting=设置
menu.split=切割
menu.to_bottom=置于底层
menu.to_down=下一层
menu.to_top=置于顶层
menu.to_up=上一层
property.audio_config=音频配置
property.config=功能配置
property.layout_properties=布局属性
property.logic_layout=逻辑布局
property.output=输出
property.test_screen=测试画面
property.video_layout=视频位置
property.video_window=视频窗口
property.windows_name=名称
property.windows_source=信号源
publish.auto=自动发布
publish.manual=手动发布
scenario_list_title=预案列表
source_list_title=信号源列表
toolbar.config=配置
toolbar.meeting_room=会议室布局
toolbar.pre_window=预开窗
toolbar.publish=发布
toolbar.save_scenario=保存预案
toolbar.saveas_scenario=另存为预案
toolbar.videowall=大屏显示模式
vpcon_tableview.dpi=分辨率
vpcon_tableview.index=序号
vpcon_tableview.name=名称
vpcon_tableview.title=显示单元
warning.create_seat_fail=无法创建坐席！
warning.create_videowall_fail=无法创建视频墙！
