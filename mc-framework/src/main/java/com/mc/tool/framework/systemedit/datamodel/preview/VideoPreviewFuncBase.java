package com.mc.tool.framework.systemedit.datamodel.preview;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.scene.control.DialogEx;
import lombok.Getter;
import org.controlsfx.control.PropertySheet.Item;
import org.controlsfx.control.TextBlockPropertyItem;

/**
 * .
 */
public abstract class VideoPreviewFuncBase extends VisualEditFunc {
  @Expose
  @Getter
  protected ObjectProperty<VideoPreviewData> videoPreviewData =
      new SimpleObjectProperty<>(new VideoPreviewData());

  @Override
  public boolean isOperatable() {
    return false;
  }

  @Override
  public void init() {
    super.init();
    getAllTerminalChild().addListener(new ListChangeListener<VisualEditTerminal>() {

      @Override
      public void onChanged(ListChangeListener.Change<? extends VisualEditTerminal> change) {
        // 重新生成列表，并把旧数据复制到新列表中
        List<VideoPreviewTerminal> newPreviewTerminals = new ArrayList<>();
        for (VisualEditTerminal terminal : getAllTerminalChild()) {
          VideoPreviewTerminal previewTerminal = new VideoPreviewTerminal();
          newPreviewTerminals.add(previewTerminal);
          previewTerminal.getTerminal().set(terminal);
          for (VideoPreviewTerminal oldPreviewTerminal : videoPreviewData.get().getTerminals()) {
            if (oldPreviewTerminal.getTerminal().get() == terminal) {
              previewTerminal.getAddress().set(oldPreviewTerminal.getAddress().get());
              break;
            }
          }
        }
        videoPreviewData.get().getTerminals().setAll(newPreviewTerminals);

        initProperties();
      }
    });
  }

  protected void initProperties() {
    properties.clear();
    addNameProperty();

    for (VideoPreviewTerminal previewTerminal : videoPreviewData.get().getTerminals()) {
      if (previewTerminal.getTerminal().get() == null) {
        continue;
      }
      ObjectProperty<String> addressProperty = new SimpleObjectProperty<>();
      addressProperty.bindBidirectional(previewTerminal.getAddress());
      TextBlockPropertyItem item = new TextBlockPropertyItem(addressProperty);
      item.getNameProperty().bind(previewTerminal.getTerminal().get().nameProperty()
          .concat(I18nUtility.getI18nBundle("systemedit").getString("property.address")));
      item.setTextAction((event) -> {
        ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        if (base != null) {
          DialogEx<String> dialog =
              ViewUtility.getTextDialog(base.getMainWindow(), addressProperty.get(), null, null);
          dialog.setContentText(item.getName());
          Optional<String> result = dialog.showAndWait();
          if (result.isPresent()) {
            addressProperty.set(result.get());
          }
        }
      });
      properties.add(item);
    }
  }

  @Override
  public ObservableList<Item> getProperties() {
    initProperties();
    return super.getProperties();
  }
}
