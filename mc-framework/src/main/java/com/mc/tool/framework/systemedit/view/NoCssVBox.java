package com.mc.tool.framework.systemedit.view;

import java.util.ArrayList;
import java.util.List;
import javafx.beans.value.WritableValue;
import javafx.scene.layout.VBox;

/**
 * .
 */
public class NoCssVBox extends VBox {
  @Override
  public List<String> impl_getAllParentStylesheets() {
    return new ArrayList<>();
  }

  @Override
  protected void impl_processCSS(WritableValue<Boolean> unused) {
  }
}
