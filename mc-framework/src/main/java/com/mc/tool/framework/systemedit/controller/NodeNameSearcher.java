package com.mc.tool.framework.systemedit.controller;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.Collection;
import javafx.util.StringConverter;

/**
 * .
 */
public class NodeNameSearcher extends NodeSearchBase {

  public NodeNameSearcher(
      Collection<VisualEditNode> roots) {
    super(roots);
  }

  @Override
  public StringConverter<VisualEditNode> createNodeStringConverter() {
    return new NodeNameStringConverter();
  }

  static class NodeNameStringConverter extends StringConverter<VisualEditNode> {
    @Override
    public String toString(VisualEditNode object) {
      return object.getName();
    }

    @Override
    public VisualEditNode fromString(String string) {
      return null;
    }
  }


}
