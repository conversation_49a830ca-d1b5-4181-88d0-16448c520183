package com.mc.tool.framework.systemedit.controller;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.Collection;
import javafx.util.Callback;
import javafx.util.StringConverter;
import org.controlsfx.control.textfield.AutoCompletionBinding.ISuggestionRequest;

/**
 * .
 */
public interface NodeSearcher {

  /**
   * 创建搜索建议callback.
   *
   * @return callback
   */
  Callback<ISuggestionRequest, Collection<VisualEditNode>> createSearchSuggestion();

  /**
   * 创建node的StringConverter.
   *
   * @return converter
   */
  StringConverter<VisualEditNode> createNodeStringConverter();

  /**
   * 搜索nodes.
   *
   * @param text      搜索文本.
   * @param searchAll 是否返回所有匹配的项
   * @return 搜索到的nodes
   */
  Collection<VisualEditNode> search(String text, boolean searchAll);
}
