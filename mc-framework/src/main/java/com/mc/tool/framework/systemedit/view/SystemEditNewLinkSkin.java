package com.mc.tool.framework.systemedit.view;

import com.mc.graph.DefaultNewLinkSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.NodeUtil;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import javafx.scene.Parent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;

/**
 * .
 */
public class SystemEditNewLinkSkin extends DefaultNewLinkSkin {

  public SystemEditNewLinkSkin(Connector sender, Parent parent, Parent container,
                               SkinManager skinManager) {
    super(sender, parent, container, skinManager);
  }

  @Override
  protected void initSenderAndReceiver() {
    receiverConnectorUi = new Circle(5);
    receiverConnectorUi.setLayoutX(skinManager.getConnectorSkin(sender).getNode().getLayoutX());
    receiverConnectorUi.setLayoutY(skinManager.getConnectorSkin(sender).getNode().getLayoutY());
    receiverConnectorUi.setUserData(NodeUtil.NODE_IGNORE);
  }

  @Override
  protected void initStyle() {
    connectionPath.setStroke(SystemEditDefinition.LINK_NORMAL_COLOR);
  }

  @Override
  public void setNoLink() {
    receiverConnectorUi.setFill(Color.rgb(50, 50, 50));
  }

}
