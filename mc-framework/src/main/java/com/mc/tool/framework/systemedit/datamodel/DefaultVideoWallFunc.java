package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.videowall.datamodel.ScreenData;
import com.mc.tool.framework.operation.videowall.datamodel.VideoData;
import com.mc.tool.framework.operation.videowall.datamodel.VideoWallData;
import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoData;
import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoWallData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.ArrayList;
import java.util.List;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultVideoWallFunc extends VideoWallFunc {
  @Expose
  @Getter
  private VideoWallData videoWallData = new VideoWallData();

  @Expose
  private ObservableList<VideoWallData> scenarios = FXCollections.observableArrayList();


  @Override
  public void init() {
    super.init();

    children.addListener(new ListChangeListener<VisualEditNode>() {

      @Override
      public void onChanged(ListChangeListener.Change<? extends VisualEditNode> change) {
        // 更新屏幕数据
        List<ScreenData> screens = new ArrayList<>();
        for (VisualEditNode node : children) {
          if (!(node instanceof VisualEditTerminal)) {
            log.warn("The function's child is not terminal!");
            continue;
          }
          VisualEditTerminal terminal = (VisualEditTerminal) node;
          if (!terminal.isRx()) {
            log.warn("The function's child is not rx!");
            continue;
          }
          ScreenData data = new ScreenData();
          data.getTarget().set(terminal);
          screens.add(data);
        }
        videoWallData.getScreens().setAll(screens);
      }

    });
  }

  @Override
  public VideoWallObject getVideoWallObject() {
    return videoWallData;
  }

  @Override
  public VideoWallObject createVideoWallObject() {
    return new VideoWallData();
  }

  @Override
  public ObservableList<? extends VideoWallObject> getScenarios() {
    return scenarios;
  }

  @Override
  public VideoWallObject toVideoWallObject(VirtualVideoWallData virtualData) {
    VideoWallObject input = getVideoWallObject();
    if (virtualData.getLayout() != null && (
        virtualData.getLayout().getRows().get() != input.getLayoutData().getRows()
            || virtualData.getLayout().getColumns().get() != input.getLayoutData().getColumns())) {
      return null;
    }
    VideoWallData data = new VideoWallData();
    input.copyTo(data, false);
    data.getVideos().clear();
    for (VirtualVideoData video : virtualData.getVideos()) {
      data.addVideo(video.toVideoData(createVideo(), data.getLayoutData().getTotalWidth(),
          data.getLayoutData().getTotalHeight()));
    }
    data.getName().set(virtualData.getName().get());
    return data;
  }

  @Override
  public VideoObject createVideo() {
    VideoData data = new VideoData();
    data.getName().set(getUniqueName());
    return data;
  }

  @Override
  public ScreenObject createScreen() {
    return new ScreenData();
  }

  @Override
  public boolean addScenario(VideoWallObject object) {
    if (object instanceof VideoWallData) {
      scenarios.add((VideoWallData) object);
      return true;
    } else {
      return false;
    }
  }

  @Override
  public boolean removeScenario(VideoWallObject object) {
    if (object instanceof VideoWallData) {
      scenarios.remove((VideoWallData) object);
      return true;
    } else {
      return false;
    }
  }

}
