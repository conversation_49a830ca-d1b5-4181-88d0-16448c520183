package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.canvas.PageCanvasUtility;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.geometry.Rectangle2D;
import lombok.Getter;


/**
 * 逻辑布局数据.
 */
public class LogicLayoutData implements IVideoWallLayout {

  @Getter
  @Expose
  private IntegerProperty totalWidthProperty = new SimpleIntegerProperty(1920);
  @Getter
  @Expose
  private IntegerProperty totalHeightProperty = new SimpleIntegerProperty(1080);
  @Getter
  @Expose
  private IntegerProperty rowsProperty = new SimpleIntegerProperty(2);
  @Getter
  @Expose
  private IntegerProperty columnsProperty = new SimpleIntegerProperty(2);

  @Override
  public int getRows() {
    return rowsProperty.get();
  }

  @Override
  public int getColumns() {
    return columnsProperty.get();
  }

  @Override
  public Collection<Rectangle2D> getScreenAreas() {
    List<Integer> widths = getWidths();
    List<Integer> heights = getHeights();
    return PageCanvasUtility.createGridRectangles(widths, heights);
  }

  @Override
  public List<Integer> getWidths() {
    return getIntegers(totalWidthProperty.get(), columnsProperty.get());
  }

  @Override
  public List<Integer> getHeights() {
    return getIntegers(totalHeightProperty.get(), rowsProperty.get());
  }

  /**
   * 获取整体宽度.
   *
   * @return 宽度
   */
  @Override
  public int getTotalWidth() {
    return totalWidthProperty.get();
  }

  /**
   * 获取整体高度.
   *
   * @return 高度
   */
  @Override
  public int getTotalHeight() {
    return totalHeightProperty.get();
  }

  /**
   * 数值平均分段.
   *
   * @param total 总长度
   * @param size  要分成的总段数
   * @return 各个段的长度的列表
   */
  public static List<Integer> getIntegers(int total, int size) {
    int segment = (int) ((double) total / size + 0.5);
    List<Integer> list = new ArrayList<>();
    int left = total;
    for (int i = 0; i < size - 1; i++) {
      left -= segment;
      list.add(segment);
    }
    list.add(left);
    return list;
  }
}
