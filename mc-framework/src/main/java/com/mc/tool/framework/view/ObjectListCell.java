package com.mc.tool.framework.view;

import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import java.io.IOException;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Region;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ObjectListCell extends ListCell<Entity> {
  @FXML
  private Label nameText;
  @FXML
  private Region activeRegion;
  @FXML
  private Region backupRegion;
  private final ApplicationBase applicationBase;
  private Node root;

  /**
   * Constructor.
   *
   * @param applicationBase application base
   */
  public ObjectListCell(ApplicationBase applicationBase) {
    this.applicationBase = applicationBase;
    FXMLLoader loader = new FXMLLoader(getClass().getResource(
        "/com/mc/tool/framework/objectlist_cell.fxml"));
    loader.setController(this);
    try {
      root = loader.load();
      setGraphic(root);
    } catch (IOException exception) {
      log.warn("Fail to load objectlist_cell.fxml", exception);
    }
  }

  @FXML
  protected void onClose(MouseEvent event) {
    try {
      applicationBase.getEntityFactory().closeEntity(getItem());
    } catch (RuntimeException exception) {
      log.warn("Fail to close entity!", exception);
    }
    applicationBase.getEntityMananger().removeEntity(getItem());
    event.consume();
  }

  @Override
  protected void updateItem(Entity item, boolean empty) {
    super.updateItem(item, empty);
    activeRegion.backgroundProperty().unbind();
    backupRegion.backgroundProperty().unbind();
    setGraphic(null);
    if (!empty) {
      nameText.setText(item.getName());
      activeRegion.backgroundProperty().bind(item.getActiveBgProperty());
      backupRegion.backgroundProperty().bind(item.getBackupBgProperty());
      setGraphic(root);
    }
  }
}
