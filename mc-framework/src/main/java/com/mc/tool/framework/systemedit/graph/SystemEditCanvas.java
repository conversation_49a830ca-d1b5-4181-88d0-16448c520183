package com.mc.tool.framework.systemedit.graph;

import com.mc.graph.McGraphCanvas;
import java.util.ArrayList;
import java.util.List;
import javafx.beans.value.WritableValue;
import javafx.scene.Group;

/**
 * .
 */
public class SystemEditCanvas extends McGraphCanvas {
  @Override
  protected InnerCanvas createInnerCanvas() {
    SystemEditInnerCanvas canvas = new SystemEditInnerCanvas();
    canvas.bgColor.bind(bgColor);
    return canvas;
  }

  static class SystemEditInnerCanvas extends McGraphCanvas.InnerCanvas {
    @Override
    protected Group createZoomContent() {
      return new Group();
    }
  }

  static class NoCssGroup extends Group {
    @Override
    public List<String> impl_getAllParentStylesheets() {
      return new ArrayList<>();
    }

    @Override
    protected void impl_processCSS(WritableValue<Boolean> unused) {
    }
  }
}
