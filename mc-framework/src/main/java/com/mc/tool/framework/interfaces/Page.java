package com.mc.tool.framework.interfaces;

import javafx.beans.property.BooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public interface Page {
  /**
   * Get the title of the page.
   *
   * @return title string.
   */
  String getTitle();

  /**
   * Get the name of the page.
   *
   * @return name string.
   */
  String getName();

  /**
   * Get the content of the page.
   *
   * @return the pane which contains the content.
   */
  Pane getView();

  String getStyleClass();

  /**
   * Get the visibility property of the page.
   *
   * @return propery.
   */
  BooleanProperty getVisibleProperty();

  /**
   * Show the specified object in the page.
   *
   * @param object the object to be shown.
   */
  void showObject(Object object);

  default void close() {
  }

  /**
   * Will not execute on ui thread.
   */
  default void refresh() {
  }

  /**
   * will not extcute on uithread.
   */
  default void refreshOnShow() {
  }

  /**
   * entity使用状态修改.
   *
   * @param active 是否在使用
   */
  default void onEntityActiveChange(boolean active) {
  }
}
