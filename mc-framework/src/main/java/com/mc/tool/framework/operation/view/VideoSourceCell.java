package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.GridCell;

/**
 * .
 */
@Slf4j
public final class VideoSourceCell extends GridCell<VisualEditTerminal> implements Initializable {
  @FXML
  private Label videoName;
  @FXML
  private Label videoPic;

  private Node root = null;

  /**
   * Constructor.
   */
  public VideoSourceCell() {
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/operation/video_source_cell.fxml"));
    loader.setController(this);
    try {
      root = loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load video_source_cell.fxml!", exception);
    }
  }

  @Override
  protected void updateItem(VisualEditTerminal item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
    } else {
      setGraphic(root);
      videoName.textProperty().bind(item.nameProperty());
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    videoName.setText("[empty]");
  }

  @FXML
  protected void onDragDetected(MouseEvent event) {
    Dragboard dbDragboard = videoPic.startDragAndDrop(TransferMode.ANY);
    ClipboardContent content = new ClipboardContent();
    content.putString(getItem().getGuid());
    Image image = videoPic.getBackground().getImages().get(0).getImage();
    dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
    dbDragboard.setContent(content);
    event.consume();
  }

  @FXML
  protected void onDragDone(DragEvent event) {

  }
}
