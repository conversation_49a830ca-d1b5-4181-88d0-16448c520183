package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public abstract class VideoWallMenuBase extends MenuItem {
  protected VideoWallControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public VideoWallMenuBase(VideoWallControllable controllable) {
    this.controllable = controllable;
    setOnAction((event) -> onAction());
    setText(getMenuText());
    setDisable(controllable.getSelectedVideos().size() == 0);
  }

  protected abstract void onAction();

  protected abstract String getMenuText();
}
