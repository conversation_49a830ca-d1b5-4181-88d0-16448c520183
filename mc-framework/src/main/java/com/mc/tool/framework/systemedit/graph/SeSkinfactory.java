package com.mc.tool.framework.systemedit.graph;

import com.mc.graph.DefaultSkinFactory;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.view.GroupCellSkin;
import com.mc.tool.framework.systemedit.view.MatrixCellSkin;
import com.mc.tool.framework.systemedit.view.SystemEditLinkSkin;
import com.mc.tool.framework.systemedit.view.SystemEditNewLinkSkin;
import com.mc.tool.framework.systemedit.view.TerminalCellSkin;
import javafx.scene.Parent;
import javafx.scene.image.Image;

/**
 * .
 */
public class SeSkinfactory extends DefaultSkinFactory {

  @Override
  public CellSkin createCellSkin(CellObject cellObject, Parent parent, Parent container,
                                 SkinManager skinManager) {
    CellSkin skin = null;
    if (cellObject.getType().equals(SystemEditDefinition.MATRIX_CELL)) {
      skin = createMatrixCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.TERMINAL_CELL)) {
      skin = createTerminaCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.GROUP_CELL)) {
      skin = createGroupCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.PREVIEW_CELL)) {
      skin = createPreviewCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.SNAPSHOT_CELL)) {
      skin = createSnapshotCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.VIDEO_WALL_CELL)) {
      skin = createVideoWallCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.SEAT_CELL)) {
      skin = createSeatCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.AUDIO_GROUP_CELL)) {
      skin = createAudioGroupCellSkin(cellObject, parent, container, skinManager);
    }

    if (skin != null) {
      skin.getRegion().layoutXProperty().bindBidirectional(cellObject.getXProperty());
      skin.getRegion().layoutYProperty().bindBidirectional(cellObject.getYProperty());
      skin.getRegion().prefWidthProperty().bindBidirectional(cellObject.getWidthProperty());
      skin.getRegion().prefHeightProperty().bindBidirectional(cellObject.getHeightProperty());
      skinManager.setCellSkin(cellObject, skin);
    }
    return skin;
  }

  protected CellSkin createGroupCellSkin(CellObject cellObject, Parent parent, Parent container,
                                         SkinManager skinManager) {
    CellSkin skin;
    skin = new GroupCellSkin(cellObject, parent, container, skinManager);
    return skin;
  }

  protected CellSkin createAudioGroupCellSkin(CellObject cellObject, Parent parent, Parent container,
                                              SkinManager skinManager) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image icon = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/audio_group.png"));
    GroupCellSkin skin = new GroupCellSkin(cellObject, parent, container, skinManager);
    skin.setIcon(icon);
    return skin;
  }

  protected CellSkin createSeatCellSkin(CellObject cellObject, Parent parent, Parent container,
                                        SkinManager skinManager) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image icon = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/seat_group.png"));
    GroupCellSkin groupCellSkin = new GroupCellSkin(cellObject, parent, container, skinManager);
    groupCellSkin.setIcon(icon);
    return groupCellSkin;
  }

  protected CellSkin createVideoWallCellSkin(CellObject cellObject, Parent parent, Parent container,
                                             SkinManager skinManager) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image icon = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/video_wall_group.png"));
    GroupCellSkin groupCellSkin = new GroupCellSkin(cellObject, parent, container, skinManager);
    groupCellSkin.setIcon(icon);
    return groupCellSkin;
  }

  protected CellSkin createSnapshotCellSkin(CellObject cellObject, Parent parent, Parent container,
                                            SkinManager skinManager) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image icon = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/snapshot_group.png"));
    GroupCellSkin groupCellSkin = new GroupCellSkin(cellObject, parent, container, skinManager);
    groupCellSkin.setIcon(icon);
    return groupCellSkin;
  }

  protected CellSkin createPreviewCellSkin(CellObject cellObject, Parent parent, Parent container,
                                           SkinManager skinManager) {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image icon = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/preview_group.png"));
    GroupCellSkin groupCellSkin = new GroupCellSkin(cellObject, parent, container, skinManager);
    groupCellSkin.setIcon(icon);
    return groupCellSkin;
  }

  protected CellSkin createMatrixCellSkin(CellObject cellObject, Parent parent, Parent container,
                                          SkinManager skinManager) {
    CellSkin skin;
    skin = new MatrixCellSkin(cellObject, parent, container, skinManager);
    return skin;
  }

  protected CellSkin createTerminaCellSkin(CellObject cellObject, Parent parent, Parent container,
                                           SkinManager skinManager) {
    CellSkin skin;
    skin = new TerminalCellSkin(cellObject, parent, container, skinManager);
    return skin;
  }

  @Override
  public LinkSkin createLinkSkin(LinkObject link, Parent parent, Parent container,
                                 SkinManager skinManager) {
    LinkSkin skin = new SystemEditLinkSkin(link, parent, container, skinManager);
    skinManager.setLinkSkin(link, skin);
    return skin;
  }

  @Override
  public NewLinkSkin createNewLinkSkin(String type, Connector sender, Parent parent,
                                       Parent container, SkinManager skinManager) {
    return new SystemEditNewLinkSkin(sender, parent, container, skinManager);
  }

}
