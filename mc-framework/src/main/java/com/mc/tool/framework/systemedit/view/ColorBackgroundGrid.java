package com.mc.tool.framework.systemedit.view;

import com.mc.common.beans.SimpleObservable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.geometry.Insets;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.paint.Color;
import lombok.Getter;

/**
 * .
 */
public class ColorBackgroundGrid {
  private List<ObjectProperty<Color>> colors = new ArrayList<>();
  private static final Color BACK_COLOR = Color.web("#f2f2f2");

  private ChangeListener<Color> colorChangeListener;

  @Getter
  private SimpleObservable observable = new SimpleObservable();

  @Getter
  private IntegerProperty maxGridSize = new SimpleIntegerProperty(Integer.MAX_VALUE);

  /**
   * .
   */
  public ColorBackgroundGrid() {
    colorChangeListener = (obs, oldVal, newVal) -> observable.update();
    maxGridSize.addListener((obs, oldVal, newVal) -> {
      observable.update();
    });
  }

  public int getSize() {
    return colors.size();
  }

  /**
   * 获取颜色.
   *
   * @param index 索引
   * @return 颜色
   */
  public Color getColor(int index) {
    if (index < 0 || index >= colors.size()) {
      throw new IllegalArgumentException();
    }
    return colors.get(index).get();
  }

  /**
   * 获取颜色属性.
   *
   * @param index 索引
   * @return 颜色属性
   */
  public ObjectProperty<Color> getColorProperty(int index) {
    if (index < 0 || index >= colors.size()) {
      throw new IllegalArgumentException();
    }
    return colors.get(index);
  }

  /**
   * 设置颜色.
   *
   * @param index 索引
   * @param color 颜色
   */
  public void setColor(int index, Color color) {
    if (index < 0 || index >= colors.size()) {
      throw new IllegalArgumentException();
    }
    colors.get(index).set(color);
  }

  /**
   * 重置颜色的数量.
   *
   * @param size 数量
   */
  public void resize(int size) {
    // 删除监听
    for (ObjectProperty<Color> item : colors) {
      item.removeListener(colorChangeListener);
    }
    colors.clear();
    // 添加项
    for (int i = 0; i < size; i++) {
      ObjectProperty<Color> item = new SimpleObjectProperty<>();
      item.addListener(colorChangeListener);
      colors.add(item);
    }
  }

  /**
   * 根据当前的颜色信息生成BackgroundFill.
   *
   * @param top    Insets的top
   * @param right  Insets的right
   * @param bottom Insets的bottom
   * @param left   Insets的left
   * @param height 要显示的高度.
   * @return 生成的BackgroundFill集合
   */
  public Collection<BackgroundFill> createBackgroundFills(double top, double right, double bottom,
                                                          double left, int height) {
    List<BackgroundFill> fills = new ArrayList<>();
    fills.add(new BackgroundFill(BACK_COLOR, null, new Insets(top, right, bottom, left)));
    int count = Math.min(colors.size(), maxGridSize.get());
    if (height > 0 && count > 0) {
      double gap = 1.0;
      double singleHeight = (height - (count - 1) * gap) / count;
      double minY = 0;
      double maxY = -gap;
      for (int i = 0; i < count; i++) {
        Color color = getColor(i);
        if (color == null) {
          continue;
        }
        minY = Math.max(maxY + gap, 0);
        maxY = Math.min(minY + singleHeight, height);
        Insets insets = new Insets(top + minY, right, height - maxY + bottom, left);
        fills.add(new BackgroundFill(color, null, insets));
      }
    }
    return fills;
  }

  /**
   * 销毁.
   */
  public void destroy() {
    unbindAll();
    colors.clear();
  }

  /**
   * 解除所有color的绑定.
   */
  public void unbindAll() {
    for (ObjectProperty<Color> item : colors) {
      item.unbind();
    }
    maxGridSize.unbind();
  }
}
