package com.mc.tool.framework.office.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.office.datamodel.OfficeDataUtility;
import com.mc.tool.framework.office.graph.OfficeGraph;
import com.mc.tool.framework.office.menu.MenuDelete;
import com.mc.tool.framework.office.menu.MenuRotateGroup;
import com.mc.tool.framework.office.view.FunctionListCell;
import com.mc.tool.framework.systemedit.control.PreviewPopover;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import com.mc.tool.framework.utility.I18nUtility;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.collections.ListChangeListener;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Accordion;
import javafx.scene.control.Button;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.ListView;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.control.TitledPane;
import javafx.scene.control.ToggleButton;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;

/**
 * .
 */
public class OfficePageController implements Initializable, OfficeControllable {
  private static final int LIST_CELL_HEIGHT = 32;
  @FXML
  private VBox leftPanel;
  @FXML
  private VBox separatedPanel;
  @FXML
  private VBox graphContainer;
  @FXML
  private TabPane officesTabPanel;
  @FXML
  private VBox vboxTitledPane;
  @FXML
  protected Button zoominBtn;
  @FXML
  protected Button zoomoutBtn;
  @FXML
  protected ToggleButton previewButton;

  private TitledPane videoWallTitledPane = new TitledPane();

  private TitledPane seatTitledPane = new TitledPane();

  private ListView<VisualEditFunc> videoWallList = new ListView<>();
  private ListView<VisualEditFunc> seatList = new ListView<>();

  private Accordion accordion = new Accordion();

  protected OfficeGraph graph;

  protected ContextMenu menu = new ContextMenu();

  private VisualEditModel systemEditModel;

  private DeviceControllable deviceControllable = null;

  private WeakAdapter weakAdapter = new WeakAdapter();

  @Override
  public void initModel(VisualEditModel model) {
    systemEditModel = model;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    initImpl();
  }

  @Override
  public void createTestData() {
  }

  protected void initGraph() {
    graph = new OfficeGraph();
    graph.init();
    graph.getCanvas().setBgColor(Color.rgb(0xfc, 0xfc, 0xfc));
    graph.setCellDeletable(true);
    graphContainer.getChildren().clear();
    graphContainer.getChildren().add(graph.getCanvas().getNode());

    VBox.setVgrow(graph.getCanvas().getNode(), Priority.ALWAYS);
    // 初始化预览
    final PreviewPopover popOver = new PreviewPopover();
    popOver.setContentNode(graph.getOverviewCanvas());
    previewButton.selectedProperty().addListener(weakAdapter.wrap((observable, oldValue, newValue) -> {
      if (newValue) {
        popOver.show(previewButton);
      } else {
        popOver.hide();
      }
    }));

    popOver.setOnHidden((event) -> previewButton.setSelected(false));
    popOver.setOnShown((event) -> previewButton.setSelected(true));

    // 初始化坐席或视频墙右键菜单.
    graph.getCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_CLICKED, (event) -> {
      if (event.getButton().equals(MouseButton.SECONDARY) && event.getClickCount() == 1) {
        menu.getItems().clear();
        createSettingMenuItems(menu);
        menu.show(graph.getCanvas().getContainer(), event.getScreenX(), event.getScreenY());
      } else {
        menu.hide();
      }
    });

    // 删除cell时删除officedata
    graph.getGraphModel().getObservableCells()
        .addListener(weakAdapter.wrap((ListChangeListener<CellObject>) change -> {
          while (change.next()) {
            for (CellObject cell : change.getRemoved()) {
              if (cell.getBindedObject() != null
                  && cell.getBindedObject() instanceof OfficeData) {
                systemEditModel.getOfficeFunctions().remove((OfficeData) cell.getBindedObject());
              }
            }
          }
        }));

    // 增删删除office data时增删cell
    systemEditModel.getOfficeFunctions()
        .addListener(weakAdapter.wrap((ListChangeListener<OfficeData>) change -> {
          List<CellObject> removeList = new ArrayList<>();
          while (change.next()) {
            for (OfficeData officeData : change.getAddedSubList()) {
              paintCellObject(officeData);
            }
            for (OfficeData officeData : change.getRemoved()) {
              CellObject object = graph.getGraphModel().findCell(officeData);
              if (object != null) {
                removeList.add(object);
              }
            }
          }

          graph.removeCells(removeList.toArray(new CellObject[0]));
        }));

    // 初始化右键菜单
    graph.getCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_CLICKED, (event) -> {
      if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
        menu.getItems().clear();
        createContextMenuItems(menu);
        menu.show(graph.getCanvas().getContainer(), event.getScreenX(), event.getScreenY());
      } else {
        menu.hide();
      }
    });
    graph.getCanvas().getContainer().setOnDragOver((event) -> {
      boolean editable = getDeviceController() == null
          || getDeviceController().getUserRight().isSystemScenarioEditable();
      if (event.getDragboard().hasString() && editable) {
        event.acceptTransferModes(TransferMode.ANY);
      }
      event.consume();
    });

    graph.getCanvas().getContainer().setOnDragDropped((event) -> {
      String guid = event.getDragboard().getString();
      VisualEditNode node = systemEditModel.findNodeByGuid(guid);
      if (node instanceof VisualEditFunc) {
        addOfficeGraphCell((VisualEditFunc) node, event.getX(), event.getY());
      }
      event.setDropCompleted(true);
      event.consume();
    });

  }

  protected void createContextMenuItems(ContextMenu menu) {
    if (getSelectedFuncs().size() == 0) {
      return;
    }

    if (deviceControllable == null
        || deviceControllable.getUserRight().isSystemScenarioEditable()) {
      menu.getItems().add(new MenuRotateGroup(this));
    }
  }


  protected void initImpl() {
    initGraph();

    for (OfficeData officeData : systemEditModel.getOfficeFunctions()) {
      paintCellObject(officeData);
    }


    // 初始化 TitledPane
    videoWallTitledPane.setAnimated(false); // 停止动画效果
    videoWallTitledPane.setContent(videoWallList);

    seatTitledPane.setAnimated(false); // 停止动画效果
    seatTitledPane.setContent(seatList);

    videoWallList.setCellFactory((view) -> new FunctionListCell());
    seatList.setCellFactory((view) -> new FunctionListCell());
    videoWallList.setItems(systemEditModel.getAllOnlineFuncs()
        .filtered((func) -> func.getNodeType().equals(SystemEditDefinition.VIDEO_WALL_CELL)));
    seatList.setItems(systemEditModel.getAllOnlineFuncs()
        .filtered((func) -> func.getNodeType().equals(SystemEditDefinition.SEAT_CELL)));


    videoWallList.prefHeightProperty()
        .bind(Bindings.size(videoWallList.getItems()).multiply(LIST_CELL_HEIGHT));
    seatList.prefHeightProperty()
        .bind(Bindings.size(seatList.getItems()).multiply(LIST_CELL_HEIGHT));


    videoWallTitledPane
        .setText(I18nUtility.getI18nBundle("office").getString("titledPane.video_wall"));
    seatTitledPane.setText(I18nUtility.getI18nBundle("office").getString("titledPane.seats"));

    accordion.getPanes().addAll(videoWallTitledPane, seatTitledPane); // , otherTitledPane);
    vboxTitledPane.getChildren().add(accordion);

    // 初始化Tab子项
    if (officesTabPanel != null) {
      addTab(I18nUtility.getI18nBundle("office").getString("tab.office"));
    }

    zoominBtn.disableProperty().bind(
        graph.getCanvas().getScaleProperty().greaterThanOrEqualTo(SystemeditConstants.MAX_SCALE));
    zoomoutBtn.disableProperty().bind(
        graph.getCanvas().getScaleProperty().lessThanOrEqualTo(SystemeditConstants.MIN_SCALE));
  }

  protected void addOfficeGraphCell(VisualEditFunc func, double xpos, double ypos) {

    OfficeData officeData = new OfficeData();
    officeData.setType(func.getCellObject().getType());
    officeData.setName(func.nameProperty());
    officeData.setFunc(func);
    double cellHeight = 100;
    double cellWidth = 100;
    double cellRatoteHeight = 30;

    if ((func.getCellObject() == null
        || !graph.getGraphModel().hasCell(graph.getGraphModel().findCell(officeData)))
        && (func.getCellObject().getType().equals(SystemEditDefinition.VIDEO_WALL_CELL)
        || func.getCellObject().getType().equals(SystemEditDefinition.SEAT_CELL))
        && !isOfficeDataExisted(func.getGuid())) {

      officeData.getWidth().set(cellWidth);
      officeData.getHeight().set(cellHeight);
      officeData.getXpos().set(xpos - cellWidth / 2);
      officeData.getYpos().set(ypos - cellRatoteHeight - (cellHeight - cellRatoteHeight) / 2);
      systemEditModel.addOfficeFunction(officeData);
    }
  }

  /**
   * 更新单个视频墙或坐席的显示.
   */
  protected void updateOfficeGraphCell(OfficeData officeData) {

    VisualEditFunc root = officeData.getFunc();
    if ((root.getCellObject() == null
        || !graph.getGraphModel().hasCell(graph.getGraphModel().findCell(officeData)))
        && (root.getNodeType().equals(SystemEditDefinition.VIDEO_WALL_CELL)
        || root.getNodeType().equals(SystemEditDefinition.SEAT_CELL)
        && root.getGuid().equals(officeData.getFunc().getGuid()))) {

      paintCellObject(officeData);
    }

  }

  /**
   * 绘画视频墙或坐席UI.
   */
  public CellObject paintCellObject(OfficeData officeData) {

    CellObject cellObject = graph.insertCell("0", "0", officeData.getXpos().get(),
        officeData.getYpos().get(), officeData.getWidth().get(), officeData.getHeight().get(),
        officeData.angleProperty().get(), officeData.getFunc().getNodeType(), officeData);

    officeData.getXpos().bindBidirectional(cellObject.getXProperty());
    officeData.getYpos().bindBidirectional(cellObject.getYProperty());
    officeData.angleProperty().bindBidirectional(cellObject.getAngleProperty());
    return cellObject;
  }

  @Override
  public OfficeGraph getGraph() {
    return graph;
  }

  @Override
  public void addTab(String tabName) {
    if (officesTabPanel == null) {
      return;
    }

    Tab tab = new Tab();
    tab.setText(tabName);
    tab.closableProperty().set(true);
    officesTabPanel.getTabs().add(tab);
  }

  /**
   * 判断当前工作区域数据是否已经存在了.
   */
  public boolean isOfficeDataExisted(String officeDataId) {
    boolean rv = false;
    if (!systemEditModel.getOfficeFunctions().isEmpty()) {
      for (OfficeData officeData : systemEditModel.getOfficeFunctions()) {
        if (officeData.getFunc().getGuid().equals(officeDataId)) {
          rv = true;
          break;
        }
      }
    }

    return rv;
  }

  /**
   * 创建Cell的右击设置菜单按钮.
   */
  protected void createSettingMenuItems(ContextMenu menu) {
    // menu.getItems().add(new MenuSetting(this));
    menu.getItems().add(new MenuDelete(this));
  }

  /**
   * 设置左侧TitledPane是否显示.
   */
  public void setOperationAble(boolean canOperation) {

    leftPanel.setVisible(canOperation); // 设置左侧面板是否显示
    leftPanel.setManaged(canOperation);
    separatedPanel.setVisible(canOperation); // 设置左侧面板是否显示
    separatedPanel.setManaged(canOperation);
    graph.setCellMovable(canOperation);

  }

  @Override
  public Collection<OfficeData> getSelectedFuncs() {
    List<OfficeData> result = new ArrayList<>();
    for (SelectableNode node : graph.getSelectionModel().getSelectedItems()) {
      OfficeData officeData = OfficeDataUtility.selectedNode2OfficeData(node);
      if (officeData != null) {
        result.add(officeData);
      }
    }
    return result;
  }

  @Override
  public void deleteSelectedFuncs() {
    Collection<OfficeData> funcs = getSelectedFuncs();
    if (!funcs.isEmpty()) {

      for (OfficeData officeData : funcs) {
        // 删除图形
        CellObject cellObject = graph.getGraphModel().findCell(officeData);
        if (cellObject != null && graph.getGraphModel().hasCell(cellObject)) {
          graph.removeCells(cellObject);
        }
        // 删除数据
        systemEditModel.getOfficeFunctions().remove(officeData);
      }
    }
  }

  @Override
  public void deleteAll() {
    systemEditModel.getOfficeFunctions().clear();
  }

  @FXML
  protected void onZoomin(ActionEvent event) {
    double scale = graph.getCanvas().getScaleProperty().get();
    scale = scale * SystemeditConstants.SCALE_FACTOR;
    if (scale > SystemeditConstants.MAX_SCALE) {
      scale = SystemeditConstants.MAX_SCALE;
    }
    graph.getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onZoomout(ActionEvent event) {
    double scale = graph.getCanvas().getScaleProperty().get();
    scale = scale / SystemeditConstants.SCALE_FACTOR;
    if (scale < SystemeditConstants.MIN_SCALE) {
      scale = SystemeditConstants.MIN_SCALE;
    }
    graph.getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onRestore(ActionEvent event) {
    graph.getCanvas().getScaleProperty().set(1);
    graph.getCanvas().fitToView();
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    this.deviceControllable = deviceController;
    if (deviceController != null) {
      boolean editable = deviceController.getUserRight().isSystemScenarioEditable();
      graph.setCellDeletable(editable);
      graph.setCellMovable(editable);
      graph.setCellResizable(editable);
      graph.setCellRotatable(editable);

      for (CellSkin skin : graph.getSkinManager().getAllCellSkin()) {
        skin.rotatableProperty().set(false);
      }
    }
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceControllable;
  }


}
