package com.mc.tool.framework.beans;

import java.util.Locale;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import lombok.Data;

/**
 * .
 */
public class ConfigBean {
  public ObjectProperty<Locale> language = new SimpleObjectProperty<>(Locale.CHINA);
  public final AuthInfo authInfo = new AuthInfo();

  /**
   * Auth info.
   */
  @Data
  public static class AuthInfo {
    private String ip = "";
    private String user = "";
    private String pwd = "";
    private boolean remember;

    public String getIp() {
      return ip;
    }

    public String getUser() {
      return user;
    }

    public String getPwd() {
      return pwd;
    }
  }

  /**
   * set auth info.
   */
  public void setAuthInfo(String ip, String user, String pwd, boolean remember) {
    authInfo.ip = ip;
    authInfo.user = user;
    authInfo.pwd = pwd;
    authInfo.remember = remember;
  }
}
