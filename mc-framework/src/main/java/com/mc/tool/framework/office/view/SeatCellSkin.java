package com.mc.tool.framework.office.view;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SeatCellSkin extends RatoteCellSkin {

  /**
   * Contructor.
   *
   * @param cellobject  cell object
   * @param parent      parent
   * @param container   container
   * @param skinManager skin manager
   */
  public SeatCellSkin(CellObject cellobject, Parent parent, Parent container,
                      SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  @Override
  protected void initRegion() {

    try {
      FXMLLoader loader = new FXMLLoader(
          getClass().getResource("/com/mc/tool/framework/office/seat_cell_skin.fxml"));
      loader.setController(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load office/seat_cell_skin.fxml", exc);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    super.initialize(location, resources);
    updateBackground();
    cellObject.getBindedObjectProperty().addListener((observable, oldValue, newValue) -> updateBackground());
  }

  protected void updateBackground() {
    CellBindedObject object = cellObject.getBindedObject();
    if (object instanceof OfficeData) {
      OfficeData data = (OfficeData) object;
      Image seatImage = new Image(
          getClass().getResourceAsStream("/com/mc/tool/framework/office/office_seat.png"));
      Image crossScreenImage = new Image(
          getClass().getResourceAsStream("/com/mc/tool/framework/office/office_cross_screen.png"));
      Background seatBackground = new Background(new BackgroundImage(seatImage,
          BackgroundRepeat.REPEAT, BackgroundRepeat.REPEAT, BackgroundPosition.CENTER, null));
      Background crossScreenBackground = new Background(new BackgroundImage(crossScreenImage,
          BackgroundRepeat.REPEAT, BackgroundRepeat.REPEAT, BackgroundPosition.CENTER, null));
      if (data.getFunc() instanceof CrossScreenFunc) {
        image.setBackground(crossScreenBackground);
      } else {
        image.setBackground(seatBackground);
      }
    }
  }


}
