package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class VisualEditFunc extends VisualEditGroup {

  /**
   * .
   */
  public enum PublishMode {
    AUTO, MANUAL;
  }

  @Expose
  protected ObjectProperty<PublishMode> publishMode = new SimpleObjectProperty<>(PublishMode.AUTO);

  /**
   * 获取发布模式属性.
   *
   * @return 发布模式属性
   */
  public ObjectProperty<PublishMode> getPublishModeProperty() {
    if (publishMode == null) {
      publishMode = new SimpleObjectProperty<>(PublishMode.AUTO);
    }
    return publishMode;
  }

  public void setPublishMode(PublishMode mode) {
    getPublishModeProperty().set(mode);
  }

  public PublishMode getPublishMode() {
    return getPublishModeProperty().get();
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    ObservableList<VisualEditFunc> list = FXCollections.observableArrayList();
    list.add(this);
    return list;
  }

  /**
   * 是否可操作.
   *
   * @return 如果可操作，返回true
   */
  public boolean isOperatable() {
    return true;
  }
}
