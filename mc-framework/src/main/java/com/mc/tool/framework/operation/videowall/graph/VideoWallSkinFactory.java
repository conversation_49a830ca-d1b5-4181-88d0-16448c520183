package com.mc.tool.framework.operation.videowall.graph;

import com.mc.common.beans.FactorBidirectionalBinding;
import com.mc.common.beans.FactorBidirectionalBinding.FactorType;
import com.mc.graph.DefaultSkinFactory;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import javafx.beans.property.DoubleProperty;
import javafx.scene.Parent;

/**
 * .
 */
public class VideoWallSkinFactory extends DefaultSkinFactory {
  private final SnapshotGetter snapshotGetter;

  public VideoWallSkinFactory(SnapshotGetter snapshotGetter) {
    this.snapshotGetter = snapshotGetter;
  }

  @Override
  public CellSkin createCellSkin(CellObject cellObject, Parent parent, Parent container,
                                 SkinManager skinManager) {
    VideoCellSkin skin = new VideoCellSkin(cellObject, parent, container, skinManager);
    skin.setSnapshotGetter(snapshotGetter);
    skin.getRegion().layoutXProperty().bindBidirectional(cellObject.getXProperty());
    skin.getRegion().layoutYProperty().bindBidirectional(cellObject.getYProperty());
    skin.getRegion().prefWidthProperty().bindBidirectional(cellObject.getWidthProperty());
    skin.getRegion().prefHeightProperty().bindBidirectional(cellObject.getHeightProperty());

    skin.addCleaner(() -> {
      skin.getRegion().layoutXProperty().unbindBidirectional(cellObject.getXProperty());
      skin.getRegion().layoutYProperty().unbindBidirectional(cellObject.getYProperty());
      skin.getRegion().prefWidthProperty().unbindBidirectional(cellObject.getWidthProperty());
      skin.getRegion().prefHeightProperty().unbindBidirectional(cellObject.getHeightProperty());
    });
    skinManager.setCellSkin(cellObject, skin);
    return skin;
  }

  @Override
  public CellSkin createScaleCellSkin(CellObject cellObject, Parent parent, Parent container,
                                      SkinManager skinManager, DoubleProperty cellScaleProperty) {
    VideoCellSkin skin = new VideoCellSkin(cellObject, parent, container, skinManager);
    skin.setSnapshotGetter(snapshotGetter);
    FactorBidirectionalBinding.bind(skin.getRegion().layoutXProperty(),
        cellObject.getXProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().layoutYProperty(),
        cellObject.getYProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().prefWidthProperty(),
        cellObject.getWidthProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().prefHeightProperty(),
        cellObject.getHeightProperty(), cellScaleProperty, FactorType.MULTIPLY);

    skin.addCleaner(() -> {
      FactorBidirectionalBinding.unbind(skin.getRegion().layoutXProperty(),
          cellObject.getXProperty(), cellScaleProperty);
      FactorBidirectionalBinding.unbind(skin.getRegion().layoutYProperty(),
          cellObject.getYProperty(), cellScaleProperty);
      FactorBidirectionalBinding.unbind(skin.getRegion().prefWidthProperty(),
          cellObject.getWidthProperty(), cellScaleProperty);
      FactorBidirectionalBinding.unbind(skin.getRegion().prefHeightProperty(),
          cellObject.getHeightProperty(), cellScaleProperty);
    });
    skinManager.setCellSkin(cellObject, skin);
    return skin;
  }
}
