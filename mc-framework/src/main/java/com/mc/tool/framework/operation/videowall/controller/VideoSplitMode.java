package com.mc.tool.framework.operation.videowall.controller;

import lombok.Getter;

/**
 * .
 */
public enum VideoSplitMode {
  SPLIT_2_2(2, 2),
  SPLIT_3_3(3, 3),
  SPLIT_4_4(4, 4),
  SPLIT_4_8(4, 8),
  SPLIT_OTHER(2, 2),
  SPLIT_BY_SCREEN("screen");

  @Getter
  private int minWidth;
  @Getter
  private int minHeight;
  @Getter
  private int rows;
  @Getter
  private int columns;
  @Getter
  private String text;

  VideoSplitMode(int rows, int columns) {
    this.rows = rows;
    this.columns = columns;
    minWidth = columns * 100;
    minHeight = rows * 100;
    text = String.format("%d * %d", rows, columns);
  }

  VideoSplitMode(String text) {
    this.rows = 0;
    this.columns = 0;
    this.minWidth = 0;
    this.minHeight = 0;
    this.text = text;
  }

}