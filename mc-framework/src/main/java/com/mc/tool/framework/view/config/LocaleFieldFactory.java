package com.mc.tool.framework.view.config;

import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.FXFormNodeWrapper;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Locale;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.ComboBox;
import javafx.util.Callback;
import javafx.util.StringConverter;

/**
 * .
 */
public class LocaleFieldFactory implements Callback<Void, FXFormNode> {

  @Override
  public FXFormNode call(Void param) {
    ComboBox<Locale> comboBox = new ComboBox<>();
    ObservableList<Locale> supportedLocale = FXCollections.observableArrayList();
    supportedLocale.add(Locale.ENGLISH);
    supportedLocale.add(Locale.SIMPLIFIED_CHINESE);
    comboBox.setItems(supportedLocale);
    comboBox.setConverter(new LocaleStringConverter());
    return new FXFormNodeWrapper(comboBox, comboBox.valueProperty());
  }

  static class LocaleStringConverter extends StringConverter<Locale> {
    private static String ENGLISH_STR =
        I18nUtility.getI18nBundle("main").getString("framework.menu.config.basic.i18n.en");
    private static String CHINESE_STR =
        I18nUtility.getI18nBundle("main").getString("framework.menu.config.basic.i18n.ch");
    private static String UNKNOWN_STR = "UNKNOWN";

    @Override
    public String toString(Locale object) {
      if (object.equals(Locale.ENGLISH)) {
        return ENGLISH_STR;
      } else if (object.equals(Locale.SIMPLIFIED_CHINESE)) {
        return CHINESE_STR;
      } else {
        return UNKNOWN_STR;
      }
    }

    @Override
    public Locale fromString(String string) {
      if (string.equals(ENGLISH_STR)) {
        return Locale.ENGLISH;
      } else if (string.equals(CHINESE_STR)) {
        return Locale.SIMPLIFIED_CHINESE;
      } else {
        return Locale.getDefault();
      }
    }

  }

}
