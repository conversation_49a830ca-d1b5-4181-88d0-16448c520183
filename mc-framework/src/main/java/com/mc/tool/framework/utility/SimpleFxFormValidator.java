package com.mc.tool.framework.utility;

import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.reflection.MultipleBeanSource;
import com.dooapp.fxform.validation.FXFormValidator;
import com.dooapp.fxform.validation.Warning;
import java.lang.annotation.ElementType;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.validation.ConstraintViolation;
import javax.validation.MessageInterpolator;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import javax.validation.groups.Default;
import javax.validation.metadata.BeanDescriptor;
import javax.validation.metadata.ConstraintDescriptor;

/**
 * .
 */
public class SimpleFxFormValidator implements FXFormValidator {

  private final Logger logger = Logger.getLogger(SimpleFxFormValidator.class.getName());

  protected Validator validator;

  protected MessageInterpolator messageInterpolator;

  protected boolean init = false;

  /**
   * Initialize the constraint validator. Might be null after that if no implementation has been
   * provided.
   */
  protected void init() {
    if (init) {
      return;
    }
    try {
      ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
      validator = factory.getValidator();
      messageInterpolator = factory.getMessageInterpolator();
    } catch (ValidationException exception) {
      // validation is not activated, since no implementation has been provided
      logger.log(Level.INFO, "Validation disabled", exception);
    }
    init = true;
  }

  public SimpleFxFormValidator() {
  }

  @Override
  public List<ConstraintViolation> validate(Element element, Object newValue, Class... groups) {
    init();
    final List<ConstraintViolation> list = new LinkedList<ConstraintViolation>();
    if (validator != null) {
      list.addAll(
          validator.validateValue((Class<Object>) (element.sourceProperty().getValue().getClass()),
              element.getName(), newValue, groups));
    }
    return list;
  }

  @Override
  public List<ConstraintViolation> validateClassConstraint(Object bean) {
    init();
    final List<ConstraintViolation> list = new LinkedList<ConstraintViolation>();
    if (validator != null && bean != null) {
      if (bean instanceof MultipleBeanSource) {
        for (Object object : ((MultipleBeanSource) bean).getSources()) {
          list.addAll(getConstraintViolation(object));
        }
      } else {
        list.addAll(getConstraintViolation(bean));
      }
    }
    return list;
  }

  private List<ConstraintViolation> getConstraintViolation(Object bean) {
    init();
    final List<ConstraintViolation> list = new LinkedList<ConstraintViolation>();
    BeanDescriptor beanDescriptor = validator.getConstraintsForClass(bean.getClass());
    Set<ConstraintDescriptor<?>> classLevelConstraints =
        beanDescriptor.findConstraints().declaredOn(ElementType.TYPE).getConstraintDescriptors();
    Set<ConstraintViolation<Object>> constraintViolations =
        validator.validate(bean, Default.class, Warning.class);
    for (ConstraintViolation constraintViolation : constraintViolations) {
      if (classLevelConstraints.contains(constraintViolation.getConstraintDescriptor())) {
        list.add(constraintViolation);
      }
    }
    return list;
  }

  @Override
  public MessageInterpolator getMessageInterpolator() {
    init();
    return messageInterpolator;
  }
}
