package com.mc.tool.framework.view;

import com.mc.tool.framework.controller.ObjectListController;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ObjectListView extends VBox {
  private ObjectListController controller;

  /**
   * Create an ObjectListView.
   */
  public ObjectListView() {
    FXMLLoader loader = new FXMLLoader(getClass().getResource(
        "/com/mc/tool/framework/objectlist.fxml"));
    loader.setRoot(this);
    loader.setControllerFactory(clazz -> InjectorProvider.getInjector().getInstance(clazz));
    try {
      loader.load();
      controller = loader.getController();
    } catch (IOException exp) {
      log.warn("Fail to load objectlist.fxml!", exp);
    }
  }

  public ObjectListController getController() {
    return controller;
  }
}
