package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.ConnectorIdentifier;
import java.util.ArrayList;
import java.util.List;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;

/**
 * .
 */
public class VisualEditGroup extends AbstractVisualEditNode {
  public static final ConnectorIdentifier GROUP_PARENT_CONNECTOR =
      ConnectorIdentifier.getIdentifier("Group.Parent.Connector");
  public static final ConnectorIdentifier GROUP_CHILD_CONNECTOR =
      ConnectorIdentifier.getIdentifier("Group.Child.Connector");

  protected ObservableList<VisualEditTerminal> allTerminals = FXCollections.observableArrayList();

  protected ObservableList<VisualEditTerminal> readOnlyAllTerminals;

  protected ObservableList<VisualEditFunc> allFunctions = FXCollections.observableArrayList();

  protected ObservableList<VisualEditFunc> readOnlyAllFunctions;

  protected ListChangeListener<VisualEditNode> childrenChangeListener;
  protected ListChangeListener<VisualEditTerminal> childTerminalChangeListener;
  protected ListChangeListener<VisualEditFunc> childFunctionChangeListener;

  protected ObservableList<ConnectorIdentifier> connectorIds =
      FXCollections.observableArrayList(GROUP_PARENT_CONNECTOR, GROUP_CHILD_CONNECTOR);

  /**
   * Constructor.
   */
  public VisualEditGroup() {
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.GROUP_CELL;
  }

  @Override
  public int getPortCount() {
    return 0;
  }

  /**
   * Check whether this group contains rx terminals.
   */
  @Override
  public boolean isRx() {
    if (isEmpty()) {
      return false;
    } else {
      VisualEditNode node = getChildren().iterator().next();
      return node.isRx();
    }
  }

  /**
   * Check whether this group contains tx terminals.
   */
  @Override
  public boolean isTx() {
    if (isEmpty()) {
      return false;
    }

    VisualEditNode node = getChildren().iterator().next();
    return node.isTx();
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectorIds;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    return readOnlyAllTerminals;
  }

  protected void updateAllTerminalChild() {
    List<VisualEditTerminal> result = new ArrayList<>();
    for (VisualEditNode node : getChildren()) {
      result.addAll(node.getAllTerminalChild());
    }

    allTerminals.setAll(result);
  }

  protected void updateAllFunctionChild() {
    List<VisualEditFunc> result = new ArrayList<>();
    for (VisualEditNode node : getChildren()) {
      result.addAll(node.getAllFunctions());
    }
    //添加与删除分开，避免场景界面出现误删的情况
    List<VisualEditFunc> addList = new ArrayList<>(result);
    addList.removeAll(allFunctions);

    List<VisualEditFunc> removeList = new ArrayList<>(allFunctions);
    removeList.removeAll(result);

    allFunctions.removeAll(removeList);
    allFunctions.addAll(addList);
  }

  @Override
  public void init() {
    readOnlyAllTerminals = FXCollections.unmodifiableObservableList(allTerminals);
    readOnlyAllFunctions = FXCollections.unmodifiableObservableList(allFunctions);

    childTerminalChangeListener = (change) -> updateAllTerminalChild();
    childFunctionChangeListener = (change) -> updateAllFunctionChild();

    for (VisualEditNode node : getChildren()) {
      node.getAllTerminalChild().addListener(childTerminalChangeListener);
      node.getAllFunctions().addListener(childFunctionChangeListener);
    }
    childrenChangeListener = change -> {
      updateAllTerminalChild();
      updateAllFunctionChild();
      while (change.next()) {
        for (VisualEditNode node : change.getAddedSubList()) {
          node.getAllTerminalChild().addListener(childTerminalChangeListener);
          node.getAllFunctions().addListener(childFunctionChangeListener);
        }
        for (VisualEditNode node : change.getRemoved()) {
          node.getAllTerminalChild().removeListener(childTerminalChangeListener);
          node.getAllFunctions().addListener(childFunctionChangeListener);
        }
      }
    };

    children.addListener(childrenChangeListener);

    addNameProperty();

    updateAllTerminalChild();
    updateAllFunctionChild();
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return readOnlyAllFunctions;
  }
}
