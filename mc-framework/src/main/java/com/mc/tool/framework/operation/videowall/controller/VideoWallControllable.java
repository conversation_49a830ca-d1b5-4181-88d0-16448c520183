package com.mc.tool.framework.operation.videowall.controller;

import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import java.util.Collection;
import javafx.beans.property.ObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import org.controlsfx.control.PropertySheet;

/**
 * .
 */
public interface VideoWallControllable extends OperationControllable {
  void init(VisualEditModel model, VisualEditFunc currentFunction);

  /**
   * 获取选中的视频窗口.
   *
   * @return 选中的视频窗口
   */
  Collection<VideoObject> getSelectedVideos();

  VideoWallFunc getVideoWallFunction();

  /**
   * 激活预案.
   *
   * @param scenario 预案数据.
   */
  void activeScenario(VideoWallObject scenario);

  /**
   * 是否有此预案.
   *
   * @param scenario 预案
   * @return 如果有，返回true
   */
  boolean hasScenario(VideoWallObject scenario);

  /**
   * 删除预案.
   *
   * @param scenario 预案
   */
  void deleteScenario(VideoWallObject scenario);

  ObjectProperty<VideoWallObject> currentScenarioProperty();

  /**
   * 开始更新视频墙数据，必须跟endUpdate配合使用.
   */
  void beginUpdate();

  /**
   * 结束更新视频数据，如果在更新期间确实发生了数据改变，那么发送视频墙数据.
   */
  void endUpdate();

  /**
   * 分割视频窗口.
   *
   * @param videoData 要分割的窗口
   * @param mode      分割模式
   */
  void splitVideo(VideoObject videoData, VideoSplitMode mode);

  void moveToUp(VideoObject videoData);

  void moveToDown(VideoObject videoData);

  void moveToTop(VideoObject videoData);

  void moveToBottom(VideoObject videoData);

  default void switchVideoMode() {
  }

  default void switchScreenMode() {
  }

  default void publish() {
  }

  void setNodeSelectionMode();

  void setAreaSelectionMode();

  default ObservableList<PropertySheet.Item> getWindowsProperties() {
    return FXCollections.observableArrayList();
  }

  default ObservableList<PropertySheet.Item> getLayoutPropperties() {
    return FXCollections.observableArrayList();
  }

  default ObservableList<PropertySheet.Item> getVideoLayoutPropperties() {
    return FXCollections.observableArrayList();
  }

}
