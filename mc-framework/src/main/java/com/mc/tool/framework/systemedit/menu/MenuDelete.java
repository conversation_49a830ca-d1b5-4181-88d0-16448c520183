package com.mc.tool.framework.systemedit.menu;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;
import javafx.beans.binding.BooleanBinding;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuDelete extends MenuItem {
  protected SystemEditControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuDelete(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.disableProperty().bind(new DisableBinding(controllable));
    this.setOnAction((event) -> {
      onAction();
    });
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.delete"));
  }

  protected void onAction() {
    controllable.deleteNodes(controllable.getSelectedNodes().toArray(new VisualEditNode[0]));
  }

  static class DisableBinding extends BooleanBinding {
    private SystemEditControllable controllable;

    public DisableBinding(SystemEditControllable controllable) {
      this.controllable = controllable;
      this.bind(controllable.getGraph().getSelectionModel().getSelectedItems());
    }

    @Override
    protected boolean computeValue() {
      Collection<VisualEditNode> nodes = controllable.getSelectedNodes();
      if (nodes.size() == 0) {
        return true;
      } else {
        // 如果其中一个不能删除，就disable
        for (VisualEditNode node : nodes) {
          if (!controllable.isDeletable(node)) {
            return true;
          }
        }
        return false;
      }
    }

  }
}
