package com.mc.tool.framework.operation.crossscreen.datamodel;

import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ObservableMap;

/**
 * .
 */
public interface CrossScreenObject {

  StringProperty getName();

  IntegerProperty getRows();

  IntegerProperty getColumns();

  ObjectProperty<VisualEditTerminal> getControlSource();

  Collection<ObjectProperty<VisualEditTerminal>> getTargets();

  ObjectProperty<VisualEditTerminal> getTarget(int index);

  ObservableMap<VisualEditTerminal, SeatConnection> getConnections();

  int indexOfTarget(VisualEditTerminal target);
}
