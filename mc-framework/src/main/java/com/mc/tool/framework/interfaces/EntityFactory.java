package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.utility.TypeWrapper;
import java.io.File;
import java.util.Collection;
import java.util.function.Consumer;

/**
 * .
 */
public interface EntityFactory {
  void createEntity(String type, Consumer<Entity> postAction);

  void createEntity(File file, Consumer<Entity> postAction);

  Entity searchEntity(Collection<String> types);

  boolean closeEntity(Entity entity);

  /**
   * Check current application if support given entity type.
   *
   * @param type name of entity type
   * @return boolean value of supportable
   */
  boolean isSupportEntity(String type);

  Collection<TypeWrapper> getSupportEntityTypes();
}
