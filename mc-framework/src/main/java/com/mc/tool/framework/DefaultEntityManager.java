package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.EntityManager;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import com.mc.tool.framework.utility.EntityUtility;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultEntityManager implements EntityManager {
  private ObservableList<Entity> deviceList = FXCollections.observableArrayList();
  private ObservableList<Entity> monitorList = FXCollections.observableArrayList();
  private AggregatedObservableArrayList<Entity> aggregatedEntityList =
      new AggregatedObservableArrayList<>("aggregatedEntityList");
  private ObservableList<Entity> allList = aggregatedEntityList.getAggregatedList();

  public DefaultEntityManager() {
    aggregatedEntityList.appendList(deviceList);
    aggregatedEntityList.appendList(monitorList);
  }

  @Override
  public ObservableList<Entity> getEntityList(String type) {
    if (EntityUtility.isDeviceType(type)) {
      return deviceList;
    } else if (EntityUtility.isMonitorType(type)) {
      return monitorList;
    } else {
      return null;
    }
  }

  @Override
  public void addEntity(Entity... entities) {
    for (Entity entity : entities) {
      if (EntityUtility.isDeviceType(entity.getType())) {
        deviceList.add(entity);
      } else if (EntityUtility.isMonitorType(entity.getType())) {
        monitorList.add(entity);
      } else {
        log.warn("Unknown entity type : {}", entity.getType());
      }
    }
  }

  @Override
  public void removeEntity(Entity... entities) {
    for (Entity entity : entities) {
      if (EntityUtility.isDeviceType(entity.getType())) {
        deviceList.remove(entity);
      } else if (EntityUtility.isMonitorType(entity.getType())) {
        monitorList.remove(entity);
      } else {
        log.warn("Unknown entity type : {}", entity.getType());
      }
    }
  }

  @Override
  public ObservableList<Entity> getAllEntity() {
    return allList;
  }

}
