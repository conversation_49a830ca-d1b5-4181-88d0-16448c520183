package com.mc.tool.framework.operation.videowall.datamodel.interfaces;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public interface VideoObject extends CellBindedObject {
  StringProperty getName();

  ObjectProperty<VisualEditTerminal> getSource();

  ReadOnlyStringProperty getNameWithSource();

  IntegerProperty getXpos();

  IntegerProperty getYpos();

  IntegerProperty getWidth();

  IntegerProperty getHeight();

  DoubleProperty getAlpha();

  void copyTo(VideoObject video);
}
