package com.mc.tool.framework.office.menu.predicate;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.Collection;
import java.util.function.Predicate;

/**
 * .
 */
public class ParentNotSamePredicate implements Predicate<Collection<VisualEditNode>> {

  @Override
  public boolean test(Collection<VisualEditNode> nodes) {
    VisualEditNode parent = null;
    for (VisualEditNode node : nodes) {
      if (parent == null) {
        parent = node.getParent();
      } else if (parent != node.getParent()) {
        return true;
      }
    }
    return false;
  }

}
