package com.mc.tool.framework.view;

import com.mc.tool.framework.utility.I18nUtility;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.HBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class OnlineStatus extends HBox implements Initializable {
  @FXML
  private Label onlineIndicator;
  @FXML
  private Label onlineText;

  private static final String ONLINE_IMAGE = "com/mc/tool/framework/img/online.png";
  private static final String OFFLINE_IMAGE = "com/mc/tool/framework/img/offline.png";
  private static Image ONLINE_BG_IMG;
  private static Image OFFLINE_BG_IMG;
  private static Background ONLINE_BG;
  private static Background OFFLINE_BG;
  private final ReadOnlyBooleanProperty onlineProperty;

  static {
    ClassLoader loader = Thread.currentThread().getContextClassLoader();
    ONLINE_BG_IMG = new Image(loader.getResourceAsStream(ONLINE_IMAGE));
    OFFLINE_BG_IMG = new Image(loader.getResourceAsStream(OFFLINE_IMAGE));

    ONLINE_BG = new Background(
        new BackgroundImage(ONLINE_BG_IMG, BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT,
            BackgroundPosition.CENTER, null));
    OFFLINE_BG = new Background(
        new BackgroundImage(OFFLINE_BG_IMG, BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT,
            BackgroundPosition.CENTER, null));
  }

  /**
   * Constructor.
   *
   * @param onlineProperty online property
   */
  public OnlineStatus(ReadOnlyBooleanProperty onlineProperty) {
    this.onlineProperty = onlineProperty;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    String path = "com/mc/tool/framework/onlinestatus.fxml";
    FXMLLoader loader = new FXMLLoader(classLoader.getResource(path));
    loader.setRoot(this);
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load onlinestatus.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    onlineIndicator.backgroundProperty()
        .bind(Bindings.when(onlineProperty).then(ONLINE_BG).otherwise(OFFLINE_BG));
    onlineText.textProperty()
        .bind(Bindings.when(onlineProperty)
            .then(I18nUtility.getI18nBundle("main").getString("framework.status.online")).otherwise(
                I18nUtility.getI18nBundle("main").getString("framework.status.offline")));
  }
}
