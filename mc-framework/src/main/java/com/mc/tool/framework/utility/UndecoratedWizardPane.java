package com.mc.tool.framework.utility;

import javafx.scene.control.DialogPaneEx;

/**
 * .
 */
public class UndecoratedWizardPane extends DialogPaneEx {

  /**
   * Creates an instance of wizard pane.
   */
  public UndecoratedWizardPane() {
    // getStylesheets().add(UndecoratedWizard.class.getResource("wizard.css").toExternalForm());
    // getStyleClass().add("wizard-pane");
  }

  /**
   * Called on entering a page. This is a good place to read values from wizard settings and assign
   * them to controls on the page
   *
   * @param wizard which page will be used on
   */
  public void onEnteringPage(UndecoratedWizard wizard) {
    // no-op
  }

  /**
   * Called on existing the page. This is a good place to read values from page controls and store
   * them in wizard settings
   *
   * @param wizard which page was used on
   */
  public void onExitingPage(UndecoratedWizard wizard) {
    // no-op
  }
}
