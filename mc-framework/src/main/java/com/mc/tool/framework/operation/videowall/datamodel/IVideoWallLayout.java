package com.mc.tool.framework.operation.videowall.datamodel;

import java.util.Collection;
import java.util.List;
import javafx.beans.property.IntegerProperty;
import javafx.geometry.Rectangle2D;


/**
 * 视频墙布局接口.
 */
public interface IVideoWallLayout {
  List<Integer> getWidths();

  List<Integer> getHeights();

  int getTotalWidth();

  int getTotalHeight();

  int getRows();

  int getColumns();

  IntegerProperty getRowsProperty();

  IntegerProperty getColumnsProperty();

  Collection<Rectangle2D> getScreenAreas();
}
