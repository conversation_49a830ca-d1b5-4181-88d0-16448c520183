package com.mc.tool.framework.utility;

import com.mc.common.validation.constraints.BlackList;
import org.hibernate.validator.cfg.ConstraintDef;

/**
 * .
 */
public class BlackListDef extends ConstraintDef<BlackListDef, BlackList> {

  public BlackListDef() {
    super(BlackList.class);
  }

  public BlackListDef blackItems(String[] items) {
    addParameter("blackItems", items);
    return this;
  }

}
