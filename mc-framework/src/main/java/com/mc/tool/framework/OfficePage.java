package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.office.view.OfficePageView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class OfficePage implements Page {
  private final OfficePageView view;
  private SimpleBooleanProperty visibilityProperty = new SimpleBooleanProperty(true);

  public OfficePage(VisualEditModel model) {
    view = new OfficePageView(model);
  }

  @Override
  public String getTitle() {
    return "Office";
  }

  @Override
  public String getName() {
    return "Office";
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibilityProperty;
  }

  @Override
  public void showObject(Object object) {
  }

  @Override
  public String getStyleClass() {
    // TODO Auto-generated method stub
    return null;
  }
}
