package com.mc.tool.framework;

import com.google.inject.Binder;
import com.google.inject.Module;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.operation.controller.OperationPageController;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenControllable;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenController;
import com.mc.tool.framework.operation.seat.controller.SeatControllable;
import com.mc.tool.framework.operation.seat.controller.SeatOperationController;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.controller.VideoWallOperationController;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.controller.SystemEditPageController;

/**
 * .
 */
public class DefaultModule implements Module {

  /**
   * 配置模块.
   */
  @Override
  public void configure(Binder binder) {
    binder.bind(ApplicationBase.class).to(DefaultApplication.class);
    binder.bind(SystemEditControllable.class).to(SystemEditPageController.class);
    binder.bind(OperationPageControllable.class).to(OperationPageController.class);
    binder.bind(VideoWallControllable.class).to(VideoWallOperationController.class);
    binder.bind(SeatControllable.class).to(SeatOperationController.class);
    binder.bind(CrossScreenControllable.class).to(CrossScreenController.class);
  }

}
