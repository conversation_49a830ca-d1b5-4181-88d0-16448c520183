package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.UserRightGetter;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public class DefaultUserRight implements UserRightGetter {

  @Override
  public boolean isSystemEditRenamable() {
    return true;
  }

  @Override
  public boolean isSystemEditGroupCreateDeletable() {
    return true;
  }

  @Override
  public boolean isVideoWallCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSeatCreateDeletable() {
    return true;
  }

  @Override
  public boolean isDeviceTypeChangable() {
    return true;
  }

  @Override
  public boolean isDeviceItemCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSystemScenarioEditable() {
    return true;
  }

  @Override
  public boolean isVideoWallLayoutEditable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowCreateDeletable(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowMovablale(VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallWindowResizable(
      VideoWallFunc func) {
    return true;
  }


  @Override
  public boolean isVideoWallScenarioCreateDeletable(
      VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallScenarioActivatable(
      VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isVideoWallConnectable(
      VideoWallFunc func) {
    return true;
  }

  @Override
  public boolean isSeatConnectable() {
    return true;
  }

  @Override
  public boolean isDeviceItemMovable() {
    return true;
  }

  @Override
  public boolean isSeatScenarioCreateDeletable() {
    return true;
  }

  @Override
  public boolean isSeatSceanrioActivatable() {
    return true;
  }

  @Override
  public boolean isDeviceConnectable() {
    return true;
  }

}
