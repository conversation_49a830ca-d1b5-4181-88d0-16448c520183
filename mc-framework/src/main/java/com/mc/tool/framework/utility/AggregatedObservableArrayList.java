package com.mc.tool.framework.utility;

import com.sun.javafx.collections.ObservableListWrapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import lombok.NonNull;

/**
 * This class aggregates several other Observed Lists (sublists), observes changes on those sublists
 * and applies those same changes to the aggregated list. Inspired by: -
 * http://stackoverflow.com/questions/25705847/listchangelistener-waspermutated-block -
 * http://stackoverflow.com/questions/37524662/how-to-concatenate-observable-lists-in-javafx -
 * https://github.com/lestard/advanced-bindings/blob/master/src/main/java/eu/lestard/advanced_bindings/api/CollectionBindings.java
 * Posted result on:
 * http://stackoverflow.com/questions/37524662/how-to-concatenate-observable-lists-in-javafx
 */
public class AggregatedObservableArrayList<T> {

  protected final List<ObservableList<T>> lists = new ArrayList<>();
  private final List<Integer> sizes = new ArrayList<>();
  private final List<InternalListModificationListener> listeners = new ArrayList<>();
  protected final InnerList<T> aggregatedList = new InnerList<>(new ArrayList<>());
  protected final String name;

  public AggregatedObservableArrayList(String name) {
    this.name = name;
  }

  /**
   * The Aggregated Observable List. This list is unmodifiable, because sorting this list would mess
   * up the entire bookkeeping we do here.
   *
   * @return an unmodifiable view of the aggregatedList
   */
  public ObservableList<T> getAggregatedList() {
    return FXCollections.unmodifiableObservableList(aggregatedList);
  }

  public Collection<ObservableList<T>> getObservableLists() {
    return new ArrayList<>(lists);
  }

  /**
   * Append list.
   *
   * @param list list to be appended
   */
  public void appendList(@NonNull ObservableList<T> list) {
    //assert !lists.contains(list) : "List is already contained: " + list;
    lists.add(list);
    final InternalListModificationListener listener = new InternalListModificationListener(list);
    list.addListener(listener);
    // System.out.println("list = " + list + " puttingInMap=" + list.hashCode());
    sizes.add(list.size());
    aggregatedList.addAll(list);
    listeners.add(listener);
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
  }

  /**
   * Prepend list .
   *
   * @param list list to prepend
   */
  public void prependList(@NonNull ObservableList<T> list) {
    assert !lists.contains(list) : "List is already contained: " + list;
    lists.add(0, list);
    final InternalListModificationListener listener = new InternalListModificationListener(list);
    list.addListener(listener);
    // System.out.println("list = " + list + " puttingInMap=" + list.hashCode());
    sizes.add(0, list.size());
    aggregatedList.addAll(0, list);
    listeners.add(0, listener);
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
  }

  /**
   * Add list .
   */
  public void addList(int index, Collection<ObservableList<T>> addedLists) {
    if (index < 0 || index > lists.size()) {
      return;
    }

    List<InternalListModificationListener> addedListeners = new ArrayList<>();
    List<Integer> addedSizes = new ArrayList<>();
    List<T> addedItems = new ArrayList<>();
    for (ObservableList<T> item : addedLists) {
      final InternalListModificationListener listener = new InternalListModificationListener(item);
      item.addListener(listener);
      addedListeners.add(listener);
      addedSizes.add(item.size());
      addedItems.addAll(item);
    }
    lists.addAll(index, addedLists);
    listeners.addAll(index, addedListeners);
    sizes.addAll(index, addedSizes);

    int presize = 0;
    for (int i = 0; i < index; i++) {
      presize += sizes.get(i);
    }
    aggregatedList.addAll(presize, addedItems);

    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
  }

  /**
   * Clear list.
   */
  public void clearList() {
    while (!lists.isEmpty()) {
      removeList(lists.get(0));
    }
  }

  /**
   * Remove list.
   *
   * @param list list to be removed
   */
  public void removeList(@NonNull ObservableList<T> list) {
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
    final int index = indexOfList(list);
    if (index < 0) {
      throw new IllegalArgumentException(
          "Cannot remove a list that is not contained: " + list + " lists=" + lists);
    }
    final int startIndex = getStartIndex(list);
    final int endIndex = getEndIndex(list, startIndex);
    // we want to find the start index of this list inside the aggregated List. End index will be
    // start + size - 1.
    lists.remove(index);
    sizes.remove(index);
    final InternalListModificationListener listener = listeners.remove(index);
    list.removeListener(listener);
    aggregatedList.remove(startIndex, endIndex + 1); // end + 1 because end is exclusive
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
  }

  /**
   * 删除列表.
   *
   * @param fromIndex 要删除首索引
   * @param toIndex   尾索引(exclusive)
   */
  public void removeList(int fromIndex, int toIndex) {
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
    if (toIndex <= fromIndex || fromIndex < 0 || toIndex > lists.size()) {
      return;
    }
    final int startIndex = getStartIndex(lists.get(fromIndex));
    final int endIndex = getEndIndex(lists.get(toIndex - 1), getStartIndex(lists.get(toIndex - 1)));
    // we want to find the start index of this list inside the aggregated List. End index will be
    // start + size - 1.
    for (int i = fromIndex; i < toIndex; i++) {
      lists.get(i).removeListener(listeners.get(i));
    }
    lists.subList(fromIndex, toIndex).clear();
    sizes.subList(fromIndex, toIndex).clear();
    listeners.subList(fromIndex, toIndex).clear();
    aggregatedList.remove(startIndex, endIndex + 1); // end + 1 because end is exclusive
    assert lists.size() == sizes.size() && lists.size() == listeners.size() : "lists.size="
        + lists.size() + " not equal to sizes.size=" + sizes.size()
        + " or not equal to listeners.size=" + listeners.size();
  }

  /**
   * Get the start index of this list inside the aggregated List. This is a private function. we can
   * safely asume, that the list is in the map.
   *
   * @param list the list in question
   * @return the start index of this list in the aggregated List
   */
  private int getStartIndex(@NonNull ObservableList<T> list) {
    int startIndex = 0;
    // System.out.println("=== searching startIndex of " + list);
    assert lists.size() == sizes.size() : "lists.size=" + lists.size() + " not equal to sizes.size="
        + sizes.size();

    int listIndex = indexOfList(list);
    // 计算startIndex
    for (int i = 0; i < listIndex; i++) {
      final Integer size = sizes.get(i);
      startIndex += size;
      // System.out.println(" startIndex = " + startIndex + " added=" + size);
    }
    // System.out.println("startIndex = " + startIndex);
    return startIndex;
  }

  /**
   * Get the end index of this list inside the aggregated List. This is a private function. we can
   * safely asume, that the list is in the map.
   *
   * @param list       the list in question
   * @param startIndex the start of the list (retrieve with {@link #getStartIndex(ObservableList)}
   * @return the end index of this list in the aggregated List
   */
  private int getEndIndex(@NonNull ObservableList<T> list, int startIndex) {
    assert lists.size() == sizes.size() : "lists.size=" + lists.size() + " not equal to sizes.size="
        + sizes.size();
    final int index = indexOfList(list);
    return startIndex + sizes.get(index) - 1;
  }

  private int indexOfList(ObservableList<T> list) {
    // 不能用lists.indexOf(list)来找，因为如果list里面的数据一样的话，会出错
    int listIndex = -1;
    for (int i = 0; i < lists.size(); i++) {
      if (lists.get(i) == list) {
        listIndex = i;
        break;
      }
    }
    return listIndex;
  }

  private class InternalListModificationListener implements ListChangeListener<T> {

    @NonNull
    private final ObservableList<T> list;

    public InternalListModificationListener(@NonNull ObservableList<T> list) {
      this.list = list;
    }

    /**
     * Called after a change has been made to an ObservableList.
     *
     * @param change an object representing the change that was done
     * @see Change
     */
    @Override
    public void onChanged(Change<? extends T> change) {
      final ObservableList<? extends T> changedList = change.getList();
      final int startIndex = getStartIndex(list);
      final int index = indexOfList(list);
      final int newSize = changedList.size();
      // System.out.println("onChanged for list=" + list + " aggregate=" + aggregatedList);
      aggregatedList.beginUpdate();
      try {
        while (change.next()) {
          final int from = change.getFrom();
          final int to = change.getTo();
          // System.out.println(" startIndex=" + startIndex + " from=" + from + " to=" + to);
          if (change.wasPermutated()) {
            final ArrayList<T> copy =
                new ArrayList<>(aggregatedList.subList(startIndex + from, startIndex + to));
            // System.out.println(" permutating sublist=" + copy);
            for (int oldIndex = from; oldIndex < to; oldIndex++) {
              int newIndex = change.getPermutation(oldIndex);
              copy.set(newIndex - from, aggregatedList.get(startIndex + oldIndex));
            }
            // System.out.println(" permutating done sublist=" + copy);
            aggregatedList.subList(startIndex + from, startIndex + to).clear();
            aggregatedList.addAll(startIndex + from, copy);
          } else {
            if (change.wasRemoved()) {
              List<? extends T> removed = change.getRemoved();
              // System.out.println(" removed= " + removed);
              // IMPORTANT! FROM == TO when removing items.
              aggregatedList.remove(startIndex + from, startIndex + from + removed.size());
            }
            if (change.wasAdded()) {
              List<? extends T> added = change.getAddedSubList();
              // System.out.println(" added= " + added);
              // add those elements to your data
              aggregatedList.addAll(startIndex + from, added);
            }
          }
        }
        // update the size of the list in the map
        // System.out.println("list = " + list + " puttingInMap=" + list.hashCode());
        sizes.set(index, newSize);
        // System.out.println("listSizesMap = " + sizes);
      } finally {
        aggregatedList.endUpdate();
      }
    }

  }

  /**
   * Dump the list.
   *
   * @param function dump funcion.
   * @return dump result.
   */
  public String dump(Function<T, Object> function) {
    StringBuilder sb = new StringBuilder();
    sb.append("[");
    aggregatedList.forEach(el -> sb.append(function.apply(el)).append(","));
    final int length = sb.length();
    sb.replace(length - 1, length, "");
    sb.append("]");
    return sb.toString();
  }

  private static class InnerList<T> extends ObservableListWrapper<T> {

    public InnerList(List<T> list) {
      super(list);
    }

    public void beginUpdate() {
      beginChange();
    }

    public void endUpdate() {
      endChange();
    }
  }
}
