package com.mc.tool.framework.operation.office;

import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.operation.office.view.OfficeSceneView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import javafx.stage.Popup;
import javafx.stage.Window;

/**
 * .
 */
public class OfficeWindow {
  private static Popup popup = new Popup();

  static {
    popup.setAutoHide(true);
    popup.setAutoFix(false);
  }

  /**
   * 显示窗体.
   */
  public static void showWindow(OperationPageControllable controllable, VisualEditModel model,
                                Window owner, double xpos, double ypos, double width, double height) {
    OfficeSceneView view = new OfficeSceneView(controllable, model);
    view.setPrefWidth(width);
    view.setPrefHeight(height);
    view.fitToView();
    popup.getContent().clear();
    popup.getContent().add(view);
    popup.show(owner, xpos - 0.5, ypos - 0.5);
  }

  public static void hideWindow() {
    popup.hide();
  }

}
