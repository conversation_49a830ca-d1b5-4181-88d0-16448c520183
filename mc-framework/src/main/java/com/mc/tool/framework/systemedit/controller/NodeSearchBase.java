package com.mc.tool.framework.systemedit.controller;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.util.Callback;
import javafx.util.StringConverter;
import org.controlsfx.control.textfield.AutoCompletionBinding.ISuggestionRequest;

/**
 * .
 */
public abstract class NodeSearchBase implements NodeSearcher {

  private final Collection<VisualEditNode> roots;

  public NodeSearchBase(Collection<VisualEditNode> roots) {
    this.roots = roots;
  }

  @Override
  public Collection<VisualEditNode> search(String text, boolean searchAll) {
    List<VisualEditNode> result = new ArrayList<>();
    for (VisualEditNode node : roots) {
      result.addAll(searchImpl(node, text, searchAll));
      if (!searchAll && result.size() > 0) {
        break;
      }
    }
    return result;
  }

  @Override
  public Callback<ISuggestionRequest, Collection<VisualEditNode>> createSearchSuggestion() {
    return new SearchSuggestion();
  }

  private Collection<VisualEditNode> searchImpl(VisualEditNode node, String text,
                                                boolean searchAll) {
    List<VisualEditNode> result = new ArrayList<>();
    StringConverter<VisualEditNode> converter = createNodeStringConverter();
    String value = converter.toString(node);
    if (value.contains(text)) {
      result.add(node);
    } else {
      for (VisualEditNode child : node.getChildren()) {
        result.addAll(searchImpl(child, text, searchAll));
        if (!searchAll && result.size() > 0) {
          break;
        }
      }
    }
    return result;
  }

  class SearchSuggestion implements Callback<ISuggestionRequest, Collection<VisualEditNode>> {

    @Override
    public Collection<VisualEditNode> call(ISuggestionRequest param) {
      return search(param.getUserText(), true);
    }
  }

}
