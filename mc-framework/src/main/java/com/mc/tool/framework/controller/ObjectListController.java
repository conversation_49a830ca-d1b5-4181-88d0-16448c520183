package com.mc.tool.framework.controller;

import com.google.inject.Inject;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.EntityFactory;
import com.mc.tool.framework.utility.EntityUtility;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.view.ObjectListCell;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import javafx.beans.binding.Bindings;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Side;
import javafx.scene.Node;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.MenuItem;
import javafx.scene.control.MultipleSelectionModel;
import javafx.scene.control.SelectionMode;
import javafx.scene.input.MouseEvent;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@SuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
@Slf4j
public class ObjectListController implements Initializable {

  private static final int MAX_LIST_COUNT = 8;

  @FXML
  ListView<Entity> listView;
  @FXML
  ContextMenu listContextMenu;
  @FXML
  Label titleLabel;

  @Inject
  private ApplicationBase applicationBase;
  private ObservableList<Entity> entityList;
  private String type;

  @Override
  public void initialize(URL arg0, ResourceBundle arg1) {
    listView.getSelectionModel().setSelectionMode(SelectionMode.SINGLE);

    listView.setCellFactory(lv -> new ObjectListCell(applicationBase));
  }

  /**
   * Init the controller by outsider.
   *
   * @param type the type of the list.
   * @param name the name of the list.
   */
  public void init(String type, String name) {
    this.type = type;
    this.titleLabel.setText(name);
    entityList = applicationBase.getEntityMananger().getEntityList(type);
    listView.setItems(entityList);
    // 选中新添加的项
    entityList.addListener((ListChangeListener<Entity>) change -> {
      while (change.next()) {
        if (change.getAddedSize() > 0) {
          listView.getSelectionModel().select(change.getAddedSubList().get(0));
          break;
        }
      }
    });
  }

  /**
   * 获取所有的action.
   *
   * @return action的列表.
   */
  public Collection<Pair<String, Runnable>> getTypeActions() {
    List<Pair<String, Runnable>> result = new ArrayList<>();
    EntityFactory factory = applicationBase.getEntityFactory();
    Collection<TypeWrapper> types = factory.getSupportEntityTypes();
    for (TypeWrapper type : types) {
      if (!EntityUtility.isType(type.getType(), this.type)) {
        continue;
      }

      Pair<String, Runnable> action = getTypeAction(type);
      if (action != null) {
        result.add(action);
      }
    }

    return result;
  }

  protected Pair<String, Runnable> getTypeAction(TypeWrapper type) {
    if (!EntityUtility.isType(type.getType(), this.type)) {
      return null;
    }
    Runnable runnable = () -> createEntity(type.getType());
    String name;
    if (EntityUtility.isCreateType(type.getType())) {
      name = String.format(
          I18nUtility.getI18nBundle("main").getString("framework.objectlist.menu.create_format"),
          type.getName());
    } else if (EntityUtility.isConnectType(type.getType())) {
      name = String.format(
          I18nUtility.getI18nBundle("main").getString("framework.objectlist.menu.connect_format"),
          type.getName());
    } else {
      name = type.getName();
    }
    return new Pair<>(name, runnable);
  }


  protected void initContextMenu() {
    EntityFactory factory = applicationBase.getEntityFactory();

    Collection<TypeWrapper> types = factory.getSupportEntityTypes();

    for (TypeWrapper type : types) {
      if (!EntityUtility.isType(type.getType(), this.type)) {
        continue;
      }

      Pair<String, Runnable> action = getTypeAction(type);
      MenuItem item = new MenuItem();
      item.setOnAction(e -> action.getValue().run());
      item.setText(action.getKey());

      if (!type.getLogoUrl().isEmpty()) {
        item.setStyle("-fx-graphic=url(" + type.getLogoUrl() + ")");
      }
      item.disableProperty().bind(Bindings.size(entityList).greaterThanOrEqualTo(MAX_LIST_COUNT));

      listContextMenu.getItems().add(item);
    }

    MenuItem closeAllMenu = new MenuItem(
        I18nUtility.getI18nBundle("main").getString("framework.menu.closeall"));
    closeAllMenu.setOnAction(e -> closeAllEntity());
    listContextMenu.getItems().add(closeAllMenu);
    listContextMenu.setAutoHide(true);

  }

  protected void closeEntity(Entity entity) {
    try {
      applicationBase.getEntityFactory().closeEntity(entity);
    } catch (RuntimeException exception) {
      log.warn("Fail to close entity!", exception);
    }
    applicationBase.getEntityMananger().removeEntity(entity);
  }

  protected void closeAllEntity() {
    for (Entity entity : entityList) {
      try {
        applicationBase.getEntityFactory().closeEntity(entity);
      } catch (RuntimeException exception) {
        log.warn("Fail to close entity!", exception);
      }
    }

    applicationBase.getEntityMananger().removeEntity(entityList.toArray(new Entity[0]));
  }

  protected void createEntity(String type) {
    EntityFactory factory = applicationBase.getEntityFactory();

    Consumer<Entity> addAction = (entity) -> {
      if (entity == null) {
        log.warn("Create entity fail!");
        return;
      }

      applicationBase.getEntityMananger().addEntity(entity);
    };

    factory.createEntity(type, addAction);

  }

  protected MultipleSelectionModel<Entity> getSelectionModel() {
    return listView.getSelectionModel();
  }

  /**
   * Response for menu button, show or hide the contextmenu.
   *
   * @param event mouseevent.
   */
  @FXML
  public void onMenu(MouseEvent event) {
    if (listContextMenu.isShowing()) {
      listContextMenu.hide();
    } else {
      listContextMenu.show((Node) event.getSource(), Side.BOTTOM, 0, 0);
    }
  }

}
