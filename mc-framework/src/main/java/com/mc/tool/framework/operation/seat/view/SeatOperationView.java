package com.mc.tool.framework.operation.seat.view;

import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.operation.interfaces.OperationView;
import com.mc.tool.framework.operation.seat.controller.SeatControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SeatOperationView extends VBox implements OperationView {
  private SeatControllable controllable;

  /**
   * Constructor.
   *
   * @param model model
   * @param func  func
   */
  public SeatOperationView(VisualEditModel model, VisualEditFunc func) {
    FXMLLoader loader = new FXMLLoader(getClass().getResource(
        "/com/mc/tool/framework/operation/seat/seat.fxml"));
    controllable = InjectorProvider.getInjector().getInstance(SeatControllable.class);
    controllable.init(model, func);
    loader.setRoot(this);
    loader.setController(controllable);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load seat.fxml!", exception);
    }
  }

  @Override
  public Node getView() {
    return this;
  }

  @Override
  public OperationControllable getOperationControllable() {
    return controllable;
  }

}
