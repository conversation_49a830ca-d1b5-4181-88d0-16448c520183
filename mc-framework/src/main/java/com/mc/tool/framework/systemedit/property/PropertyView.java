package com.mc.tool.framework.systemedit.property;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.effect.DropShadow;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.ColumnConstraints;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class PropertyView extends VBox implements Initializable {
  @FXML
  private HBox titleBox;
  @FXML
  private GridPane propertiesGrid;
  @FXML
  private VBox emptyBox;

  @FXML
  private Label deviceIcon;
  @FXML
  private Label deviceNameLabel;

  @FXML
  private Label editConfigBtn;

  private List<PropertySettingListener> listeners = new ArrayList<>();

  private ObjectProperty<PropertySheet> propertySource = new SimpleObjectProperty<>();

  private ObjectProperty<PropertySheet> innerProperty;
  private ChangeListener<PropertySheet> propertyInnerChangeListener;

  /**
   * Constructor.
   */
  public PropertyView() {
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/systemedit/property/property_view.fxml"));
    loader.setRoot(this);
    loader.setController(this);
    try {
      loader.load();
    } catch (Exception exception) {
      log.warn("fail to load property_view.xml", exception);
    }
  }

  /**
   * .
   */
  public static class PropertySettingListener {
    public void call() {
    }
  }

  public void addPropertySettingListener(PropertySettingListener listener) {
    listeners.add(listener);
  }


  @Override
  public void initialize(URL location, ResourceBundle resources) {
    BooleanBinding showPropertyBinding = new BooleanBinding() {
      {
        super.bind(propertySource);
      }

      @Override
      protected boolean computeValue() {
        return propertySource.get() != null;
      }
    };

    titleBox.visibleProperty().bind(showPropertyBinding);
    propertiesGrid.visibleProperty().bind(showPropertyBinding);
    emptyBox.visibleProperty().bind(showPropertyBinding.not());

    titleBox.managedProperty().bind(titleBox.visibleProperty());
    propertiesGrid.managedProperty().bind(propertiesGrid.visibleProperty());
    emptyBox.managedProperty().bind(emptyBox.visibleProperty());

    ColumnConstraints col1 = new ColumnConstraints();
    col1.setPercentWidth(50);
    ColumnConstraints col2 = new ColumnConstraints();
    col2.setPercentWidth(50);
    propertiesGrid.getColumnConstraints().addAll(col1, col2);

    DropShadow shadow = new DropShadow();
    editConfigBtn.setOnMouseEntered((event) -> {
      editConfigBtn.setEffect(shadow);
    });

    editConfigBtn.setOnMouseExited((event) -> {
      editConfigBtn.setEffect(null);
    });

    editConfigBtn.setOnMouseClicked((event) -> {
      for (PropertySettingListener listen : listeners) {
        listen.call();
      }
    });

    propertySource.addListener((observable, oldValue, newValue) -> onObjectChange(newValue));

    propertyInnerChangeListener = (observable, oldValue, newValue) -> propertySource.set(newValue);
  }

  /**
   * 设置当前属性.
   *
   * @param properties 属性
   */
  public void setProperty(ObjectProperty<PropertySheet> properties) {
    if (innerProperty != null) {
      innerProperty.removeListener(propertyInnerChangeListener);
    }
    if (properties == null) {
      innerProperty = null;
      propertySource.set(null);
    } else {
      propertySource.set(properties.get());
      properties.addListener(propertyInnerChangeListener);
      innerProperty = properties;
    }

  }

  private void onObjectChange(PropertySheet node) {
    if (node == null) {
      return;
    }
    deviceNameLabel.setText(node.getName());
    deviceIcon.setBackground(new Background(new BackgroundImage(new Image(node.getLogoUrl()),
        BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER, null)));
    propertiesGrid.getChildren().clear();
    int row = 0;
    for (PropertyItem<?> item : node.getProperties()) {
      Label title = new Label(item.getTitle());
      title.setTooltip(new Tooltip(item.getTitle()));
      propertiesGrid.add(title, 0, row);
      int oldRow = row;
      for (Object value : item.getValue()) {
        if (value == null) {
          continue;
        }
        Label valueLabel = new Label(value.toString());
        valueLabel.setTooltip(new Tooltip(value.toString()));
        propertiesGrid.add(valueLabel, 1, row);
        row++;
      }
      // 必须要换行
      if (row == oldRow) {
        row++;
      }
    }
  }


}
