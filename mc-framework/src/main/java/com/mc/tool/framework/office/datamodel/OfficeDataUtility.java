package com.mc.tool.framework.office.datamodel;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.util.SelectableNode;

/**
 * .
 */
public class OfficeDataUtility {
  /**
   * 把SelectedNode转换为OfficeData.
   *
   * @param node 要转换的SelectedNode
   * @return 如果转换成功，返回相应的OfficeData，否则返回null
   */
  public static OfficeData selectedNode2OfficeData(SelectableNode node) {
    if (node instanceof CellSkin) {
      CellSkin skin = (CellSkin) node;
      CellBindedObject cellBindedObject = skin.getCell().getBindedObject();
      if (cellBindedObject instanceof OfficeData) {
        return (OfficeData) cellBindedObject;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}
