package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ExtraActionInfo;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import java.util.Collections;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;

/**
 * .
 */
public class VideoData implements CellBindedObject, VideoObject {
  @Expose
  @Getter
  protected StringProperty name = new SimpleStringProperty("");

  @Expose
  @Getter
  protected ObjectProperty<VisualEditTerminal> source = new SimpleObjectProperty<>();

  protected StringProperty nameWithSource = null;

  /**
   * 视频位置的横坐标.
   */
  @Expose
  @Getter
  protected IntegerProperty xpos = new SimpleIntegerProperty();

  /**
   * 视频位置的纵坐标.
   */
  @Expose
  @Getter
  protected IntegerProperty ypos = new SimpleIntegerProperty();

  /**
   * 视频的宽度.
   */
  @Expose
  @Getter
  protected IntegerProperty width = new SimpleIntegerProperty();

  /**
   * 视频的高度.
   */
  @Expose
  @Getter
  protected IntegerProperty height = new SimpleIntegerProperty();

  @Expose
  @Getter
  protected DoubleProperty alpha = new SimpleDoubleProperty(1);

  protected ChangeListener<VisualEditTerminal> changeListener;
  protected ChangeListener<String> sourceNameChangeListener;
  protected WeakAdapter weakAdapter;

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return FXCollections.observableArrayList();
  }

  @Override
  public Collection<ExtraActionInfo> getExtraActions() {
    return Collections.emptyList();
  }

  /**
   * .
   */
  public VideoData() {
    weakAdapter = new WeakAdapter();
  }

  @Override
  public ReadOnlyStringProperty getNameWithSource() {
    if (nameWithSource == null) {
      nameWithSource = new SimpleStringProperty();
      changeListener = weakAdapter.wrap(this::updateNameWithSourceBinding);
      sourceNameChangeListener =
          weakAdapter.wrap((obs, oldVal, newVal) -> nameWithSource.set(createNameWithSource()));
      source.addListener(changeListener);
      name.addListener(sourceNameChangeListener);
      updateNameWithSourceBinding(source, source.get(), source.get());
    }
    return nameWithSource;
  }

  protected void updateNameWithSourceBinding(ObservableValue<?> obs, VisualEditTerminal oldVal,
                                             VisualEditTerminal newVal) {
    if (oldVal != null) {
      oldVal.nameProperty().removeListener(sourceNameChangeListener);
    }
    if (newVal != null) {
      nameWithSource.set(createNameWithSource());
      newVal.nameProperty().addListener(sourceNameChangeListener);
    } else {
      nameWithSource.set(name.getValue());
    }
  }

  protected String createNameWithSource() {
    return name.get() + "[" + source.get().getName() + "]";
  }

  /**
   * 复制数据.
   *
   * @param videoData 接收复制的数据的videodata.
   */
  @Override
  public void copyTo(VideoObject videoData) {
    videoData.getName().set(name.get());
    videoData.getXpos().set(xpos.get());
    videoData.getYpos().set(ypos.get());
    videoData.getWidth().set(width.get());
    videoData.getHeight().set(height.get());
    videoData.getAlpha().set(alpha.get());
    videoData.getSource().set(source.get());
  }
}
