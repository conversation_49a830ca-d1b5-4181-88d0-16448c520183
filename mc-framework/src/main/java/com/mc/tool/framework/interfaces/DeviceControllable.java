package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.DefaultUserRight;
import javafx.beans.property.BooleanProperty;

/**
 * .
 */
public interface DeviceControllable {
  /**
   * 是否为演示版本.
   *
   * @return 如果为演示版本，返回true
   */
  boolean isDemo();

  boolean isOnline();

  BooleanProperty onlineProperty();

  default UserRightGetter getUserRight() {
    return new DefaultUserRight();
  }

  default void close() {
  }
}
