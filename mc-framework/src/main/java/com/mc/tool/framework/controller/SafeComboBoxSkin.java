package com.mc.tool.framework.controller;

import com.sun.javafx.scene.control.skin.ComboBoxListViewSkin;
import javafx.scene.AccessibleAttribute;
import javafx.scene.control.ComboBox;

/**
 * 用于解决combobox报错的问题.
 */
@SuppressWarnings("unused")
public class SafeComboBoxSkin<T> extends ComboBoxListViewSkin<T> {
  public SafeComboBoxSkin(ComboBox<T> comboBox) {
    super(comboBox);
  }

  @Override
  public Object queryAccessibleAttribute(AccessibleAttribute attribute, Object... parameters) {
    try {
      return super.queryAccessibleAttribute(attribute, parameters);
    } catch (NullPointerException e) {
      return null;
    }
  }
}
