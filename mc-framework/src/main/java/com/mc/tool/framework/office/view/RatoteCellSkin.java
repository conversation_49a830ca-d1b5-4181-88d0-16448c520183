package com.mc.tool.framework.office.view;

import com.mc.graph.AbstractCellSkin;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import java.net.URL;
import java.util.Collection;
import java.util.Collections;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.value.ChangeListener;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Point2D;
import javafx.scene.Cursor;
import javafx.scene.ImageCursor;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.image.Image;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.transform.Rotate;
import lombok.Getter;

/**
 * .
 */
public class RatoteCellSkin extends AbstractCellSkin implements Initializable {
  @FXML
  protected Pane region;
  @FXML
  @Getter
  protected Pane image;
  @FXML
  @Getter
  protected Pane ratote;
  @FXML
  @Getter
  protected Label nameLabel;

  protected boolean isRotate = false;
  protected boolean isDragged = false;

  protected DoubleProperty centerYposProperty;
  protected DoubleProperty centerXposProperty;
  protected double lastAngle;
  protected DoubleProperty angle;


  public RatoteCellSkin(CellObject cellobject, Parent parent, Parent container,
                        SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);

  }

  protected DoubleProperty getCenterXposProperty() {
    if (centerXposProperty == null) {
      centerXposProperty = new SimpleDoubleProperty();
    }
    return centerXposProperty;
  }

  protected DoubleProperty getCenterYposProperty() {
    if (centerYposProperty == null) {
      centerYposProperty = new SimpleDoubleProperty();
    }
    return centerYposProperty;
  }

  protected DoubleProperty getAngleProperty() {
    if (angle == null) {
      angle = new SimpleDoubleProperty();
    }
    return angle;
  }

  protected double getCenterYpos() {
    return getCenterYposProperty().get();
  }

  protected double getCenterXpos() {
    return getCenterXposProperty().get();
  }

  protected void updateNameBinding(Label label) {
    // 更新Name绑定
    label.textProperty().unbind();
    if (getCell().getBindedObject() instanceof OfficeData) {
      OfficeData data = (OfficeData) getCell().getBindedObject();
      if (data.getFunc() != null) {
        label.textProperty().bindBidirectional(data.getFunc().nameProperty());
      }
    }
  }

  /*
   ******************************* 图像旋转相关代码*********************************.
   */

  protected void setMouseEvent() {
    // 定义region的鼠标游标
    getRatote().addEventFilter(MouseEvent.MOUSE_MOVED, (event) -> {
      setRatoteDragCursor();
      event.consume();
    });
    getRegion().addEventFilter(MouseEvent.MOUSE_EXITED, (event) -> {
      getRatote().setCursor(Cursor.DEFAULT);
    });
    getRegion().addEventFilter(MouseEvent.MOUSE_MOVED, (event) -> {
      getRatote().setCursor(Cursor.DEFAULT);
    });


    getRatote().addEventFilter(MouseEvent.MOUSE_PRESSED, (event) -> {
      isDragged = false;
      isRotate = true;
      lastAngle = getAngleProperty().get();
      event.consume();
    });
    getRatote().addEventFilter(MouseEvent.MOUSE_DRAGGED, (event) -> {
      isDragged = true;
      if (isRotate) {
        setRatoteDragCursor();
        updateAngle(event.getScreenX(), event.getScreenY());
        event.consume();
      }
    });
    getRatote().addEventFilter(MouseEvent.MOUSE_RELEASED, (event) -> {
      if (isDragged) {
        updateAngle(event.getScreenX(), event.getScreenY());
      }
      getRatote().setCursor(Cursor.DEFAULT);
      isDragged = false;
      isRotate = false;
      event.consume();
    });
  }


  /*
   * 设置旋转鼠标游标.
   */
  private void setRatoteDragCursor() {
    Image image = new Image(OfficeConstants.ROTATE_CURSOR);
    getRatote().setCursor(new ImageCursor(image, image.getWidth() / 2, image.getHeight() / 2));
  }

  /*
   * 按坐标旋转图片.
   */
  private void updateAngle(double xpos, double ypos) {
    getAngleProperty().set(getAngle(xpos, ypos) + lastAngle);
  }


  /*
   * 按角度旋转图片.
   */
  private void paintRotateImage() {
    Rotate rotate = new Rotate(getAngleProperty().get(), getCenterXpos(), getCenterYpos());
    getImage().getTransforms().setAll(rotate);
  }

  /*
   * 计算鼠标旋转角度.
   */
  private double getAngle(double xpos, double ypos) {

    double returnValue = 0;
    Point2D point2d = image.localToScreen(getCenterXpos(), getCenterXpos());
    double xdistance = xpos - point2d.getX();
    double ydistance = ypos - point2d.getY();
    double qtan = 0;

    if (xdistance > 0 && ydistance < 0) {
      // 第一个象限
      qtan = Math.abs(ydistance) / Math.abs(xdistance);
      returnValue = 90 - Math.toDegrees(Math.atan(qtan));
    } else if (xdistance > 0 && ydistance > 0) {
      // 第二个象限
      qtan = Math.abs(ydistance) / Math.abs(xdistance);
      returnValue = 90 + Math.toDegrees(Math.atan(qtan));
    } else if (xdistance < 0 && ydistance > 0) {
      // 第三个象限
      qtan = Math.abs(ydistance) / Math.abs(xdistance);
      returnValue = 270 - Math.toDegrees(Math.atan(qtan));
    } else if (xdistance < 0 && ydistance < 0) {
      // 第四个象限
      qtan = Math.abs(ydistance) / Math.abs(xdistance);
      returnValue = 270 + Math.toDegrees(Math.atan(qtan));
    } else if (xdistance == 0 && ydistance < 0) {
      // 正Y轴
      returnValue = 0;
    } else if (xdistance == 0 && ydistance > 0) {
      // 负Y轴
      returnValue = 180;
    } else if (xdistance > 0 && ydistance == 0) {
      // 正X轴
      returnValue = 90;
    } else if (xdistance < 0 && ydistance == 0) {
      // 负X轴
      returnValue = 270;
    }
    return returnValue;
  }


  protected void updateRatoteAngleBinding() {
    // 更新Name绑定
    if (getCell().getBindedObject() instanceof OfficeData) {
      OfficeData data = (OfficeData) getCell().getBindedObject();
      getAngleProperty().bindBidirectional(data.angleProperty());
    }
  }

  protected void updateRotateCenter() {
    // 设置图像旋转中心点坐标.
    getCenterXposProperty()
        .bind(Bindings.selectDouble(image.boundsInLocalProperty(), "width").divide(2));
    getCenterYposProperty()
        .bind(Bindings.selectDouble(image.boundsInLocalProperty(), "height").divide(2));
  }


  @Override
  public void initialize(URL location, ResourceBundle resources) {
    Tooltip nameTooltip = new Tooltip();
    nameTooltip.textProperty().bind(nameLabel.textProperty());
    nameLabel.setTooltip(nameTooltip);

    getRegion().setUserData(this);
    ratote.setVisible(false);

    updateRatoteAngleBinding();
    updateNameBinding(getNameLabel());
    getCell().getBindedObjectProperty().addListener((change) -> {
      updateRatoteAngleBinding();
      updateNameBinding(getNameLabel());
    });

    ChangeListener<Number> valueChangeListener = (observable, oldVal, newVal) -> paintRotateImage();
    getAngleProperty().addListener(valueChangeListener);
    getCenterXposProperty().addListener(valueChangeListener);
    getCenterYposProperty().addListener(valueChangeListener);

    updateRotateCenter();

    setMouseEvent();
  }


  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return Collections.emptyList();
  }


  @Override
  public Color getSelectionBorderColor() {
    return SystemeditConstants.SELECTED_COLOR;
  }


  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }


  @Override
  public boolean isResizeble() {
    return false;
  }


  @Override
  protected void initInner() {
  }


  @Override
  protected void initRegion() {
  }


  @Override
  protected void onConnectorChange() {
    setMouseEvent();
    updateRatoteAngleBinding();
  }


  @Override
  protected void layoutConnectors() {
  }

  @Override
  public Region getRegion() {
    return region;
  }


}
