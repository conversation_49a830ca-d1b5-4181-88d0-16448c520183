package com.mc.tool.framework.systemedit.behavior;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorBehavior;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphController;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;

/**
 * .
 */
public class HighLightCellBehavior implements CellBehavior {
  private GraphController controller;

  /**
   * Contructor.
   *
   * @param controller controller
   */
  public HighLightCellBehavior(GraphController controller) {
    this.controller = controller;
    init();
  }

  private void init() {
  }

  @Override
  public void attach() {
  }

  @Override
  public void detach() {
  }

  @Override
  public void createConnectorBehavior(ConnectorSkin skin) {
    ConnectorBehavior behavior = new HightLightMatrixConnectorBehavior(controller, skin);
    skin.addBehavior(behavior);
  }

  static class HightLightMatrixConnectorBehavior implements ConnectorBehavior {
    private ConnectorSkin connectorSkin;
    private GraphController controller;
    private EventHandler<MouseEvent> mouseClickedHandler;

    public HightLightMatrixConnectorBehavior(GraphController controller, ConnectorSkin skin) {
      this.connectorSkin = skin;
      this.controller = controller;
      init();
    }

    @Override
    public void attach() {
      connectorSkin.getNode().addEventHandler(MouseEvent.MOUSE_PRESSED, mouseClickedHandler);
    }

    @Override
    public void detach() {
      connectorSkin.getNode().removeEventHandler(MouseEvent.MOUSE_PRESSED, mouseClickedHandler);
    }

    private void init() {
      mouseClickedHandler = (event) -> {
        if (connectorSkin.getConnector().getCell().getType()
            .equals(SystemEditDefinition.MATRIX_CELL)
            && !connectorSkin.getConnector().highLightProperty().isBound()) {
          CellBindedObject bindedObject = connectorSkin.getConnector().getCell().getBindedObject();
          if (bindedObject instanceof VisualEditMatrix) {
            VisualEditMatrix matrix = (VisualEditMatrix) bindedObject;
            VisualEditTerminal terminal = matrix.getTerminal(connectorSkin.getConnector().getId());
            if (terminal != null) {
              CellSkin skin = controller.getSkinManager().getCellSkin(terminal.getCellObject());
              if (skin != null && !skin.isSelected()) {
                controller.getSelectionModel().deselectAll();
                controller.getSelectionModel().select(skin, true);
              }
            }
          }
        }
      };
    }

  }
}
