package com.mc.tool.framework.office.view;

import com.mc.tool.framework.office.controller.OfficePageController;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class OfficePageView extends VBox {

  @Getter
  private OfficePageController controllable;

  /**
   * Constructor.
   */
  public OfficePageView(VisualEditModel model) {

    try {
      controllable = InjectorProvider.getInjector().getInstance(OfficePageController.class);
      controllable.initModel(model);

      URL location =
          getClass().getResource("/com/mc/tool/framework/office/office_dynamic_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controllable);
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load office_dynamic_view.fxml", exc);
    }

  }

  public void setOperationAble(boolean canOperation) {
    controllable.setOperationAble(canOperation);
  }

}
