package com.mc.tool.framework.systemedit.datamodel;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;

/**
 * .
 */
public interface VisualEditTerminal extends VisualEditNode {
  String SUB_TERMINAL_CONNECTOR = "connect.to.subterminal";

  boolean isRx();

  boolean isTx();

  boolean isOnline();

  /**
   * 判断设备是否有效，如果是无效的话，表示可删除数据的.
   *
   * @return 如果有效，返回true.
   */
  boolean isValid();

  BooleanProperty onlineProperty();

  /**
   * 获取设备的分辨率.
   *
   * @return 分辨率
   */
  Resolution getResolution(int index);

  default Resolution getResolution() {
    return getResolution(0);
  }

  /**
   * 是否有连接外部设备，rx应该连接显示设备，tx应该连接视频源设备.
   *
   * @return 如果有连接，返回true
   */
  boolean isTargetDeviceConnected();

  default boolean isTargetDeviceConnected(int index) {
    return isTargetDeviceConnected();
  }

  BooleanProperty targetDeviceConnectedProperty();

  default BooleanProperty targetDeviceConnectedProperty(int index) {
    return targetDeviceConnectedProperty();
  }

  TargetDeviceType getTargetDeviceType();

  void setTargetDeviceType(TargetDeviceType type);

  ObjectProperty<TargetDeviceType> targetDeviceTypeProperty();

  NetworkInterfaceType getNetworkInterfaceType();

  void setNetworkInterfaceType(NetworkInterfaceType type);

  ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty();

  MultimediaInterfaceType getMultimediaInterfaceType();

  void setMultimediaInterfaceType(MultimediaInterfaceType type);

  ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty();

  /**
   * Can seperate to several sub terminal.
   *
   * @return if can, return true.
   */
  boolean canSeperate();

  void seperate(boolean sep);

}
