package com.mc.tool.framework.operation.videowall.menu;

import com.mc.graph.canvas.PageCanvasUtility;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.controller.VideoWallUtility;
import com.mc.tool.framework.operation.videowall.datamodel.IVideoWallLayout;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;
import javafx.geometry.Point2D;
import javafx.geometry.Rectangle2D;

/**
 * .
 */
public class MenuFitToScreen extends VideoWallMenuBase {

  public MenuFitToScreen(VideoWallControllable controllable) {
    super(controllable);
  }

  @Override
  protected void onAction() {
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    controllable.beginUpdate();
    try {
      for (VideoObject video : videos) {
        fitScreen(controllable.getVideoWallFunction().getVideoWallObject().getUserLayout(), video);
      }
    } finally {
      controllable.endUpdate();
    }

  }

  protected void fitScreen(IVideoWallLayout layoutData, VideoObject videoData) {
    int maxArea = 0;
    int maxRow = -1;
    int maxColumns = -1;
    Rectangle2D videoRect = new Rectangle2D(videoData.getXpos().get(), videoData.getYpos().get(),
        videoData.getWidth().get(), videoData.getHeight().get());
    for (int i = 0; i < layoutData.getRows(); i++) {
      for (int j = 0; j < layoutData.getColumns(); j++) {
        Rectangle2D screenRect = PageCanvasUtility.getGridRect(layoutData.getWidths(),
            layoutData.getHeights(), new Point2D(j, i));
        Rectangle2D intersection = VideoWallUtility.getIntersection(videoRect, screenRect);
        if (intersection == null) {
          continue;
        }
        int area = (int) (intersection.getWidth() * intersection.getHeight());
        if (area > maxArea) {
          maxArea = area;
          maxRow = i;
          maxColumns = j;
        }
      }
    }

    int minX;
    int minY;
    int maxX;
    int maxY;
    if (maxArea != 0) {
      Rectangle2D gridRect = PageCanvasUtility.getGridRect(layoutData.getWidths(),
          layoutData.getHeights(), new Point2D(maxColumns, maxRow));
      minX = (int) gridRect.getMinX();
      minY = (int) gridRect.getMinY();
      maxX = (int) gridRect.getMaxX();
      maxY = (int) gridRect.getMaxY();
    } else {
      minX = videoData.getXpos().get();
      if (minX < 0) {
        minX = 0;
      } else {
        int index = PageCanvasUtility.indexOfColumn(layoutData.getWidths(), minX);
        index = Math.min(index, layoutData.getColumns() - 1);
        minX = PageCanvasUtility.getColumnStartPos(layoutData.getWidths(), index);
      }

      minY = videoData.getYpos().get();
      if (minY < 0) {
        minY = 0;
      } else {
        int index = PageCanvasUtility.indexOfRow(layoutData.getHeights(), minY);
        index = Math.min(index, layoutData.getRows() - 1);
        minY = PageCanvasUtility.getColumnStartPos(layoutData.getHeights(), index);
      }

      maxX = minX + videoData.getWidth().get();
      if (maxX > layoutData.getTotalWidth()) {
        maxX = layoutData.getTotalWidth();
      } else {
        int index = PageCanvasUtility.indexOfColumn(layoutData.getWidths(), maxX - 1);
        index = Math.min(index, layoutData.getColumns() - 1);
        maxX = PageCanvasUtility.getColumnEndPos(layoutData.getWidths(), index);
      }

      maxY = minY + videoData.getHeight().get();
      if (maxY > layoutData.getTotalHeight()) {
        maxY = layoutData.getTotalHeight();
      } else {
        int index = PageCanvasUtility.indexOfRow(layoutData.getHeights(), maxY - 1);
        index = Math.min(index, layoutData.getRows() - 1);
        maxY = PageCanvasUtility.getColumnEndPos(layoutData.getHeights(), index);
      }
    }

    videoData.getXpos().set(minX);
    videoData.getYpos().set(minY);
    videoData.getWidth().set(maxX - minX);
    videoData.getHeight().set(maxY - minY);
  }

  @Override
  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.fit_screen");
  }

}
