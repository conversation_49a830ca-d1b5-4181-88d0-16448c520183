package com.mc.tool.framework.operation.seat.view;

import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import com.mc.tool.framework.operation.seat.controller.SeatControllable;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatMode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.ObjectBinding;
import javafx.beans.property.ObjectProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Side;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.image.Image;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.GridCell;

/**
 * .
 */
@Slf4j
public final class SeatCell extends GridCell<SeatConnection> implements Initializable {
  @FXML
  private VBox root;
  @FXML
  private Label screenPic;
  @FXML
  private Label screenName;

  private final SeatControllable controllable;
  private final SnapshotGetter snapshotGetter;

  private static final String BG_PREFIX = "com/mc/tool/framework/operation/seat/";
  private static final String SEAT_DISCONNECT = BG_PREFIX + "seat_disconnect.png";
  private static final String SEAT_VIDEO = BG_PREFIX + "seat_video.png";
  private static final String SEAT_FULL = BG_PREFIX + "seat_full.png";
  private static final int SNAPSHOT_X = 8;
  private static final int SNAPSHOT_Y = 6;
  private static final int SNAPSHOT_WIDTH = 195;
  private static final int SNAPSHOT_HEIGHT = 114;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public SeatCell(SeatControllable controllable, SnapshotGetter snapshotGetter) {
    this.controllable = controllable;
    this.snapshotGetter = snapshotGetter;
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/operation/seat/seat_cell.fxml"));
    loader.setController(this);

    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load seat_cell.fxml", exception);
    }
  }

  @Override
  protected void updateItem(SeatConnection item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
      screenPic.styleProperty().unbind();
      screenName.textProperty().unbind();
    } else {
      setGraphic(root);
      screenName.textProperty().bind(item.getNameWithSource());

      ObjectProperty<Image> image = snapshotGetter.getSnapshot(item.getTx());
      ObjectBinding<Background> bgBinding = Bindings.createObjectBinding(() -> {
        String screenImage;
        switch (getItem().getMode().get()) {
          case DISCONNECT:
            screenImage = SEAT_DISCONNECT;
            break;
          case VIDEO:
            screenImage = SEAT_VIDEO;
            break;
          case FULL:
            screenImage = SEAT_FULL;
            break;
          default:
            screenImage = "";
            break;
        }
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        BackgroundImage screenImageBg = null;
        if (!screenImage.isEmpty()) {
          Image screenImageInstance = new Image(classLoader.getResourceAsStream(screenImage));
          screenImageBg = new BackgroundImage(screenImageInstance, BackgroundRepeat.NO_REPEAT,
              BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER,
              new BackgroundSize(1, 1, true, true, false, false));
        }

        BackgroundImage snapshotImageBg = null;
        if (image.get() != null) {
          snapshotImageBg = new BackgroundImage(image.get(), BackgroundRepeat.NO_REPEAT,
              BackgroundRepeat.NO_REPEAT,
              new BackgroundPosition(Side.LEFT, SNAPSHOT_X, false, Side.TOP, SNAPSHOT_Y, false),
              new BackgroundSize(SNAPSHOT_WIDTH, SNAPSHOT_HEIGHT, false, false, false, false));
        }
        return new Background(screenImageBg, snapshotImageBg);
      }, image, item.getTx(), getItem().getMode());
      screenPic.backgroundProperty().bind(bgBinding);

    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    root.setOnDragOver((event) -> {
      boolean connectable = controllable.getDeviceController() == null
          || controllable.getDeviceController().getUserRight().isSeatConnectable();

      if (event.getDragboard().hasString() && connectable) {
        event.acceptTransferModes(TransferMode.ANY);
      }
      event.consume();
    });

    root.setOnDragDropped((event) -> {
      event.setDropCompleted(true);
      String guid = event.getDragboard().getString();
      VisualEditTerminal terminal = controllable.getTxByGuid(guid);
      boolean connectable = controllable.getDeviceController() == null
          || controllable.getDeviceController().getUserRight().isSeatConnectable();
      if (connectable) {
        controllable.beginUpdate();
        if (terminal == null) {
          getItem().getTx().set(null);
          getItem().getMode().set(SeatMode.DISCONNECT);
        } else {
          getItem().getTx().set(terminal);
          getItem().getMode().set(SeatMode.VIDEO);
        }
        controllable.endUpdate();
      }

      event.consume();
    });

    Tooltip tooltip = new Tooltip();
    tooltip.textProperty().bind(screenName.textProperty());
    screenName.setTooltip(tooltip);
  }
}
