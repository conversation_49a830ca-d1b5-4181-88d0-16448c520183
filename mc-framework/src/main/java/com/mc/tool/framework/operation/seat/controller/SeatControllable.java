package com.mc.tool.framework.operation.seat.controller;

import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.operation.seat.datamodel.SeatData;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;

/**
 * .
 */
public interface SeatControllable extends OperationControllable {
  void init(VisualEditModel model, VisualEditFunc currentFunction);

  /**
   * 开始更新视频墙数据，必须跟endUpdate配合使用.
   */
  void beginUpdate();

  /**
   * 结束更新连接数据，如果在更新期间确实发生了数据改变，那么发送连接数据.
   */
  void endUpdate();

  VisualEditTerminal getTxByGuid(String guid);

  /**
   * 激活预案.
   *
   * @param scenario 预案数据.
   */
  void activeScenario(SeatData scenario);

  ObjectProperty<SeatData> currentScenarioProperty();

  /**
   * 是否有此预案.
   *
   * @param scenario 预案
   * @return 如果有，返回true
   */
  boolean hasScenario(SeatData scenario);

  /**
   * 删除预案.
   *
   * @param scenario 预案
   */
  void deleteScenario(SeatData scenario);
}
