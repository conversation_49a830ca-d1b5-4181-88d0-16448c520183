package com.mc.tool.framework.operation.videowall.graph;

import com.mc.common.util.WeakBinder;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import com.mc.tool.framework.operation.videowall.controller.VideoWallConstants;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.systemedit.view.AbstractCellSkinEx;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.StringBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.BorderStrokeStyle;
import javafx.scene.layout.BorderWidths;
import javafx.scene.layout.CornerRadii;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.shape.StrokeLineCap;
import javafx.scene.shape.StrokeLineJoin;
import javafx.scene.shape.StrokeType;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VideoCellSkin extends AbstractCellSkinEx {
  private HBox pane;
  private Label nameLabel;
  private SnapshotGetter snapshotGetter;
  private WeakBinder weakBinder = null;
  private ObjectProperty<Image> image = null;

  private List<Runnable> cleaner = new ArrayList<>();

  public VideoCellSkin(CellObject cellobject, Parent parent, Parent container,
                       SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  protected WeakBinder getWeakBinder() {
    if (weakBinder == null) {
      weakBinder = new WeakBinder();
    }
    return weakBinder;
  }

  @Override
  public Region getRegion() {
    return pane;
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return Collections.emptyList();
  }

  @Override
  public Color getSelectionBorderColor() {
    return Color.rgb(0xf0, 0x85, 0x19);
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public boolean isResizeble() {
    return true;
  }

  @Override
  protected Label getNameLabel() {
    return null;
  }

  @Override
  protected Node[] getNameLabelChangeSource() {
    return new Node[0];
  }

  @Override
  protected void initInner() {

  }

  @Override
  protected void initRegion() {
    pane = new HBox();
    pane.setPadding(new Insets(10));
    pane.setMinWidth(2);
    pane.setMinHeight(2);
    nameLabel = new Label();
    nameLabel.setMinHeight(0);
    nameLabel.setMinWidth(0);
    nameLabel.setMouseTransparent(true);
    pane.getChildren().add(nameLabel);
    pane.setUserData(this);
    Tooltip tooltip = new Tooltip();
    tooltip.textProperty().bind(nameLabel.textProperty());
    Tooltip.install(pane, tooltip);
    updateBinding();
    getCell().getBindedObjectProperty()
        .addListener(weakAdapter.wrap((observable, oldVal, newVal) -> {
          updateBinding();
        }));
  }

  protected void updateBinding() {
    if (getCell().getBindedObject() == null) {
      return;
    }
    if (!(getCell().getBindedObject() instanceof VideoObject)) {
      log.warn("Error binded object");
      return;
    }
    getWeakBinder().unbindAll();
    VideoObject data = (VideoObject) getCell().getBindedObject();
    BgColorBinding binding = new BgColorBinding(data);
    getWeakBinder().bind(pane.styleProperty(), new SimpleStringProperty(
        "-fx-background-color:" + VideoWallConstants.VIDEO_BORER_COLOR + ", ").concat(binding)
        .concat("; -fx-background-insets:0, 3;"));
    setSnapshotGetter(snapshotGetter);
    getWeakBinder().bind(nameLabel.textProperty(), data.getNameWithSource());
  }

  protected void setSnapshotGetter(SnapshotGetter getter) {
    this.snapshotGetter = getter;
    if (!(getCell().getBindedObject() instanceof VideoObject)) {
      return;
    }
    VideoObject data = (VideoObject) getCell().getBindedObject();
    if (snapshotGetter != null) {
      if (image != null) {
        image.unbind();
      }
      image = snapshotGetter.getSnapshot(data);
      if (image == null) {
        log.warn("Snapshot property is null!");
      } else {
        getWeakBinder().bind(pane.backgroundProperty(), Bindings.createObjectBinding(() -> {
          if (image.get() == null) {
            BackgroundFill[] fills = new BackgroundFill[2];
            //边框
            fills[0] = new BackgroundFill(Color.web(VideoWallConstants.VIDEO_BORER_COLOR), null,
                new Insets(0));
            //背景色
            fills[1] = new BackgroundFill(Color.web(data.getSource().get() == null
                ? VideoWallConstants.EMPTY_VIDEO_COLOR : VideoWallConstants.NORMAL_VIDEO_COLOR),
                null, new Insets(3));
            return new Background(fills);
          } else {
            return new Background(new BackgroundImage(image.get(), BackgroundRepeat.NO_REPEAT,
                BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER,
                new BackgroundSize(1, 1, true, true, false, false)));
          }
        }, image, data.getSource()));
      }
    }
  }

  @Override
  protected void onConnectorChange() {

  }

  @Override
  protected void layoutConnectors() {

  }

  @Override
  public BorderStroke getSelectedBorderStroke() {
    BorderStrokeStyle style = new BorderStrokeStyle(StrokeType.INSIDE, StrokeLineJoin.MITER,
        StrokeLineCap.BUTT, 10, 0, null);
    return new BorderStroke(getSelectionBorderColor(), style, new CornerRadii(3),
        new BorderWidths(2));
  }

  @Override
  public void destroy() {
    super.destroy();
    if (image != null) {
      image.unbind();
    }
    getWeakBinder().unbindAll();

    for (Runnable runnable : cleaner) {
      runnable.run();
    }
    cleaner.clear();
  }

  /**
   * 添加销毁时要做的事情.
   *
   * @param runnable runnable
   */
  public void addCleaner(Runnable runnable) {
    cleaner.add(runnable);
  }

  static class BgColorBinding extends StringBinding {
    private final VideoObject videoData;

    public BgColorBinding(VideoObject data) {
      this.videoData = data;
      this.bind(videoData.getSource());
    }

    @Override
    protected String computeValue() {
      return videoData.getSource().get() == null ? VideoWallConstants.EMPTY_VIDEO_COLOR
          : VideoWallConstants.NORMAL_VIDEO_COLOR;
    }

  }

}
