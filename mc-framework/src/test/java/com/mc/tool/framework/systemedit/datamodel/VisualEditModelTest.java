package com.mc.tool.framework.systemedit.datamodel;

import static org.junit.Assert.assertEquals;

import com.google.common.collect.BiMap;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.graph.GraphAdapterBuilder;
import com.google.gson.typeadapters.BimapCreator;
import com.google.gson.typeadapters.RuntimeTypeAdapterFactory;
import com.mc.graph.connector.AnomymousConnectorObject;
import com.mc.graph.connector.IntegerConnectorIdentifier;
import com.mc.graph.connector.StringConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.framework.systemedit.controller.SystemEditPageController;
import org.hildan.fxgson.FxGsonBuilder;
import org.junit.Test;

public class VisualEditModelTest {

  @Test
  public void test() {
    RuntimeTypeAdapterFactory<VisualEditNode> nodeFactory = RuntimeTypeAdapterFactory.of(VisualEditNode.class)
        .registerSubtype(DefaultVisualEditMatrix.class)
        .registerSubtype(DefaultVisualEditTerminal.class)
        .registerSubtype(InnerVisualEditGroup.class)
        .registerSubtype(VisualEditGroup.class);

    RuntimeTypeAdapterFactory<ConnectorIdentifier> connectorFactory = RuntimeTypeAdapterFactory.of(ConnectorIdentifier.class)
        .registerSubtype(IntegerConnectorIdentifier.class)
        .registerSubtype(StringConnectorIdentifier.class)
        .registerSubtype(AnomymousConnectorObject.class);
    String string = "";
    VisualEditModel model = new VisualEditModel();

    {
      GsonBuilder builder = new FxGsonBuilder().builder()
          .excludeFieldsWithoutExposeAnnotation()
          .setPrettyPrinting()
          .enableComplexMapKeySerialization();
      builder = builder.registerTypeAdapterFactory(connectorFactory);
      new GraphAdapterBuilder()
          .addDelegateAdapterFactory(nodeFactory)
          .addType(VisualEditNode.class)
          .addType(VisualEditTerminal.class)
          .addType(VisualEditMatrix.class)
          .addType(DefaultVisualEditMatrix.class)
          .addType(DefaultVisualEditTerminal.class)
          .addType(VisualEditGroup.class)
          .addType(VisualEditModel.class)
          .registerOn(builder);

      Gson gson = builder.create();


      SystemEditPageController.createTestData(model);
      string = gson.toJson(model);
      System.out.println(string);
    }

    {

      GsonBuilder builder = new FxGsonBuilder().builder().excludeFieldsWithoutExposeAnnotation().setPrettyPrinting();
      builder = builder.registerTypeAdapterFactory(connectorFactory);
      new GraphAdapterBuilder()
          .addDelegateAdapterFactory(nodeFactory)
          .addType(VisualEditNode.class)
          .addType(VisualEditTerminal.class)
          .addType(VisualEditMatrix.class)
          .addType(DefaultVisualEditMatrix.class)
          .addType(DefaultVisualEditTerminal.class)
          .addType(VisualEditGroup.class)
          .addType(InnerVisualEditGroup.class)
          .addType(VisualEditModel.class)
          .registerOn(builder);
      builder.registerTypeAdapter(BiMap.class, new BimapCreator());
      Gson gson = builder.create();
      VisualEditModel newModel = gson.fromJson(string, VisualEditModel.class);
      assertEquals(model.getRoots().size(), newModel.getRoots().size());
    }
  }


}
