package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.interfaces.ChangedEvent;
import com.mc.tool.caesar.api.interfaces.ChangedListener;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.interfaces.Threshold;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeSet;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.swing.event.EventListenerList;

/**
 * .
 */
public class DefaultCommitRollback implements CommitRollback {

  private static final Logger LOG = Logger.getLogger(DefaultCommitRollback.class.getName());
  private Map<PropertyObject, PropertyObject> rollbackStorage = Collections.emptyMap();
  private EventListenerList eventListenerList = null;
  private ChangedListener depChangedListener = null;
  private Collection<CommitRollback> changedObjects = Collections.emptyList();

  private static Lock lock = new ReentrantLock();

  protected final void initCommitRollback() {
    ChangedListener cl;
    lock.lock();
    try {
      Collection<? extends CommitRollback> dependentCommitRollbacks = getDependentCommitRollbacks();
      if (!dependentCommitRollbacks.isEmpty()) {
        cl = getChangedListener();
        for (CommitRollback commitRollback : dependentCommitRollbacks) {
          if (null != commitRollback) {
            commitRollback.removeChangedListener(cl);
            commitRollback.addChangedListener(cl);
          }
        }
      }
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Init fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  protected ChangedListener getChangedListener() {
    lock.lock();
    try {
      if (null == this.depChangedListener) {
        this.depChangedListener = changedEvent -> {
          CommitRollback commitRollback = changedEvent.getCommitRollback();
          if (commitRollback.isChanged()) {
            this.addChangedCr(commitRollback);
          } else {
            this.removeChangedCr(commitRollback);
          }
          this.fireChanged();
        };
      }
      return this.depChangedListener;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Get changed listener fail!", exception);
      return this.depChangedListener;
    } finally {
      lock.unlock();
    }
  }

  private void addChangedCr(CommitRollback commitRollback) {
    lock.lock();
    try {
      if (null == commitRollback) {
        return;
      }
      if (this.changedObjects.isEmpty()) {
        this.changedObjects = new HashSet<>();
      }
      this.changedObjects.add(commitRollback);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "add changed cr fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  private void removeChangedCr(CommitRollback commitRollback) {
    lock.lock();
    try {
      if (null == commitRollback) {
        return;
      }
      this.changedObjects.remove(commitRollback);
      if (this.changedObjects.isEmpty()) {
        this.changedObjects = Collections.emptyList();
      }
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "remove changed cr fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void commit() {
    lock.lock();
    try {
      commit(Threshold.ALL);
    } finally {
      lock.unlock();
    }
  }

  /**
   * .
   *
   * @param threshold 提交的阈值 如果阈值比修改时的大，忽略 如果阈值等于Threshold.ALL，就提交修改 如果阈值小于小于修改的阈值，那么会把修改的阈值降低到
   *                  threshold - 1
   * @brief 提交修改
   */
  @Override
  public final void commit(Threshold threshold) {
    lock.lock();
    try {
      Iterator<PropertyObject> iterator = this.rollbackStorage.keySet().iterator();
      while (iterator.hasNext()) {
        PropertyObject propertyObject = iterator.next();
        if (propertyObject.isChanged(threshold)) {
          propertyObject.commit(threshold);
          if (!propertyObject.isChanged()) {
            iterator.remove();
          }
        }
      }
      if (this.rollbackStorage.isEmpty()) {
        this.rollbackStorage = Collections.emptyMap();
        removeChangedCr(this);
      }
      Collection<CommitRollback> others = new HashSet<>(this.changedObjects);

      others.remove(this);
      for (CommitRollback dependant : others) {
        dependant.commit(threshold);
      }
      fireChanged();
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Commit fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void commitProperty(String propertyName, int... indizes) {
    lock.lock();
    try {
      commitProperty(Threshold.ALL, propertyName, indizes);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void commitProperty(Threshold threshold, String propertyName, int... indizes) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.getProperty().equals(propertyName)
            && Arrays.equals(propertyObject.getIndizes(), indizes)
            && propertyObject.isChanged(threshold)) {
          propertyObject.commit(threshold);
          if (propertyObject.isChanged()) {
            break;
          }
          this.rollbackStorage.remove(propertyObject);
        }
      }
      if (this.rollbackStorage.isEmpty()) {
        this.rollbackStorage = Collections.emptyMap();
        removeChangedCr(this);
      }
      fireChanged();
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Commit Property fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void rollback() {
    lock.lock();
    try {
      rollback(Threshold.ALL);
    } finally {
      lock.unlock();
    }
  }

  /**
   * .
   *
   * @param threshold threshold
   * @brief 回滚
   * @note 回滚操作完成后，会commit修改
   */
  @Override
  public final void rollback(Threshold threshold) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : new ArrayList<>(
          this.rollbackStorage.keySet())) {
        if (propertyObject.isChanged(threshold)) {
          rollbackImpl(propertyObject.getProperty(), propertyObject.getIndizes(),
              propertyObject.getValue(threshold), threshold);
        }
      }
      if (this.rollbackStorage.isEmpty()) {
        this.rollbackStorage = Collections.emptyMap();
      }
      for (CommitRollback dependant : getDependentCommitRollbacks()) {
        dependant.rollback(threshold);
      }
      commit(threshold);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Roll back fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void rollbackProperty(String propertyName, int... indizes) {
    lock.lock();
    try {
      rollbackProperty(Threshold.ALL, propertyName, indizes);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void rollbackProperty(Threshold threshold, String propertyName, int... indizes) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.getProperty().equals(propertyName)
            && Arrays.equals(propertyObject.getIndizes(), indizes)
            && propertyObject.isChanged(threshold)) {
          rollbackImpl(propertyObject.getProperty(), propertyObject.getIndizes(),
              propertyObject.getValue(threshold), threshold);
          break;
        }
      }
      if (this.rollbackStorage.isEmpty()) {
        this.rollbackStorage = Collections.emptyMap();
      }
      commitProperty(threshold, propertyName, indizes);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Roll back property fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
    return Collections.emptyList();
  }

  @Override
  public final boolean isChanged() {
    DefaultCommitRollback defaultCommitRollback = this;
    synchronized (defaultCommitRollback) {
      return this.isChanged(Threshold.ALL);
    }
  }

  @Override
  public final boolean isChanged(Threshold threshold) {
    lock.lock();
    try {
      if (Threshold.ALL.getThreshold() == threshold.getThreshold()
          && !this.changedObjects.isEmpty()) {
        return true;
      }
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.isChanged(threshold)) {
          return true;
        }
      }
      for (CommitRollback commitRollback : this.changedObjects) {
        if (!commitRollback.equals(this) && commitRollback.isChanged(threshold)) {
          return true;
        }
      }
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Check is changed fail!", exception);
      return false;
    } finally {
      lock.unlock();
    }
  }

  @Override
  public boolean equals(Threshold threshold) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.equals(threshold)) {
          return true;
        }
      }
      for (CommitRollback commitRollback : this.changedObjects) {
        if (!commitRollback.equals(this) && commitRollback.equals(threshold)) {
          return true;
        }
      }
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Check equals fail!", exception);
      return false;
    } finally {
      lock.unlock();
    }
  }

  /* Error */
  @Override
  public final boolean isPropertyChanged(String propertyName, int... indizes) {
    DefaultCommitRollback defaultCommitRollback = this;
    synchronized (defaultCommitRollback) {
      return this.isPropertyChanged(Threshold.ALL, propertyName, indizes);
    }
  }

  @Override
  public final boolean isPropertyChanged(Threshold threshold, String propertyName, int... indizes) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.getProperty().equals(propertyName)
            && Arrays.equals(propertyObject.getIndizes(), indizes)) {
          return propertyObject.isChanged(threshold);
        }
      }
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Check is property changed fail!", exception);
      return false;
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final boolean propertyEquals(Threshold threshold, String propertyName, int... indizes) {
    lock.lock();
    try {
      for (PropertyObject propertyObject : this.rollbackStorage.keySet()) {
        if (propertyObject.getProperty().equals(propertyName)
            && Arrays.equals(propertyObject.getIndizes(), indizes)) {
          return propertyObject.equals(threshold);
        }
      }
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Check property equals fail!", exception);
      return false;
    } finally {
      lock.unlock();
    }
  }

  protected void rollbackImpl(String propertyName, int[] indizes, Object value,
      Threshold treshold) {
    LOG.log(Level.CONFIG, "Ingnoring rollback of {0}",
        propertyName + Arrays.toString(indizes) + " to the value of " + value);
  }

  private void fireChanged() {
    lock.lock();
    try {
      if (null != this.eventListenerList) {
        ChangedEvent changedEvent = new ChangedEvent(this);
        for (ChangedListener changedListener : this.eventListenerList
            .getListeners(ChangedListener.class)) {
          try {
            changedListener.handleChanged(changedEvent);
          } catch (Throwable throwable) {
            LOG.log(Level.SEVERE, "Error notifying of changed state.", throwable);
          }
        }
      }
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "firechanged fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void addChangedListener(ChangedListener changeListener) {
    lock.lock();
    try {
      if (null == this.eventListenerList) {
        this.eventListenerList = new EventListenerList();
      }
      this.eventListenerList.remove(ChangedListener.class, changeListener);
      this.eventListenerList.add(ChangedListener.class, changeListener);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "add changed listener fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  @Override
  public final void removeChangedListener(ChangedListener changeListener) {
    lock.lock();
    try {
      if (null == this.eventListenerList) {
        return;
      }
      this.eventListenerList.remove(ChangedListener.class, changeListener);
      if (0 == this.eventListenerList.getListenerCount()) {
        this.eventListenerList = null;
      }
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Remove changed listener fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  protected final void addToRollBack(Threshold threshold, String propertyName, Object oldValue,
      Object newValue, int... indizes) {
    lock.lock();
    try {
      if (objectsEqual(oldValue, newValue)) {
        return;
      }
      PropertyObject newPo = new PropertyObject(propertyName, indizes);
      PropertyObject oldPo = this.rollbackStorage.get(newPo);
      if (null == oldPo) {
        if (this.rollbackStorage.isEmpty()) {
          this.rollbackStorage = new HashMap<>();
        }
        this.rollbackStorage.put(newPo, newPo);
      } else {
        newPo = oldPo;
      }
      if (newPo.isChanged(threshold) && objectsEqual(newPo.getValue(threshold), newValue)) {
        newPo.removeValues(threshold);
        if (!newPo.isChanged(Threshold.UI_ALL)) {
          this.rollbackStorage.remove(newPo);
        }
      } else {
        newPo.addValue(threshold, oldValue);
      }
      if (this.rollbackStorage.isEmpty()) {
        this.rollbackStorage = Collections.emptyMap();
        removeChangedCr(this);
      } else {
        addChangedCr(this);
      }
      fireChanged();
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Add to roll back fail!", exception);
    } finally {
      lock.unlock();
    }
  }

  private boolean objectsEqual(Object left, Object right) {
    if (null == left) {
      return null == right;
    }
    if (left.getClass().isArray()) {
      return Arrays.deepEquals(new Object[]{left}, new Object[]{right});
    }
    return left.equals(right);
  }

  final Collection<CommitRollback> getChangedObjects() {
    return new HashSet<>(this.changedObjects);
  }

  final Collection<PropertyObject> getRollbackStorage() {
    return new TreeSet<>(this.rollbackStorage.keySet());
  }
}

