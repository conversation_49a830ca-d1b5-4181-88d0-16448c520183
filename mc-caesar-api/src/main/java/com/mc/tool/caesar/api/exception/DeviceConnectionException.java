package com.mc.tool.caesar.api.exception;

import java.io.IOException;

/**
 * .
 */
public class DeviceConnectionException extends IOException {

  private static final long serialVersionUID = -658240988174577774L;

  public DeviceConnectionException(String message, Throwable cause) {
    super(message, cause);
  }

  public DeviceConnectionException(String message) {
    super(message);
  }

  public DeviceConnectionException(Throwable cause) {
    super(cause);
  }

  public DeviceConnectionException() {

  }
}

