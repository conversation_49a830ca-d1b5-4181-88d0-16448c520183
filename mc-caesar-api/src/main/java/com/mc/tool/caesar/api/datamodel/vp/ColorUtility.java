package com.mc.tool.caesar.api.datamodel.vp;

import javafx.scene.paint.Color;

/**
 * .
 */
public class ColorUtility {

  public static Color fromeRgb(int rgb) {
    return Color.rgb((rgb >> 16) & 0xff, (rgb >> 8) & 0xff, rgb & 0xff);
  }

  public static int toRgb(Color color) {
    return ((int) (color.getRed() * 255) << 16) + ((int) (color.getGreen() * 255) << 8)
        + ((int) (color.getBlue() * 255));
  }
}
