package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.DefaultCommitRollback;
import java.util.Objects;

/**
 * .
 */
public class VideoData extends DefaultCommitRollback implements DataObject, Cloneable {

  private CpuData cpuData;
  private int id;
  private static final String namePrefix = "VIDEO_";
  public static final String PROPERTY_X = "VideoData.x";
  public static final String PROPERTY_Y = "VideoData.y";
  public static final String PROPERTY_WIDTH = "VideoData.width";
  public static final String PROPERTY_HEIGHT = "VideoData.height";
  public static final String PROPERTY_ALPHA = "VideoData.alpha";
  public static final String PROPERTY_NAME = "VideoData.name";
  public static final String PROPERTY_CPU = "Video.CPU";

  public static final int MAX_WIDTH_HEIGHT = 1000000;

  private String name;
  private double alpha = 1; // 透明度
  private int x0 = 0;
  private int y0 = 0;
  private int width = 1920;
  private int height = 1080;
  private CustomPropertyChangeSupport pcs;

  private VideoData(CpuData data, int id) {
    cpuData = data;
    this.id = id;
  }

  /**
   * .
   */
  public static VideoData createVideoData(CpuData data, int id) {
    VideoData videoData = new VideoData(data, id);
    videoData.setName(namePrefix + String.format("%03d", videoData.getId()));
    return videoData;
  }

  /**
   * 复制，只能在撤销恢复时备份时使用.
   */
  @Override
  public VideoData clone() {
    try {
      super.clone();
    } catch (CloneNotSupportedException ex) {
      ex.printStackTrace();
    }
    VideoData videoData = new VideoData(cpuData, id);
    videoData.alpha = alpha;
    videoData.height = height;
    videoData.width = width;
    videoData.x0 = x0;
    videoData.y0 = y0;
    videoData.name = name;
    videoData.pcs = pcs;
    return videoData;
  }

  public void setPropertyChangeSupport(CustomPropertyChangeSupport pcs) {
    this.pcs = pcs;
  }


  public CpuData getBindingCpu() {
    return cpuData;
  }

  /**
   * .
   */
  public void setBindingCpu(CpuData cpuData) {
    if (this.cpuData == cpuData) {
      return;
    }
    this.cpuData = cpuData;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_CPU, null, this);
    }
  }

  @Override
  public String getName() {
    return name;
  }

  /**
   * .
   */
  public void setName(String name) {
    if (name == null) {
      name = "";
    }
    if (name.length() > 16) {
      name = name.substring(0, 16);
    }
    if (name.isEmpty() || Objects.equals(this.name, name)) {
      return;
    }
    this.name = name;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_NAME, null, this);
    }
  }

  @Override
  public int getId() {
    // TODO Auto-generated method stub
    return id;
  }

  @Override
  public void delete() {
    delete(true);
  }

  @Override
  public void delete(boolean paramBoolean) {
    cpuData = null;
    pcs = null;
  }

  public double getAlpha() {
    return alpha;
  }

  /**
   * .
   */
  public void setAlpha(double alpha) {
    alpha = ((int) (alpha * 100 + 0.5)) / 100.0; // 保留两位小数点
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }

    if (Math.abs(this.alpha - alpha) < .0000001) {
      return;
    }
    this.alpha = alpha;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_ALPHA, null, this);
    }
  }

  public int getX() {
    return x0;
  }

  /**
   * .
   */
  public void setX(int xx) {
    if (this.x0 == xx) {
      return;
    }
    this.x0 = xx;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_X, null, this);
    }
  }

  public int getY() {
    return y0;
  }

  /**
   * .
   */
  public void setY(int yy) {
    if (this.y0 == yy) {
      return;
    }
    this.y0 = yy;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_Y, null, this);
    }
  }

  public int getWidth() {
    return width;
  }

  /**
   * .
   */
  public void setWidth(int width) {
    if (width < 0) {
      width = 0;
    } else if (width > MAX_WIDTH_HEIGHT) {
      width = MAX_WIDTH_HEIGHT;
    }
    if (this.width == width) {
      return;
    }
    this.width = width;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_WIDTH, null, this);
    }
  }

  public int getHeight() {
    return height;
  }

  /**
   * .
   */
  public void setHeight(int height) {
    if (height <= 0) {
      height = 0;
    } else if (height > MAX_WIDTH_HEIGHT) {
      height = MAX_WIDTH_HEIGHT;
    }
    if (this.height == height) {
      return;
    }
    this.height = height;
    if (pcs != null) {
      pcs.firePropertyChange(PROPERTY_HEIGHT, null, this);
    }
  }
}
