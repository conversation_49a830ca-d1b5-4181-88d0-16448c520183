package com.mc.tool.caesar.api.interfaces;

import com.google.gson.annotations.Expose;

/**
 * .
 */
public final class Threshold {

  public static final Threshold ALL = new Threshold(Integer.MIN_VALUE);
  public static final Threshold UI_ALL = new Threshold(Integer.MIN_VALUE);
  public static final Threshold ONE = new Threshold(1);
  public static final Threshold TWO = new Threshold(2);
  public static final Threshold THREE = new Threshold(3);
  public static final Threshold FOUR = new Threshold(4);
  public static final Threshold FIVE = new Threshold(5);
  public static final Threshold SIX = new Threshold(6);
  public static final Threshold SEVEN = new Threshold(7);
  public static final Threshold EIGHT = new Threshold(8);
  public static final Threshold NINE = new Threshold(9);
  public static final Threshold TEN = new Threshold(10);
  public static final Threshold CONTROLLER = THREE;
  @Expose
  private final int threshold;

  public Threshold(int threshold) {
    this.threshold = threshold;
  }

  public int getThreshold() {
    return this.threshold;
  }

  @Override
  public boolean equals(Object obj) {
    return obj instanceof Threshold && this.threshold == Threshold.class.cast(obj).threshold;
  }

  @Override
  public int hashCode() {
    int hash = 3;
    hash = 37 * hash + this.threshold;
    return hash;
  }

  @Override
  public String toString() {
    return "Threshold{threshold=" + this.threshold + '}';
  }
}

