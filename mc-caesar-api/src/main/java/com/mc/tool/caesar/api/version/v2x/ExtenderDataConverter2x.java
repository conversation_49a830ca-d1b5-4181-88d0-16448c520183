package com.mc.tool.caesar.api.version.v2x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class ExtenderDataConverter2x implements ApiDataConverter<ExtenderData> {

  private final int dataSize;

  public ExtenderDataConverter2x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(ExtenderData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readInteger();
    data.setStatus(status);
    int port = cfgReader.read2ByteValue();
    data.setPort(port);

    int remotePort = cfgReader.read2ByteValue();
    data.setRemotePort(remotePort);

    int rdport = cfgReader.read2ByteValue();
    data.setRdPort(rdport);

    int remoteRdPort = cfgReader.read2ByteValue();
    data.setRdRemotePort(remoteRdPort);

    int id = cfgReader.readInteger();
    data.setId(id);
    int type = cfgReader.readInteger();
    data.setType(type);
    int cpuCon = cfgReader.read2ByteValue();
    data.setCpuCon(cpuCon);

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reservedSize = dataSize - readedSize;
    cfgReader.readByteArray(reservedSize);
  }

  @Override
  public void writeData(ExtenderData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getPort());
    cfgWriter.write2ByteSmallEndian(data.getRemotePort());
    cfgWriter.write2ByteSmallEndian(data.getRdPort());
    cfgWriter.write2ByteSmallEndian(data.getRdRemotePort());
    cfgWriter.writeInteger(data.getId());
    cfgWriter.writeInteger(data.getType());
    cfgWriter.write2ByteSmallEndian(data.getCpuCon());

    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
