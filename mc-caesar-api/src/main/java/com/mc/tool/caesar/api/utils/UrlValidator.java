package com.mc.tool.caesar.api.utils;

import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * url验证.
 */
public final class UrlValidator {

  private Matcher matcher;
  private final Protocol protocol;

  /**
   * .
   */
  public enum Protocol {
    HTTP, RTSP, FTP;

    Protocol() {

    }
  }

  /**
   * UrlValidator.
   */
  public UrlValidator(String url, Protocol protocol) {
    this.protocol = protocol;
    Pattern pt;
    if (protocol == Protocol.FTP) {
      pt = Pattern.compile("^(?:" + protocol.name().toLowerCase(Locale.SIMPLIFIED_CHINESE)
          + ":\\/\\/)(?:(\\S+)\\:(\\S+)\\@)?((?:[0-9]{1,3}\\.)"
          + "{3}[0-9]{1,3})(?:\\:(\\d+))?(\\/(?:\\S+)?)?$");
    } else {
      pt = Pattern.compile("^(?:" + protocol.name().toLowerCase(Locale.SIMPLIFIED_CHINESE)
          + ":\\/\\/)((?:[0-9]{1,3}\\.){3}[0-9]{1,3})(?:\\:(\\d+))?(\\/(?:\\S+)?)?$");
    }
    if (url != null && !url.isEmpty()) {
      this.matcher = pt.matcher(url);
    }
  }

  /**
   * getHostname.
   */
  public String getHostname() {
    if (this.protocol == Protocol.FTP) {
      return this.matcher != null ? this.matcher.group(3) : null;
    }
    return this.matcher != null ? this.matcher.group(1) : null;
  }

  /**
   * getPort.
   */
  public int getPort() {
    if (this.matcher != null) {
      if (this.protocol == Protocol.FTP) {
        String port = this.matcher.group(4);
        if (port == null) {
          return -1;
        }
        return Integer.parseInt(port);
      }
      String port = this.matcher.group(2);
      if (port == null) {
        return -1;
      }
      return Integer.parseInt(port);
    }
    return -1;
  }

  /**
   * getPath.
   */
  public String getPath() {
    if (this.protocol == Protocol.FTP) {
      return this.matcher != null ? this.matcher.group(5) : null;
    }
    return this.matcher != null ? this.matcher.group(3) : null;
  }

  public boolean isValid() {
    //return this.matcher != null ? this.matcher.matches() : false;
    return this.matcher != null && this.matcher.matches();
  }
}

