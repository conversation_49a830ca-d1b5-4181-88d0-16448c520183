package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import java.beans.IndexedPropertyChangeEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/**
 * .
 */
public class UserGroupData extends AbstractData
    implements DataObject, AccessControlObject, FavoriteObject, CaesarCommunicatable {

  public static final String PROPERTY_BASE = "UserGroupData.";
  public static final String FIELD_VIDEO_ACCESS = "VideoAccess";
  public static final String PROPERTY_VIDEO_ACCESS = "UserGroupData.VideoAccess";
  public static final String FIELD_NO_ACCESS = "NoAccess";
  public static final String PROPERTY_NO_ACCESS = "UserGroupData.NoAccess";
  public static final String FIELD_ID = "ID";
  public static final String PROPERTY_ID = "UserGroupData.ID";
  public static final String FIELD_NAME = "Name";
  public static final String PROPERTY_NAME = "UserGroupData.Name";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "UserGroupData.Status";
  public static final String PROPERTY_STATUS_DELETE = "UserGroupData.Status.Delete";
  public static final String PROPERTY_STATUS_NEW = "UserGroupData.Status.New";
  public static final String PROPERTY_STATUS_ACTIVE = "UserGroupData.Status.Active";
  public static final String FIELD_FAVORITE = "Favorite";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;


  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<>();

    bitMapStatus.add(new BitFieldEntry(UserGroupData.PROPERTY_STATUS_ACTIVE,
        CaesarConstants.UserGroup.Status.ACTIVE));
    bitMapStatus.add(new BitFieldEntry(UserGroupData.PROPERTY_STATUS_DELETE,
        CaesarConstants.UserGroup.Status.DELETE));
    bitMapStatus.add(new BitFieldEntry(UserGroupData.PROPERTY_STATUS_NEW,
        CaesarConstants.UserGroup.Status.NEW));

    Map<String, Collection<BitFieldEntry>> bitMaps = new HashMap<>();
    bitMaps.put(UserGroupData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  @Expose
  private StringProperty name = new SimpleStringProperty(""); // 用户名，最多16个字符
  @Expose
  private int status = 0;
  @Expose
  private final transient AdvancedBitSet videoAccess; // 如果对某个cpu的权限是videoaccess，那么cpu.oid对应的位为1
  @Expose
  private final transient AdvancedBitSet noAccess; // 如果对某个cpu的权限是noaccess，那么cpu.oid对应的位为1
  @Expose
  private final int[] favorite = createArrayInt(CaesarConstants.Cpu.FAVORITE);
  // favorite的cpu的oid+1的列表

  @Expose
  private boolean init = false;

  /**
   * .
   */
  public UserGroupData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      int oid, String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();
    if (configDataManager == null) {
      this.videoAccess = new AdvancedBitSet();
      this.noAccess = new AdvancedBitSet();
      return;
    }
    this.videoAccess = new AdvancedBitSet(getConfigDataManager().getConfigMetaData().getCpuCount());
    this.noAccess = new AdvancedBitSet(getConfigDataManager().getConfigMetaData().getCpuCount());
    this.videoAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT,
        evt -> {
          if (this.init) {
            return;
          }
          if (evt instanceof IndexedPropertyChangeEvent) {
            this.firePropertyChange(UserGroupData.PROPERTY_VIDEO_ACCESS,
                evt.getOldValue(), evt.getNewValue(),
                ((IndexedPropertyChangeEvent) evt).getIndex());
            this.firePropertyChange(AccessControlObject.PROPERTY_VIDEO_ACCESS,
                evt.getOldValue(), evt.getNewValue(),
                ((IndexedPropertyChangeEvent) evt).getIndex());
          }
        });
    this.noAccess.addPropertyChangeListener(AdvancedBitSet.PROPERTY_BIT,
        evt -> {
          if (this.init) {
            return;
          }
          if (evt instanceof IndexedPropertyChangeEvent) {
            this.firePropertyChange(UserGroupData.PROPERTY_NO_ACCESS,
                evt.getOldValue(), evt.getNewValue(),
                ((IndexedPropertyChangeEvent) evt).getIndex());
            this.firePropertyChange(AccessControlObject.PROPERTY_NO_ACCESS,
                evt.getOldValue(), evt.getNewValue(),
                ((IndexedPropertyChangeEvent) evt).getIndex());
          }
        });
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    this.init = true;

    this.videoAccess.clear();
    this.noAccess.clear();
    setName("");
    setStatus(0);
    for (int i = 0; i < getConfigDataManager().getConfigMetaData().getCpuCount(); i++) {
      this.noAccess.set(i, true);
    }
    for (int i = 0; i < this.favorite.length; i++) {
      setFavoriteData(i, null);
    }
    this.init = false;
  }

  public void setInitMode(boolean initMode) {
    this.init = initMode;
  }

  public boolean isInitMode() {
    return this.init;
  }

  /**
   * .
   */
  public void setLockControlCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.videoAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.videoAccess.setBits(bitIndex);
    }
  }

  /**
   * .
   */
  public void setLockVideoCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.noAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.noAccess.setBits(bitIndex);
    }
  }

  @Override
  public int getId() {
    return getOid() + 1;
  }

  public IntegerProperty getIdProperty() {
    return new SimpleIntegerProperty(getOid() + 1);
  }

  @Override
  public String getName() {
    return this.name.get();
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.name.get();
    this.name.set(name);
    firePropertyChange(UserGroupData.PROPERTY_NAME, oldValue, this.name.get());
  }

  public StringProperty getNameProperty() {
    return this.name;
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(UserGroupData.PROPERTY_STATUS, oldValue, this.status);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.UserGroup.Status.DELETE);
  }

  public boolean isStatusNew() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.UserGroup.Status.NEW);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.UserGroup.Status.ACTIVE);
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.UserGroup.Status.DELETE));
  }

  public void setStatusNew(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.UserGroup.Status.NEW));
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.UserGroup.Status.ACTIVE));
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (UserGroupData.PROPERTY_VIDEO_ACCESS.equals(propertyName)) {
      setVideoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if (UserGroupData.PROPERTY_NO_ACCESS.equals(propertyName)) {
      setNoAccess(getConfigDataManager().getCpuData(indizes[0]), Boolean.TRUE.equals(value));
    } else if (UserGroupData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (UserGroupData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (FavoriteObject.PROPERTY_FAVORITE.equals(propertyName)) {
      setFavorite(indizes[0], (Integer) value);
    }
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    cfgWriter.writeString(getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeInteger(getStatus());

    short[] lckControl = this.videoAccess
        .getAsShortArray(getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (short lck : lckControl) {
      cfgWriter.writeShort(lck);
    }
    short[] lckVideo =
        this.noAccess
            .getAsShortArray(getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (short lck : lckVideo) {
      cfgWriter.writeShort(lck);
    }
    for (int idx = 0; idx < this.favorite.length; idx++) {
      cfgWriter.writeInteger(getFavorite(idx));
    }

  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    if (!isPropertyChangedByUi(UserGroupData.PROPERTY_NAME)) {
      setName(name);
    }

    int status = cfgReader.readInteger();
    if (!isPropertyChangedByUi(UserGroupData.PROPERTY_STATUS)) {
      setStatus(status);
    }

    this.init = true;
    this.videoAccess.clear();
    this.noAccess.clear();

    short[] lckControl =
        createArrayShort(getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (int idx = 0; idx < lckControl.length; idx++) {
      lckControl[idx] = cfgReader.readShort();
    }
    this.videoAccess.setFromShorts(lckControl);

    short[] lckVideo = createArrayShort(
        getConfigDataManager().getConfigMetaData().getCpuCount() / 16);
    for (int idx = 0; idx < lckVideo.length; idx++) {
      lckVideo[idx] = cfgReader.readShort();
    }
    if (!isPropertyChangedByUi(UserGroupData.PROPERTY_NO_ACCESS)) {
      this.noAccess.setFromShorts(lckVideo);
    }

    this.init = false;
    for (int idx = 0; idx < this.favorite.length; idx++) {
      int favorite = cfgReader.readInteger();
      if (!isPropertyChangedByUi("FavoriteObject.Favorite")) {
        setFavorite(idx, favorite);
      }
    }
  }

  @Override
  public boolean isVideoAccess(CpuData cpuData) {
    return null != cpuData && this.videoAccess.get(cpuData.getOid());
  }

  @Override
  public void setVideoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.videoAccess.get(cpuData.getOid());
    this.videoAccess.set(cpuData.getOid(), locked);
    firePropertyChange(UserGroupData.PROPERTY_VIDEO_ACCESS, oldValue,
        locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getVideoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.videoAccess.getBits());
  }

  @Override
  public boolean isNoAccess(CpuData cpuData) {
    return null != cpuData && this.noAccess.get(cpuData.getOid());
  }

  @Override
  public void setUsbNoAccess(CpuData paramCpuData, boolean paramBoolean) {

  }

  @Override
  public boolean isUsbNoAccess(CpuData paramCpuData) {
    return false;
  }

  @Override
  public void setNoAccess(CpuData cpuData, boolean locked) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuData) {
      return;
    }
    boolean oldValue = this.noAccess.get(cpuData.getOid());
    this.noAccess.set(cpuData.getOid(), locked);
    firePropertyChange(UserGroupData.PROPERTY_NO_ACCESS, oldValue, locked, cpuData.getOid());
  }

  @Override
  public Collection<CpuData> getNoAccessCpuDatas() {
    return getConfigDataManager().getCpuData(this.noAccess.getBits());
  }

  /**
   * .
   */
  public void setNoAccessCpus(Collection<CpuData> cpuDatas) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (null == cpuDatas) {
      this.noAccess.setBits();
    } else {
      int[] bitIndex = new int[cpuDatas.size()];
      CpuData[] cds = cpuDatas.toArray(new CpuData[0]);
      for (int i = 0; i < bitIndex.length; i++) {
        bitIndex[i] = cds[i].getOid();
      }
      this.noAccess.setBits(bitIndex);
    }
  }

  @Override
  public int getFavorite(int idx) {
    return this.favorite[idx];
  }

  /**
   * .
   *
   * @param idx      轮询的序号，0~15
   * @param favorite 轮询的cpu的oid+1
   * @brief 设置轮询的cpu
   */
  @Override
  public void setFavorite(int idx, int favorite) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.favorite[idx];
    this.favorite[idx] = favorite;
    firePropertyChange(FavoriteObject.PROPERTY_FAVORITE, oldValue, this.favorite[idx], idx);
  }

  @Override
  public CpuData getFavoriteData(int idx) {
    return getConfigDataManager().getCpuData(this.favorite[idx] - 1);
  }

  @Override
  public Collection<CpuData> getFavoriteDatas() {
    Collection<CpuData> cpuDatas = new ArrayList<>();
    for (int i = 0; i < this.favorite.length; i++) {
      CpuData cpuData = getFavoriteData(i);
      if (null != cpuData) {
        cpuDatas.add(cpuData);
      }
    }
    return cpuDatas;
  }

  @Override
  public void setFavoriteData(int idx, CpuData cpuData) {
    setFavorite(idx, null == cpuData ? 0 : cpuData.getOid() + 1);
  }

  @Override
  public void delete(boolean internalCommit) {
    setStatusDelete(true);
    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  @Override
  public void delete() {
    delete(true);
  }

  @Override
  public AdvancedBitSet getVideoAccessBitSet() {
    return videoAccess;
  }

  @Override
  public void setVideoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getVideoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getNoAccessBitSet() {
    return noAccess;
  }

  @Override
  public void setNoAccessBits(int... bits) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    getNoAccessBitSet().setBits(bits);
  }

  @Override
  public AdvancedBitSet getUsbNoAccessBitSet() {
    return new AdvancedBitSet();
  }

  @Override
  public void setUsbNoAccessBits(int... bits) {
  }

  @Override
  public int[] getFavoriteArray() {
    return favorite.clone();
  }

}
