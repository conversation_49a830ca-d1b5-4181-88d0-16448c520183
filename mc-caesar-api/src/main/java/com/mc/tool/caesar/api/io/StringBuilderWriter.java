package com.mc.tool.caesar.api.io;

import java.io.IOException;
import java.io.Writer;

/**
 * .
 */
public class StringBuilderWriter extends Writer {

  private final StringBuilder sb;

  public StringBuilderWriter() {
    this.sb = new StringBuilder(4096);
  }

  public StringBuilderWriter(StringBuilder stringBuilder) {
    this.sb = stringBuilder;
  }

  @Override
  public void close() throws IOException {

  }

  @Override
  public void flush() throws IOException {

  }

  @Override
  public void write(char[] cbuf, int off, int len) throws IOException {
    this.sb.append(cbuf, off, len);
  }

  public String getString() {
    return this.sb.toString();
  }
}

