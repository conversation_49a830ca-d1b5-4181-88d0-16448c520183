package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.interfaces.Idable;
import com.mc.tool.caesar.api.interfaces.Nameable;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.AdvancedBitSet;
import java.util.Collection;

/**
 * .
 *
 * @brief 权限信息接口
 */
public interface AccessControlObject extends Idable, Nameable {

  String PROPERTY_VIDEO_ACCESS = "AccessControlObject.VideoAccess";
  String PROPERTY_NO_ACCESS = "AccessControlObject.NoAccess";
  String PROPERTY_USB_NO_ACCESS = "AccessControlObject.UsbNoAccess";

  void setVideoAccess(CpuData paramCpuData, boolean paramBoolean);

  boolean isVideoAccess(CpuData paramCpuData);

  void setNoAccess(CpuData paramCpuData, boolean paramBoolean);

  boolean isNoAccess(CpuData paramCpuData);

  void setUsbNoAccess(CpuData paramCpuData, boolean paramBoolean);

  boolean isUsbNoAccess(CpuData paramCpuData);

  Collection<CpuData> getVideoAccessCpuDatas();

  Collection<CpuData> getNoAccessCpuDatas();

  AdvancedBitSet getVideoAccessBitSet();

  void setVideoAccessBits(int... bits);

  AdvancedBitSet getNoAccessBitSet();

  void setNoAccessBits(int... bits);

  AdvancedBitSet getUsbNoAccessBitSet();

  void setUsbNoAccessBits(int... bits);

  void setThreshold(Threshold paramThreshold);

  Threshold getThreshold();
}

