package com.mc.tool.caesar.api.utils;

import com.google.gson.annotations.Expose;
import java.beans.PropertyChangeListener;
import java.beans.PropertyChangeSupport;
import java.util.Arrays;
import java.util.BitSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * 加强功能的bitset.
 */
public final class AdvancedBitSet {

  public static final String PROPERTY_BIT = "AdvancedBitSet.bit";
  @Expose
  private final BitSet bitSet;
  private final PropertyChangeSupport pcs = new PropertyChangeSupport(this);

  public AdvancedBitSet(int nbits) {
    this.bitSet = new BitSet(nbits);
  }

  public AdvancedBitSet() {
    this.bitSet = new BitSet();
  }

  public AdvancedBitSet(AdvancedBitSet input) {
    this();
    setBits(input.getBits());
  }

  public synchronized void removePropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(listener);
  }

  public synchronized void removePropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.removePropertyChangeListener(propertyName, listener);
  }

  public synchronized void addPropertyChangeListener(String propertyName,
      PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(propertyName, listener);
  }

  public synchronized void addPropertyChangeListener(PropertyChangeListener listener) {
    this.pcs.addPropertyChangeListener(listener);
  }

  /**
   * 设置第bitIndex个位的值.
   *
   * @param bitIndex 从0开始
   * @param value    value
   */
  public void set(int bitIndex, boolean value) {
    boolean oldValue = this.bitSet.get(bitIndex);
    this.bitSet.set(bitIndex, value);
    this.pcs.fireIndexedPropertyChange(AdvancedBitSet.PROPERTY_BIT, bitIndex, oldValue, value);
  }

  public boolean get(int bitIndex) {
    return this.bitSet.get(bitIndex);
  }

  /**
   * .
   */
  public void clear() {
    for (int bitIndex : getBits()) {
      set(bitIndex, false);
    }
  }

  /**
   * 设置多个位为1.
   *
   * @param bits 需要设置为1的序号的集合，不在集合内的都设为0
   */
  public void setBits(int... bits) {
    Map<Integer, Boolean> map = new TreeMap<>();
    for (int bitIndex : getBits()) {
      map.put(bitIndex, Boolean.FALSE);
    }
    for (int bitIndex : bits) {
      map.put(bitIndex, Boolean.TRUE);
    }
    for (Map.Entry<Integer, Boolean> entry : map.entrySet()) {
      set(entry.getKey(), entry.getValue());
    }
  }

  /**
   * 把整型数组的值的各个位设置到bitset中.
   *
   * @param ints 整型数组，每个int的最低位先push到bitset
   * @return AdvancedBitSet AdvancedBitSet
   */
  public AdvancedBitSet setFromInts(int... ints) {
    for (int i = 0; i < ints.length; i++) {
      String binary = Integer.toBinaryString(ints[i]);
      for (int bit = 0; bit < binary.length(); bit++) {
        set(i * 32 + binary.length() - bit - 1, '1' == binary.charAt(bit));
      }
    }
    return this;
  }

  /**
   * 把整型数组的值的各个位设置到bitset中.
   *
   * @param longs 整型数组，每个long的最低位先push到bitset
   * @return AdvancedBitSet AdvancedBitSet
   */
  public AdvancedBitSet setFromLongs(long... longs) {
    int[] ints = new int[2 * longs.length];
    for (int i = 0; i < longs.length; i++) {
      ints[2 * i] = (int) (0xFFFFFFFFFFFFFFFFL & longs[0]);
      ints[2 * i + 1] = (int) (0xFFFFFFFFFFFFFFFFL & longs[0] >> 32);
    }
    return setFromInts(ints);
  }

  /**
   * 把整型数组的值的各个位设置到bitset中.
   *
   * @param shorts 整型数组，每个short的最低位先push到bitset
   * @return AdvancedBitSet AdvancedBitSet
   */
  public AdvancedBitSet setFromShorts(short... shorts) {
    for (int i = 0; i < shorts.length; i++) {
      String binary = Integer.toBinaryString(0xFFFF & shorts[i]);
      for (int bit = 0; bit < binary.length(); bit++) {
        set(i * 16 + binary.length() - bit - 1, '1' == binary.charAt(bit));
      }
    }
    return this;
  }

  /**
   * 把字节数组的值的各个位设置到bitset中.
   *
   * @param bytes 整型数组，每个byte的最低位先push到bitset
   * @return AdvancedBitSet AdvancedBitSet
   */
  public AdvancedBitSet setFromBytes(byte... bytes) {
    for (int i = 0; i < bytes.length; i++) {
      String binary = Integer.toBinaryString(0xFF & bytes[i]);
      for (int bit = 0; bit < binary.length(); bit++) {
        set(i * 8 + binary.length() - bit - 1, '1' == binary.charAt(bit));
      }
    }
    return this;
  }

  /**
   * 获取值为1的所有bit的序号.
   *
   * @return int[] int[]
   */
  public int[] getBits() {
    int[] result = new int[this.bitSet.cardinality()];
    int position = 0;
    for (int i = 0; i < result.length; i++) {
      result[i] = this.bitSet.nextSetBit(position);
      position = result[i] + 1;
    }
    return result;
  }

  public byte[] getAsByteArray() {
    return getAsByteArray(-1);
  }

  /**
   * 转换为byte的数组，0~7位为第一个byte，一一对应此byte的0~7位，如此类推.
   *
   * @param minLength 最小的数组长度
   * @return byte[] byte[]
   */
  public byte[] getAsByteArray(int minLength) {
    Map<Integer, Set<Integer>> splitBits = splitBits(8);
    int length = 0;
    for (Integer integer : splitBits.keySet()) {
      length = Math.max(integer, length);
    }
    byte[] result = new byte[Math.max(minLength, length + 1)];

    Arrays.fill(result, (byte) 0);
    for (Map.Entry<Integer, Set<Integer>> entry : splitBits.entrySet()) {
      int value = 0;
      for (Integer integer : entry.getValue()) {
        value |= 1 << integer;
      }
      result[entry.getKey()] = (byte) (0xFF & value);
    }
    return result;
  }

  public short[] getAsShortArray() {
    return getAsShortArray(-1);
  }

  /**
   * 转换为short的数组，0~15位为第一个short，一一对应此short的0~15位，如此类推.
   *
   * @param minLength 最小的数组长度
   * @return short[] short[]
   */
  public short[] getAsShortArray(int minLength) {
    Map<Integer, Set<Integer>> splitBits = splitBits(16);
    int length = 0;
    for (Integer integer : splitBits.keySet()) {
      length = Math.max(integer, length);
    }
    short[] result = new short[Math.max(minLength, length + 1)];

    Arrays.fill(result, (short) 0);
    for (Map.Entry<Integer, Set<Integer>> entry : splitBits.entrySet()) {
      int value = 0;
      for (Integer integer : entry.getValue()) {
        value |= 1 << integer;
      }
      result[entry.getKey()] = (short) (0xFFFF & value);
    }
    return result;
  }

  public int[] getAsIntArray() {
    return getAsIntArray(-1);
  }

  /**
   * 转换为int的数组，0~31位为第一个int，一一对应此int的0~31位，如此类推.
   *
   * @param minLength 最小的数组长度
   * @return int[] int[]
   */
  public int[] getAsIntArray(int minLength) {
    Map<Integer, Set<Integer>> splitBits = splitBits(32);
    int length = 0;
    for (Integer integer : splitBits.keySet()) {
      length = Math.max(integer, length);
    }
    int[] result = new int[Math.max(minLength, length + 1)];

    Arrays.fill(result, 0);
    for (Map.Entry<Integer, Set<Integer>> entry : splitBits.entrySet()) {
      int value = 0;
      for (Integer integer : entry.getValue()) {
        value |= 1 << integer;
      }
      result[entry.getKey()] = value;
    }
    return result;
  }

  public long[] getAsLongArray() {
    return getAsLongArray(-1);
  }

  /**
   * 转换为long的数组，0~63位为第一个long，一一对应此long的0~63位，如此类推.
   *
   * @param minLength 最小的数组长度
   * @return long[] long[]
   */
  public long[] getAsLongArray(int minLength) {
    Map<Integer, Set<Integer>> splitBits = splitBits(64);
    int length = 0;
    for (Integer integer : splitBits.keySet()) {
      length = Math.max(integer, length);
    }
    long[] result = new long[Math.max(minLength, length + 1)];

    Arrays.fill(result, 0L);
    for (Map.Entry<Integer, Set<Integer>> entry : splitBits.entrySet()) {
      long value = 0L;
      for (Integer integer : entry.getValue()) {
        value |= 1L << integer;
      }
      result[entry.getKey()] = value;
    }
    return result;
  }

  /**
   * 以size个bit为一组，获取各组中值为1的bit的序号，序号范围为[0,size-1].
   *
   * @param size size
   * @return map map
   */
  private Map<Integer, Set<Integer>> splitBits(int size) {
    Map<Integer, Set<Integer>> result = new HashMap<>();
    for (int bit : getBits()) {
      int key = bit / size;
      Set<Integer> set = result.computeIfAbsent(key, k -> new HashSet<>());
      set.add(bit % size);
    }
    return result;
  }

  @Override
  public String toString() {
    return this.bitSet.toString();
  }
}

