package com.mc.tool.caesar.api.datamodel.vp;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.io.NormalStruct;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.util.logging.Logger;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import org.slf4j.LoggerFactory;

/**
 * .
 */
public class VpConsoleData extends AbstractData
    implements DataObject, CaesarCommunicatable {

  static final Logger LOG = Logger.getLogger(VpConsoleData.class.getName());
  public static final int MAX_PORT_COUNT = 8;
  private static final org.slf4j.Logger log = LoggerFactory.getLogger(VpConsoleData.class);
  @Expose
  private StringProperty name = new SimpleStringProperty("");
  @Expose
  private StringProperty outResolutionInfo = new SimpleStringProperty("Invalid");
  @Expose
  private int id = 0; // 设备id
  @Expose
  private boolean active = false; //
  @Expose
  private boolean online = false;

  private transient VpConConfigData configData;
  @Expose
  private VpType vpType;

  private ConsoleData[] inPorts = null; // length is inPortCount
  private VpConsoleData[] outPorts = null; // length is outPortCount. if outPortCount is 1, outPorts
  // is null
  @Expose
  private transient VpResolution[] inResolutions = null; // length is inPortCount(vp6) or 8(vp7)
  @Expose
  private transient VpResolution[] outResolutions = null; // length is outPortCount

  private VpConsoleData parent = null;

  public static final String PROPERTY_STATUS_ACTIVE = "VpConsoleData.Status.Active";
  public static final String PROPERTY_STATUS_ONLINE = "VpConsoleData.Status.Online";

  /**
   * .
   */
  public VpConsoleData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      int oid, String fqn, VpType vpType) {
    super(pcs, configDataManager, oid, fqn);
    this.vpType = vpType;
  }

  public VpConsoleData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      int oid, String fqn, VpType vpType, VpConsoleData parent) {
    this(pcs, configDataManager, oid, fqn, vpType);
    this.parent = parent;
  }

  /**
   * .
   */
  public void reBuild(VpType vpType) {
    reset();
    this.vpType = vpType;
    inPorts = new ConsoleData[vpType.getInPortCount()];
    outPorts = new VpConsoleData[vpType.getOutPortCount()];

    if (vpType == VpType.VP7) {
      inResolutions = new VpResolution[8];
      for (int i = 0; i < 8; i++) {
        inResolutions[i] = new VpResolution();
      }
    } else {
      inResolutions = new VpResolution[vpType.getInPortCount()];
      for (int i = 0; i < vpType.getInPortCount(); i++) {
        inResolutions[i] = new VpResolution();
      }
    }

    outResolutions = new VpResolution[vpType.getOutPortCount()];
    for (int i = 0; i < vpType.getOutPortCount(); i++) {
      outResolutions[i] = new VpResolution();
    }

    configData = vpType.createConfigData();
  }

  public void reBuild(VpType vpType, VpConsoleData parent) {
    this.reBuild(vpType);
    this.parent = parent;
  }

  public boolean isStatusActive() {
    return active;
  }

  public boolean isStatusOnline() {
    return online;
  }

  /**
   * .
   */
  public void reset() {
    active = false;
    configData = null;
    inPorts = null;
    outPorts = null;
    inResolutions = null;
    outResolutions = null;
    parent = null;
  }

  /**
   * .
   */
  public void setStatusActive(boolean active) {
    boolean old = this.active;
    this.active = active;
    firePropertyChange(PROPERTY_STATUS_ACTIVE, old, active);
  }

  /**
   * 设置在线状态.
   *
   * @param online 在线状态
   */
  public void setStatusOnline(boolean online) {
    boolean old = this.online;
    this.online = online;
    firePropertyChange(PROPERTY_STATUS_ONLINE, old, online);
  }

  @Override
  public int getId() {
    return this.id;
  }

  public void setId(int id) {
    this.id = id;
  }

  public void setName(String name) {
    this.name.set(name);
  }

  public StringProperty nameProperty() {
    return this.name;
  }

  @Override
  public String getName() {
    return this.name.get();
  }

  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {

  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {

  }

  @Override
  public void delete() {

  }

  @Override
  public void delete(boolean paramBoolean) {

  }

  public int getInPortCount() {
    return vpType.getInPortCount();
  }

  /**
   * 获取有效的输入端口数.
   *
   * @return 有效的输入端口个数
   */
  public int getValidInPortCount() {
    int result = 0;
    for (ConsoleData data : getInPortList()) {
      if (data != null && data.isOnline()) {
        result++;
      }
    }
    return result;
  }

  public int getOutPortCount() {
    return vpType.getOutPortCount();
  }

  public VpType getType() {
    return vpType;
  }

  public VpConConfigData getConfigData() {
    return configData;
  }

  public void setConfigData(VpConConfigData configData) {
    this.configData = configData;
  }

  /**
   * .
   */
  public void setResolutions(VpResolution[] resolutions) {
    if (resolutions == null) {
      return;
    }
    if (vpType == VpType.VP6) {
      if (resolutions.length != getInPortCount() + getOutPortCount()) {
        log.warn("Invalid vp6 resolution count %d", resolutions.length);
        return;
      }
      int index = 0;
      for (int i = 0; i < getOutPortCount(); i++) {
        setOutResolution(i, resolutions[index]);
        if (i < outPorts.length && outPorts[i] != null) {
          outPorts[i].setOutResolution(0, resolutions[index]);
        }
        index++;
      }

      for (int i = 0; i < getInPortCount(); i++) {
        setInResoluion(i, resolutions[index]);
        index++;
      }
    } else if (vpType == VpType.VP7) {
      if (resolutions.length < 4) {
        // VP7至少有4个输出分辨率
        log.warn("Invalid vp7 resolution count %d", resolutions.length);
        return;
      }
      for (int i = 0; i < 4; i++) {
        setOutResolution(i, resolutions[i]);
      }
      for (int i = 4; i < resolutions.length; i++) {
        setInResoluion(i - 4, resolutions[i]);
      }
    }

  }

  /**
   * .
   */
  public VpResolution getOutResolution(int index) {
    if (index < 0 || index >= getOutPortCount()) {
      return null;
    }
    return outResolutions[index];
  }

  /**
   * 获取输出分辨率字符串.
   *
   * @return 分辨率
   */
  public String getOutResolution() {
    StringBuilder builder = new StringBuilder();
    for (int i = 0; i < getOutPortCount(); i++) {
      VpResolution resolution = getOutResolution(i);
      if (i != 0) {
        builder.append("; ");
      }
      if (resolution.isValid()) {
        builder.append(resolution.getWidth()).append("*").append(resolution.getHeight());
      } else {
        builder.append("Invalid");
      }
    }
    return builder.toString();
  }

  public StringProperty outResolutionProperty() {
    return outResolutionInfo;
  }

  /**
   * .
   */
  public void setOutResolution(int index, VpResolution resolution) {
    if (index < 0 || index >= getOutPortCount()) {
      return;
    }
    outResolutions[index].width.set(resolution.width.get());
    outResolutions[index].height.set(resolution.height.get());
    if (outPorts != null && outPorts[index] != null) {
      outPorts[index].setOutResolution(0, resolution);
    }

    outResolutionInfo.set(getOutResolution());
  }

  /**
   * .
   */
  public VpResolution getInResolution(int index) {
    if (index < 0 || index > getInPortCount()) {
      return null;
    }
    return inResolutions[index];
  }

  /**
   * .
   */
  public void setInResoluion(int index, VpResolution resolution) {
    if (index < 0 || index >= inResolutions.length) {
      return;
    }
    inResolutions[index].width.set(resolution.getWidth());
    inResolutions[index].height.set(resolution.getHeight());

    ConsoleData consoleData = getInPort(index);
    if (consoleData != null && consoleData.getExtenderData(0) != null) {
      ResolutionData resolutionData = new ResolutionData();
      if (resolution.isValid()) {
        resolutionData.setWidth((int) resolution.getWidth());
        resolutionData.setHeight((int) resolution.getHeight());
      } else {
        resolution.setWidth(0);
        resolution.setHeight(0);
      }
      consoleData.getExtenderData(0).setResolution(0, resolutionData);
    }
  }

  /**
   * 获取输入con列表.
   *
   * @return 输入con列表
   */
  public ConsoleData[] getInPortList() {
    if (inPorts == null) {
      return new ConsoleData[0];
    }
    return inPorts.clone();
  }


  /**
   * .
   */
  public ConsoleData getInPort(int index) {
    if (index < 0 || index >= getInPortCount()) {
      return null;
    }
    return inPorts[index];
  }

  /**
   * .
   */
  public void setInPort(int index, ConsoleData consoleData) {
    if (index < 0 || index >= getInPortCount()) {
      return;
    }
    inPorts[index] = consoleData;
  }

  public VpConsoleData[] getOutPortList() {
    return outPorts.clone();
  }

  /**
   * 获取outport的索引.
   *
   * @param vpConsoleData vp console data.
   * @return 索引，如果获取不成功，返回-1.
   */
  public int getOutportIndex(VpConsoleData vpConsoleData) {
    for (int i = 0; i < getOutPortCount(); i++) {
      if (outPorts[i] == vpConsoleData) {
        return i;
      }
    }
    return -1;
  }

  /**
   * .
   */
  public void setOutPort(int index, VpConsoleData data) {
    if (index < 0 || index >= getOutPortCount()) {
      return;
    }
    if (data.getParent() != this) {
      return;
    }
    outPorts[index] = data;
  }

  public VpConsoleData getParent() {
    return parent;
  }

  public boolean hasParent() {
    return parent != null;
  }

  /**
   * .
   */
  public static class VpResolution extends NormalStruct {

    @Expose
    private Unsigned32 width = new Unsigned32();
    @Expose
    private Unsigned32 height = new Unsigned32();
    public static final long INVALID_VALUE = 4294967295L;

    public long getWidth() {
      return width.get();
    }

    public void setWidth(long width) {
      this.width.set(width);
    }

    public long getHeight() {
      return height.get();
    }

    public void setHeight(long height) {
      this.height.set(height);
    }

    public boolean isValid() {
      return getWidth() != INVALID_VALUE && getHeight() != INVALID_VALUE;
    }

    public String getText() {
      return String.format("%d*%d", getWidth(), getHeight());
    }
  }

}
