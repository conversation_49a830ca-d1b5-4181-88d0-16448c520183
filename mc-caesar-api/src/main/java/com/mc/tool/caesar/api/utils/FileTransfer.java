package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.BasicTeraController;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarController;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 通过ftp读写数据.
 */
public final class FileTransfer {

  private static final BasicTeraController CONTROLLER = new CaesarController(null);
  private static final int BUFFER_LEN = 1024;

  /**
   * .
   *
   * @param urlPath urlPath
   * @throws ConfigException           ConfigException
   * @throws DeviceConnectionException DeviceConnectionException
   * @throws BusyException             BusyException
   * @throws IOException               IOException
   */
  public static byte[] read(String urlPath)
      throws ConfigException, DeviceConnectionException, BusyException, IOException {

    UrlValidator urlValidator = new UrlValidator(urlPath, UrlValidator.Protocol.FTP);
    if (urlValidator.isValid()) {
      String hostName = urlValidator.getHostname();
      int fileType = getFileType(urlValidator.getPath());
      return read(hostName, fileType);
    } else {
      return new byte[0];
    }
  }

  /**
   * 读取文件.
   *
   * @param hostName 主机地址
   * @param fileType file type
   * @return 读取到的数据
   */
  public static byte[] read(String hostName, int fileType)
      throws ConfigException, BusyException, IOException {

    long key = Long.parseLong(hostName.replace(".", ""));
    CONTROLLER.setIdentifier(key);
    CONTROLLER.setConnection(key, hostName + ":" + CaesarConstants.CFG_PORT);
    try {
      return read(CONTROLLER, fileType);
    } finally {
      CONTROLLER.setConnection(key, null);
    }
  }

  /**
   * 读取文件.
   *
   * @param controller controller
   * @param fileType   file type
   * @return 读取到的数据
   */
  public static byte[] read(BasicTeraController controller, int fileType)
      throws ConfigException, BusyException, IOException {
    ByteArrayOutputStream bos = new ByteArrayOutputStream();
    controller.setSystemFileOpen(fileType);
    byte[] data;
    do {
      data = controller.readSystemFile(fileType, BUFFER_LEN);
      if (data.length > 0) {
        bos.write(data);
      }
    } while (data.length == BUFFER_LEN);
    controller.setSystemFileClose(fileType);
    return bos.toByteArray();
  }

  /**
   * .
   */
  public static void write(byte[] bb, String urlPath)
      throws ConfigException, DeviceConnectionException, BusyException {
    UrlValidator urlValidator = new UrlValidator(urlPath, UrlValidator.Protocol.FTP);
    if (urlValidator.isValid()) {
      String hostName = urlValidator.getHostname();
      int fileType = getFileType(urlValidator.getPath());

      write(hostName, fileType, bb);
    }
  }

  /**
   * 写入文件.
   *
   * @param hostName 主机地址
   * @param fileType file type
   * @param bb       要写入的数据.
   */
  public static void write(String hostName, int fileType, byte[] bb)
      throws DeviceConnectionException, ConfigException, BusyException {
    ByteArrayInputStream is = null;
    try {
      is = new ByteArrayInputStream(bb);
      write(hostName, fileType, is);
    } finally {
      try {
        if (is != null) {
          is.close();
        }
      } catch (IOException ex) {
        throw new ConfigException(CfgError.IO, ex);
      }
    }
  }

  /**
   * 写入文件.
   *
   * @param hostName 主机地址
   * @param fileType file type
   * @param is       数据流
   */
  public static void write(String hostName, int fileType, InputStream is)
      throws DeviceConnectionException, ConfigException, BusyException {
    long key = Long.parseLong(hostName.replace(".", ""));
    CONTROLLER.setIdentifier(key);
    CONTROLLER.setConnection(key, hostName + ":" + CaesarConstants.CFG_PORT);
    try {
      write(CONTROLLER, fileType, is);
    } finally {
      CONTROLLER.setConnection(key, null);
    }
  }

  /**
   * 写入文件.
   *
   * @param controller controller
   * @param fileType   filetype
   * @param is         数据流
   */
  public static void write(BasicTeraController controller, int fileType, InputStream is)
      throws DeviceConnectionException, ConfigException, BusyException {
    controller.setSystemFileOpen(fileType);

    CfgReader reader = new CfgReader(is);
    while (reader.available() > 0) {
      int length = Math.min(reader.available(), BUFFER_LEN);

      byte[] bytes = reader.readByteArray(length);
      controller.writeSystemFile(fileType, bytes);
    }
    controller.setSystemFileClose(fileType);
  }

  /**
   * .
   *
   * @param fileName fileName
   */
  public static int getFileType(String fileName) {
    int index = 1;
    fileName = fileName.substring(1).split("\\.")[0];
    for (String config : CaesarConstants.CONFIG_NAMES) {
      if (config.equals(fileName)) {
        return index;
      }
      index++;
    }
    return 0;
  }
}

