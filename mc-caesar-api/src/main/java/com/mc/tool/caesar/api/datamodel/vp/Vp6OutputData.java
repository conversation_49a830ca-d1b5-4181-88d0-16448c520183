package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;
import java.util.logging.Logger;

/**
 * .
 */
public class Vp6OutputData extends NormalStruct {

  static final Logger LOG = Logger.getLogger(Vp6OutputData.class.getName());

  /**
   * .
   */
  public static class VpOutputResImpl extends NormalStruct {

    public Unsigned16 sync = new Unsigned16();
    public Unsigned16 backPorch = new Unsigned16();
    public Unsigned16 activeVideoTime = new Unsigned16();
    public Unsigned16 frontPorch = new Unsigned16();
    public Unsigned8 polarity = new Unsigned8();
  }

  public Unsigned8 enable = new Unsigned8();
  public VpOutputResImpl horzOutput = inner(new VpOutputResImpl());
  public VpOutputResImpl vertOutput = inner(new VpOutputResImpl());
  public Unsigned32 clock = new Unsigned32();
  public Unsigned8[] reserved = array(new Unsigned8[41]);

  /**
   * 打印数据内容.
   */
  public void print() {
    String msg = String.format(
        "enable(%d), clock(0x%x), " + "hs(0x%x), hbp(0x%x), havt(0x%x), hfp(0x%x), hp(0x%x), "
            + "vs(0x%x), vbp(0x%x), vavt(0x%x), vfp(0x%x), vp(0x%x)",
        enable.get(), clock.get(), horzOutput.sync.get(), horzOutput.backPorch.get(),
        horzOutput.activeVideoTime.get(), horzOutput.frontPorch.get(), horzOutput.polarity.get(),
        vertOutput.sync.get(), vertOutput.backPorch.get(), vertOutput.activeVideoTime.get(),
        vertOutput.frontPorch.get(), vertOutput.polarity.get());
    LOG.info(msg);
  }
}
