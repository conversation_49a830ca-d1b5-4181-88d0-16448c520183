package com.mc.tool.caesar.api;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Arrays;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * DkmProtocalCrypto.
 */
public class DkmProtocalCrypto {

  /**
   * encrypt.
   */
  public static byte[] encrypt(byte[] input)
      throws NoSuchPaddingException, NoSuchAlgorithmException, BadPaddingException,
          IllegalBlockSizeException, UnsupportedEncodingException, InvalidKeyException,
          InvalidKeySpecException, InvalidAlgorithmParameterException {
    if (input.length <= 3) {
      return Arrays.copyOf(input, input.length);
    } else {
      byte[] output = new byte[input.length + ENCRYPTED_LEN];
      System.arraycopy(input, 0, output, 0, HEAD_LEN);
      int len = input.length + ENCRYPTED_LEN;
      byte[] lenBytes = {(byte) len, (byte) (len >> 8), (byte) (len >> 16), (byte) (len >> 24)};
      System.arraycopy(lenBytes, 0, output, HEAD_LEN, ENCRYPTED_LEN);
      Cipher cipher = Cipher.getInstance(algorithm);
      cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(IV));
      byte[] encryptedData = cipher.doFinal(Arrays.copyOfRange(input, HEAD_LEN, input.length));
      assert encryptedData.length == input.length - HEAD_LEN;
      System.arraycopy(encryptedData, 0, output, HEAD_LEN + ENCRYPTED_LEN, encryptedData.length);
      return output;
    }
  }

  /**
   * decrypt.
   */
  public static byte[] decrypt(byte[] input)
      throws NoSuchPaddingException, NoSuchAlgorithmException, UnsupportedEncodingException,
          InvalidKeyException, BadPaddingException, IllegalBlockSizeException,
          InvalidKeySpecException, InvalidAlgorithmParameterException {
    if (input.length <= 3) {
      return Arrays.copyOf(input, input.length);
    } else {
      byte[] output = new byte[input.length - ENCRYPTED_LEN];
      System.arraycopy(input, 0, output, 0, HEAD_LEN);
      Cipher cipher = Cipher.getInstance(algorithm);
      cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(IV));
      byte[] decryptedData =
          cipher.doFinal(Arrays.copyOfRange(input, HEAD_LEN + ENCRYPTED_LEN, input.length));
      assert decryptedData.length == input.length - HEAD_LEN - ENCRYPTED_LEN;
      System.arraycopy(decryptedData, 0, output, HEAD_LEN, decryptedData.length);
      return output;
    }
  }

  /**
   * decryptWithoutHead.
   */
  public static byte[] decryptWithoutHead(byte[] input)
      throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException,
          InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
    Cipher cipher = Cipher.getInstance(algorithm);
    cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(IV));
    return cipher.doFinal(input);
  }

  private static byte[] KEY =
      new byte[] {
        (byte) 0x99,
        (byte) 0x1f,
        (byte) 0xe2,
        (byte) 0xe7,
        (byte) 0xb0,
        (byte) 0x24,
        (byte) 0xb,
        (byte) 0x24,
        (byte) 0x98,
        (byte) 0x9b,
        (byte) 0xfa,
        (byte) 0x8c,
        (byte) 0xe3,
        (byte) 0x18,
        (byte) 0x7d,
        (byte) 0x3c,
      };
  private static byte[] IV =
      new byte[] {
        (byte) 0x19,
        (byte) 0xa8,
        (byte) 0x8d,
        (byte) 0x70,
        (byte) 0xa9,
        (byte) 0xc2,
        (byte) 0xcd,
        (byte) 0x90,
        (byte) 0x20,
        (byte) 0x16,
        (byte) 0x4e,
        (byte) 0xa8,
        (byte) 0xd7,
        (byte) 0xa2,
        (byte) 0x34,
        (byte) 0x2f,
      };
  private static int HEAD_LEN = 3;
  private static int ENCRYPTED_LEN = 4;
  private static String algorithm = "AES_128/OFB/NoPadding";
  private static Key secretKey = new SecretKeySpec(KEY, "AES");

  public static String md5(String str) {
    return compute(str, "MD5");
  }

  public static String sha1(String str) {
    return compute(str, "SHA-1");
  }

  private static String compute(String inStr, String hash) {
    String result = null;
    try {
      byte[] valueByte = inStr.getBytes(StandardCharsets.UTF_8);
      MessageDigest md = MessageDigest.getInstance(hash);
      md.update(valueByte);
      result = toHex(md.digest());
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
    }
    return result;
  }

  // 将传递进来的字节数组转换成十六进制的字符串形式并返回
  private static String toHex(byte[] buffer) {
    StringBuilder sb = new StringBuilder(buffer.length * 2);
    for (byte b : buffer) {
      sb.append(Character.forDigit((b & 0xf0) >> 4, 16));
      sb.append(Character.forDigit(b & 0x0f, 16));
    }
    return sb.toString();
  }
}
