package com.mc.tool.caesar.api.exception;

/**
 * .
 */
public class ConfigException extends Exception {

  /**
   * .
   */
  private static final long serialVersionUID = 1L;
  private final int errorCode;

  public ConfigException(int errorCode, String message, Throwable cause) {
    super(message, cause);
    this.errorCode = errorCode;
  }

  public ConfigException(int errorCode, Throwable cause) {
    this(errorCode, null, cause);
  }

  public ConfigException(int errorCode, String message) {
    this(errorCode, message, null);
  }

  public ConfigException(int errorCode) {
    this(errorCode, null, null);
  }

  public int getError() {
    return this.errorCode;
  }
}

