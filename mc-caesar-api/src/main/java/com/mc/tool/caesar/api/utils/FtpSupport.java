package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.CaesarConstants;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.net.ftp.FTPClient;

/**
 * ftp相关操作.
 */
public final class FtpSupport {

  private static final Logger LOG = Logger.getLogger(FtpSupport.class.getName());
  public static final int FTP_PORT = CaesarConstants.FTP_PORT;
  static byte[] data = new byte[0];

  /**
   * .
   *
   * @param urlPath urlPath
   * @param enc     enc
   * @throws ConnectException ConnectException
   * @throws IOException      IOException
   */
  public static byte[] read(String urlPath, String enc) throws ConnectException, IOException {
    ByteArrayOutputStream os = new ByteArrayOutputStream();

    URL url = new URL(urlPath);
    if (null == url.getAuthority() || url.getAuthority().isEmpty()) {
      throw new ConnectException();
    }
    FTPClient ftpClient = null;
    try {
      ftpClient = createFtpClient(url.getHost(), enc);
      if (ftpClient.login(getUser(url, enc), getPassword(url, enc))) {
        ftpClient.setFileType(2);

        ftpClient.enterLocalPassiveMode();
        ftpClient.retrieveFile(url.getFile(), os);
        ftpClient.logout();

        data = os.toByteArray();
      } else {
        LOG.log(Level.SEVERE, "Invalid user or passwort");
        throw new ConnectException();
      }
      return data;
    } finally {
      if (ftpClient != null) {
        ftpClient.disconnect();
      }
      try {
        os.close();
      } catch (IOException ex) {
        ex.printStackTrace();
      }
    }
  }

  /**
   * .
   *
   * @param bytes   bytes
   * @param urlName urlName
   * @param enc     enc
   * @throws EOFException     EOFException
   * @throws ConnectException ConnectException
   * @throws IOException      IOException
   */
  public static void write(byte[] bytes, String urlName, String enc)
      throws EOFException, ConnectException, IOException {
    InputStream is = null;

    URL url = new URL(urlName);
    FTPClient ftpClient = null;
    try {
      ftpClient = createFtpClient(url.getHost(), enc);
      if (ftpClient.login(getUser(url, enc), getPassword(url, enc))) {
        is = new ByteArrayInputStream(bytes);
        ftpClient.setFileType(2);

        ftpClient.enterLocalPassiveMode();
        ftpClient.storeFile(url.getFile(), is);
        ftpClient.logout();
      } else {
        LOG.log(Level.SEVERE, "Invalid user or passwort");
        throw new ConnectException();
      }
    } finally {
      if (ftpClient != null) {
        ftpClient.disconnect();
      }
      try {
        if (is != null) {
          is.close();
        }
      } catch (IOException ex) {
        ex.printStackTrace();
      }
    }
  }

  /**
   * .
   *
   * @param urlName urlName
   * @param enc     enc
   * @throws IOException IOException
   */
  public static void delete(String urlName, String enc) throws IOException {
    URL url = new URL(urlName);
    FTPClient ftpClient = null;
    try {
      ftpClient = createFtpClient(url.getHost(), enc);
      if (ftpClient.login(getUser(url, enc), getPassword(url, enc))) {
        ftpClient.deleteFile("default.dtl");
      } else {
        LOG.log(Level.SEVERE, "Invalid user or passwort");
        throw new ConnectException();
      }
    } finally {
      if (ftpClient != null) {
        ftpClient.disconnect();
      }
    }
  }

  /**
   * .
   *
   * @param urlName urlName
   * @param enc     enc
   * @throws IOException IOException
   */
  public static boolean checkUserRights(String urlName, String enc) throws IOException {
    boolean isValid = false;
    int index = urlName.lastIndexOf('/');
    if (-1 != index) {
      URL url = new URL(urlName.substring(0, index));

      FTPClient ftpClient = createFtpClient(url.getHost(), enc);
      if (ftpClient.login(getUser(url, enc), getPassword(url, enc))) {
        ftpClient.logout();
        isValid = true;
      }
      ftpClient.disconnect();
    }
    return isValid;
  }

  private static String getUser(URL url, String enc) throws UnsupportedEncodingException {
    String[] userInfo = url.getUserInfo().split(":");
    if (userInfo.length == 2) {
      return URLDecoder.decode(userInfo[0], enc);
    }
    return null;
  }

  private static String getPassword(URL url, String enc) throws UnsupportedEncodingException {
    String[] userInfo = url.getUserInfo().split(":");
    if (userInfo.length == 2) {
      return URLDecoder.decode(userInfo[1], enc);
    }
    return null;
  }

  private static FTPClient createFtpClient(String host, String enc) throws IOException {
    FTPClient ftpClient = new FTPClient();
    ftpClient.setControlEncoding(enc);
    ftpClient.connect(host, CaesarConstants.FTP_PORT);
    return ftpClient;
  }

  /**
   * .
   *
   * @param host host
   * @param user user
   * @param pwd  pwd
   * @param file file
   * @param enc  enc
   */
  public static String createUrl(String host, String user, String pwd, String file,
      String enc) {
    StringBuilder sb = new StringBuilder("ftp://");
    try {
      String ftpUser = URLEncoder.encode(user.trim(), enc);
      String ftpPass = URLEncoder.encode(pwd, enc);
      sb.append(ftpUser);
      sb.append(":");
      sb.append(ftpPass);
      sb.append("@");
      sb.append(host);
      sb.append("/");
      sb.append(file);
    } catch (UnsupportedEncodingException ex) {
      LOG.log(Level.SEVERE, "createURL", ex);
    }
    return sb.toString();
  }
}

