package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.util.HashMap;
import java.util.Map;

/**
 * .
 *
 * @brief 定义各个版本的各种数据所占的字节数
 */
public class Version {

  private static final String UNKNOWN = "Unknown";

  protected Map<Class, Integer> classSizes = new HashMap<>();
  protected Map<Class, Integer> classCounts = new HashMap<>();
  protected Map<Class, Integer> classOffsets = new HashMap<>();
  protected Map<Class, ApiDataConverter> classConverter = new HashMap<>();

  public static final int SNMP_FLAG = 16777216;
  protected int versionValue = 0;

  protected Version() {

  }

  protected Version(int versionValue) {
    this.versionValue = versionValue;
  }

  public static Version getLeastVersion() {
    return new Version2x();
  }

  /**
   * 获取最新的版本.
   *
   * @return 最新的版本.
   */
  public static Version getLatestVersion() {
    String version = System.getProperty("latest-dkm-version");
    if (version != null) {
      int versionValue = Integer.parseInt(version);
      return getVersion(versionValue);
    } else {
      return new Version5x();
    }
  }

  /**
   * getVersion.
   */
  public static Version getVersion(int version) {
    int masterVersion = version >> 16;
    int subVersion = version & 0xff;
    // 兼容之前的版本
    if (masterVersion <= 2 || version == 0x30005) {
      return new Version2x();
    } else if (masterVersion == 3) {
      return new Version3x();
    } else if (masterVersion == 4) {
      return new Version4x();
    } else if (masterVersion == 5 && subVersion == 0) { // 5.0是音频联动版本
      return new Version50();
    } else if (masterVersion == 5 && subVersion > 0) {
      return new Version5x();
    } else {
      return new VersionTooNew();
    }
  }

  public boolean isVersionTooNew() {
    return false;
  }

  public int getMinCpuId() {
    return 1001;
  }

  public int getMaxCpuId() {
    return getMinCpuId() + getClassCount(CpuData.class) - 1;
  }

  public int getMinConId() {
    return 3001;
  }

  public int getMaxConId() {
    return getMinConId() + getClassCount(ConsoleData.class) - 1;
  }

  public int getNetworkCount() {
    return 1;
  }

  /**
   * 是否能够连接.
   */
  public boolean isConnectable() {
    return false;
  }

  /**
   * 是否有ldap功能.
   */
  public boolean hasLdap() {
    return false;
  }

  /**
   * 是否有snmp服务器2.
   */
  public boolean hasSnmpServer2() {
    return false;
  }

  /**
   * 是否有syslog服务器2.
   */
  public boolean hasSyslogServer2() {
    return false;
  }

  /**
   * 是否有级联矩阵数据.
   */
  public boolean hasMatrixData() {
    return false;
  }

  public int getMatrixDataMaxCount() {
    return 16;
  }

  /**
   * syslog的开关是否在syslogdata中.
   */
  public boolean isSyslogEnableSwitchInSyslogData() {
    return false;
  }

  /**
   * 是否为复杂的syslog数据.
   */
  public boolean isComplicatedSyslogData() {
    return false;
  }

  /**
   * 是否有snmp的端口配置选项.
   */
  public boolean hasSnmpPortOption() {
    return false;
  }

  /**
   * 是否有sntp的端口配置选项.
   */
  public boolean hasSntpPortOption() {
    return false;
  }

  /**
   * 获取设备恢复出厂设置的slot参数.
   */
  public int getDeviceFactoryResetSlot() {
    return 0;
  }

  /**
   * 获取重启所有板卡(包括cpu与io)的slot参数.
   */
  public int getRestartAllSlot() {
    return 0;
  }

  /**
   * 获取版本名称.
   */
  public String getName() {
    return String.format("%s(%x)", UNKNOWN, versionValue);
  }

  public int getVersionValue() {
    return versionValue;
  }

  public int getMetaDataSize() {
    return 0;
  }

  public int getConfigDataSize() {
    return 0;
  }

  public int getModuleCount() {
    return 65;
  }

  /**
   * 配置信息中是否有外设版本.
   *
   * @return 如果有，返回true
   */
  public boolean hasExtVersion() {
    return false;
  }

  /**
   * 配置信息中是否有外设状态信息.
   *
   * @return 如果有，返回true
   */
  public boolean hasExtenderStatusInfo() {
    return false;
  }

  /**
   * 配置信息中是否有外设分辨率.
   *
   * @return 如果有，返回true
   */
  public boolean hasExtResolution() {
    return false;
  }

  /**
   * 配置信息中是否有外设序列号.
   *
   * @return 如果有，返回true
   */
  public boolean hasExtSerial() {
    return false;
  }

  /**
   * TX是否有偏移设置.
   *
   * @return 如果有，返回true
   */
  public boolean hasTxOffset() {
    return false;
  }

  /**
   * getClassSize.
   */
  public int getClassSize(Class clazz) {
    if (classSizes.containsKey(clazz)) {
      return classSizes.get(clazz);
    }
    assert false;
    return 0;
  }

  /**
   * 获取数据读写器.
   *
   * @param clazz 数据类型的class
   * @param <T>   数据类型
   * @return 读写器
   */
  public <T extends CaesarCommunicatable> ApiDataConverter<T> getDataConverter(Class<T> clazz) {
    if (classConverter.containsKey(clazz)) {
      return classConverter.get(clazz);
    }
    return null;
  }

  /**
   * 获取数据数组的长度.
   *
   * @param clazz 数据类型.
   * @return 长度.
   */
  public int getClassCount(Class clazz) {
    if (classCounts.containsKey(clazz)) {
      return classCounts.get(clazz);
    }
    assert false;
    return 0;
  }

  /**
   * 获取数据在configdata的偏移.
   *
   * @param clazz 数据类型.
   * @return 偏移.
   */
  public int getClassOffset(Class clazz) {
    if (classOffsets.containsKey(clazz)) {
      return classOffsets.get(clazz);
    }
    assert false;
    return 0;
  }

  /**
   * 获取最大外设数量.
   *
   * @return 最大的外设数量
   */
  public int getMaxExtNumber() {
    return 1024;
  }


  static class VersionTooNew extends Version {

    @Override
    public boolean isVersionTooNew() {
      return true;
    }
  }

  public boolean hasMacroKeyName() {
    return false;
  }

  public int getFkeySize() {
    return 16;
  }

  public int getMacroSize() {
    return 16;
  }

  public boolean hasIrregularCrossScreen() {
    return false;
  }

  public boolean hasMacroDualHdmi() {
    return false;
  }

  /**
   * 是否有TxRX分组数据.
   */
  public boolean hasTxRxGroupData() {
    return false;
  }

  public boolean hasBranchData() {
    return false;
  }

  public boolean hasMultiviewData() {
    return false;
  }

  public boolean hasSourceClipData() {
    return false;
  }

  /**
   * Tx是否有远程开关机功能.
   */
  public boolean hasTxRemoteSwitching() {
    return false;
  }

  /**
   * 是否只读有效数据.
   */
  public boolean isOnlyReadValidData() {
    return false;
  }

  /**
   * 是否支持双主控.
   */
  public boolean hasDoubleBackupMatrix() {
    return false;
  }

  public boolean hasExtArgHighCompressRatio() {
    return false;
  }

  public boolean hasExtArgOsdMenu() {
    return false;
  }

  public boolean hasExtArgAudioTrigger() {
    return false;
  }

  public boolean hasExtArgEventEnable() {
    return false;
  }

  public boolean hasExtArgTriggerHoldTime() {
    return false;
  }

  public boolean supportClipTx() {
    return false;
  }

  public boolean hasExtArgIcronEnable() {
    return false;
  }

  public boolean hasExtArgUartBaudrate() {
    return false;
  }

  public boolean hasExtArgDoubledpEnable() {
    return false;
  }
}

