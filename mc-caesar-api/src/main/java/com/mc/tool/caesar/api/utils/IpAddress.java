package com.mc.tool.caesar.api.utils;

import com.mc.tool.caesar.api.exception.InvalidIpException;

/**
 * ip地址.
 */
public class IpAddress implements Comparable<IpAddress> {

  private byte[] ipOct = new byte[4];

  public IpAddress() {

  }

  /**
   * .
   */
  public IpAddress(String ipStr) {
    if (ipStr != null && !ipStr.isEmpty()) {
      byte[] newIpOct = IpUtil.getAddressByte(ipStr);
      if (newIpOct != null) {
        this.ipOct[0] = newIpOct[0];
        this.ipOct[1] = newIpOct[1];
        this.ipOct[2] = newIpOct[2];
        this.ipOct[3] = newIpOct[3];
      }
    }
  }

  /**
   * .
   */
  public IpAddress(byte a0, byte b0, byte c0, byte d0) {
    this.ipOct[0] = a0;
    this.ipOct[1] = b0;
    this.ipOct[2] = c0;
    this.ipOct[3] = d0;
  }

  public byte[] getData() {
    return this.ipOct.clone();
  }

  /**
   * .
   */
  public IpAddress mask(IpAddress ipMask) {
    IpAddress ipAddr = null;
    if (this.ipOct != null) {
      ipAddr = new IpAddress((byte) (this.ipOct[0] & ipMask.ipOct[0]),
          (byte) (this.ipOct[1] & ipMask.ipOct[1]), (byte) (this.ipOct[2] & ipMask.ipOct[2]),
          (byte) (this.ipOct[3] & ipMask.ipOct[3]));
    }
    return ipAddr;
  }

  public String getClassMask() throws InvalidIpException {
    return IpUtil.getClassMask(this);
  }

  @Override
  public String toString() {
    if (this.ipOct == null) {
      return "";
    }
    return IpUtil.getAddressString(this.ipOct);
  }

  public Long getIntIp() {
    return IpUtil.intAddress(toString());
  }

  @Override
  public boolean equals(Object oj) {
    if (oj instanceof IpAddress) {
      IpAddress ipAddr = (IpAddress) oj;

      return ipAddr.ipOct[1] == this.ipOct[1] && ipAddr.ipOct[2] == this.ipOct[2]
          && ipAddr.ipOct[3] == this.ipOct[3] && ipAddr.ipOct[0] == this.ipOct[0];
    }
    return false;
  }

  @Override
  public int hashCode() {
    int result = 17;
    result = 31 * result + this.ipOct[1];
    result = 31 * result + this.ipOct[2];
    result = 31 * result + this.ipOct[3];
    result = 31 * result + this.ipOct[0];

    return result;
  }

  @Override
  public int compareTo(IpAddress ipAddr) {
    return getIntIp().compareTo(ipAddr.getIntIp());
  }
}

