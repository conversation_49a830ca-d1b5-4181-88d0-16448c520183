package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.io.NormalStruct;

/**
 * .
 */
public class TxClipData extends NormalStruct {

  private Unsigned16 left = new Unsigned16();
  private Unsigned16 top = new Unsigned16();
  private Unsigned16 right = new Unsigned16();
  private Unsigned16 bottom = new Unsigned16();

  public int getLeft() {
    return left.get();
  }

  public void setLeft(int left) {
    this.left.set(left);
  }

  public int getTop() {
    return top.get();
  }

  public void setTop(int top) {
    this.top.set(top);
  }

  public int getRight() {
    return right.get();
  }

  public void setRight(int right) {
    this.right.set(right);
  }

  public int getBottom() {
    return bottom.get();
  }

  public void setBottom(int bottom) {
    this.bottom.set(bottom);
  }
}