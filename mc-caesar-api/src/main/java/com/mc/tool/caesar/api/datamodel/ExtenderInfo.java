package com.mc.tool.caesar.api.datamodel;

import lombok.Data;

/**
 * .
 */
@Data
public class ExtenderInfo {

  private int id;
  private String name;
  private String port;
  private ExtenderType type;
  private String serial;
  private long extraid;

  /**
   * .
   */
  public enum ExtenderType {
    TX, RX, USB, VP
  }

  /**
   * .
   */
  public enum ImportResult {
    FORMAT_ERROR, TYPE_ERROR, NAME_ERROR, ID_ERROR, UNKNOWN_ERROR, OK
  }

}
