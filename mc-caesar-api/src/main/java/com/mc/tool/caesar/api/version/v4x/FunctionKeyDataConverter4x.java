package com.mc.tool.caesar.api.version.v4x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData.MacroType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class FunctionKeyDataConverter4x implements ApiDataConverter<FunctionKeyData> {

  private static final int MACRO_TYPE_CON = 0x08;
  private static final int MACRO_TYPE_USER = 0x04;
  private final int dataSize;

  public FunctionKeyDataConverter4x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(FunctionKeyData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    int status = cfgReader.readByteValue();
    data.setStatus(status);

    int hostSubscript = cfgReader.read2ByteValue();
    data.setHostSubscript(hostSubscript);

    int lineHotkey = cfgReader.readInteger();
    data.setHotkey(lineHotkey & 0xffffff);
    data.setKeyline(lineHotkey >> 24);

    int handleMode = cfgReader.read2ByteValue();
    data.setMacroCmd(handleMode & 0xfff);

    int macroType = handleMode >> 12;
    if (macroType == MACRO_TYPE_CON) {
      data.setMacroType(MacroType.CON);
    } else if (macroType == MACRO_TYPE_USER) {
      data.setMacroType(MacroType.USER);
    } else {
      data.setMacroType(MacroType.ERROR);
    }

    // 3个int参数
    for (int i = 0; i < 3; i++) {
      data.setIntParam(i, cfgReader.read2ByteValue());
    }
    // 1个string参数
    data.setStringParam(0, cfgReader.readString(CaesarConstants.NAME_LEN));

    data.setMacroName(cfgReader.readString(CaesarConstants.NAME_LEN));

    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reservedSize = dataSize - readedSize;
    cfgReader.readByteArray(reservedSize);
  }

  @Override
  public void writeData(FunctionKeyData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeByte((byte) data.getStatus());
    cfgWriter.write2ByteSmallEndian(data.getHostSubscript());

    int lineHotkey = data.getKeyline() << 24;
    lineHotkey += data.getHotkey() & 0xffffff;
    cfgWriter.writeInteger(lineHotkey);

    int handleMode = 0;
    switch (data.getMacroType()) {
      case CON:
        handleMode = MACRO_TYPE_CON;
        break;
      case USER:
        handleMode = MACRO_TYPE_USER;
        break;
      default:
        break;
    }
    handleMode <<= 12;
    handleMode += data.getMacroCmd() & 0xfff;
    cfgWriter.write2ByteSmallEndian(handleMode);

    int paramSize = 3;
    for (int i = 0; i < paramSize; i++) {
      cfgWriter.write2ByteSmallEndian(data.getIntParam(i));
    }

    cfgWriter.writeString(data.getStringParam(0), CaesarConstants.NAME_LEN);

    cfgWriter.writeString(data.getMacroName(), CaesarConstants.NAME_LEN);

    long endSize = cfgWriter.getSize();
    cfgWriter.writeByteArray(new byte[(int) (dataSize - (endSize - startSize))]);
  }
}
