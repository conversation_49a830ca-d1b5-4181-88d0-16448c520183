package com.mc.tool.caesar.api;

import com.google.common.base.Charsets;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据库数据.
 */
@Slf4j
public class DbDataModel {
  private String hostname;

  @Getter
  @Setter
  private String dataBackup = "";

  private static final String GET_DB_BK_FORMAT = "http://%s:%d/mediacomm/config/db/backup";
  private static final int PORT = 8081;

  public DbDataModel() {
  }

  /**
   * 加载全部数据.
   */
  public void reloadAll() {
    if (hostname == null) {
      log.warn("Host name is null when reload db.");
      return;
    }
    try {
      URL apiUrl = new URL(String.format(GET_DB_BK_FORMAT, hostname, PORT));
      HttpURLConnection conn = (HttpURLConnection) apiUrl.openConnection();
      conn.setRequestMethod("GET");
      conn.setConnectTimeout(1000);
      conn.setReadTimeout(5000);
      conn.connect();
      if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
        conn.disconnect();
        throw new IOException(String.format("Error response code:%d", conn.getResponseCode()));
      }
      BufferedReader reader = new BufferedReader(
          new InputStreamReader(conn.getInputStream(), Charsets.UTF_8));
      dataBackup = reader.lines().collect(Collectors.joining("\n"));
      reader.close();
    } catch (IOException exception) {
      log.warn("Fail to reload dbdatamodel.", exception);
    } catch (RuntimeException runtimeException) {
      log.warn("Fail to reload dbdatamodel.", runtimeException);
    }
  }

  /**
   * 设置主机地址.
   *
   * @param hostname 主机地址
   */
  public void setConnection(String hostname) {
    this.hostname = hostname;
  }

}
