package com.mc.tool.caesar.api;

import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtAudioTrigger;
import com.mc.tool.caesar.api.datamodel.extargs.ExtBooleanArg;
import com.mc.tool.caesar.api.datamodel.extargs.ExtDpMode;
import com.mc.tool.caesar.api.datamodel.extargs.ExtUartBaudRate;
import com.mc.tool.caesar.api.datamodel.extargs.ExtVideoQp;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * 外设参数处理.
 */
public class CaesarExtArgUtility {
  /**
   * 从map读取外设参数接口.
   */
  @FunctionalInterface
  public interface CaesarExtArgReader {
    void apply(ExtenderData extenderData, int level1, int level2,
               Map<Integer, byte[]> result);
  }

  private static final Logger LOG = Logger.getLogger(CaesarExtArgUtility.class.getName());

  static final Map<CaesarExtArg, CaesarExtArgUtility.CaesarExtArgReader> READERS;

  static {
    READERS = new HashMap<>();
    READERS.put(CaesarExtArg.EXT_ARG_USB_ENABLE_ID, CaesarExtArgUtility::readExtArgUsgEnable);
    READERS.put(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID, CaesarExtArgUtility::readExtArgAnalogInput);
    READERS.put(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID,
        CaesarExtArgUtility::readExtArgTouchEnable);
    READERS.put(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER, CaesarExtArgUtility::readExtArgAudioTrigger);
    READERS.put(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME,
        CaesarExtArgUtility::readExtArgTriggerHoldTime);
    READERS.put(CaesarExtArg.EXT_ARG_EVENT_ENABLE, CaesarExtArgUtility::readExtArgEventEnable);
    READERS.put(CaesarExtArg.EXT_ARG_VIDEO_QP_ID, CaesarExtArgUtility::readExtArgVideoQp);
    READERS.put(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO,
        CaesarExtArgUtility::readHighCompressionRatio);
    READERS.put(CaesarExtArg.EXT_ARG_OSD_MENU, CaesarExtArgUtility::readExtArgOsdMenuEnable);
    READERS.put(CaesarExtArg.EXT_ARG_ICRON_ENABLE, CaesarExtArgUtility::readExtArgIcronEnable);
    READERS.put(CaesarExtArg.EXT_ARG_UART_BAUD_RATE, CaesarExtArgUtility::readExtArgUartBaudrate);
    READERS.put(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE, CaesarExtArgUtility::readExtArgDoubleDpEnable);
  }

  /**
   * 获取外设支持的参数列表.
   */
  public static List<CaesarExtArg> getExtArgs(Version version, ExtenderData extenderData) {
    boolean hasUsbEnable = CaesarExtArgUtility.checkUsbEnableArgExist(version, extenderData);
    boolean hasAnalogInput = CaesarExtArgUtility.checkAnalogInputArgExist(version, extenderData);
    boolean hasTouchingScreenId =
        CaesarExtArgUtility.checkTouchingScreenIdArgExist(version, extenderData);
    boolean hasAudioTrigger = CaesarExtArgUtility.checkAudioTriggerArgExist(version, extenderData);
    boolean hasTriggerHoldTime =
        CaesarExtArgUtility.checkTriggerHoldTimeArgExist(version, extenderData);
    boolean hasEventEnable = CaesarExtArgUtility.checkEventEnableArgExist(version, extenderData);
    boolean hasVideoQp = CaesarExtArgUtility.checkVideoQpArgExist(version, extenderData);
    boolean hasHighCompressionRatio =
        CaesarExtArgUtility.checkHighCompressionRatioArgExist(version, extenderData);
    boolean hasOsdMenu = CaesarExtArgUtility.checkOsdMenuArgExist(version, extenderData);
    boolean hasIcronEnable = CaesarExtArgUtility.checkIcronEnableArgExist(version, extenderData);
    boolean hasUartBaudRate =
        CaesarExtArgUtility.checkUartBaudRateArgExist(version, extenderData);
    boolean hasDoubleDpEnable =
        CaesarExtArgUtility.checkDoubleDpEnableArgExist(version, extenderData);
    Map<CaesarExtArg, Boolean> extArgs = new HashMap<>();
    extArgs.put(CaesarExtArg.EXT_ARG_USB_ENABLE_ID, hasUsbEnable);
    extArgs.put(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID, hasAnalogInput);
    extArgs.put(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID, hasTouchingScreenId);
    extArgs.put(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER, hasAudioTrigger);
    extArgs.put(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME, hasTriggerHoldTime);
    extArgs.put(CaesarExtArg.EXT_ARG_EVENT_ENABLE, hasEventEnable);
    extArgs.put(CaesarExtArg.EXT_ARG_VIDEO_QP_ID, hasVideoQp);
    extArgs.put(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO, hasHighCompressionRatio);
    extArgs.put(CaesarExtArg.EXT_ARG_OSD_MENU, hasOsdMenu);
    extArgs.put(CaesarExtArg.EXT_ARG_ICRON_ENABLE, hasIcronEnable);
    extArgs.put(CaesarExtArg.EXT_ARG_UART_BAUD_RATE, hasUartBaudRate);
    extArgs.put(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE, hasDoubleDpEnable);
    return extArgs.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey)
        .collect(Collectors.toList());
  }


  /**
   * 检查外设是否有usb使能参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkUsbEnableArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null;
  }

  /**
   * 检查外设是否有音频数据参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkAnalogInputArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null && extenderData.getExtenderStatusInfo().getHwSpecial().isMicIn();
  }

  /**
   * 检查外设是否有触屏使能参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkTouchingScreenIdArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null && extenderData.isCpuType();
  }

  /**
   * 检查外设是否有音频触发参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkAudioTriggerArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null && extenderData.isCpuType() && version.hasExtArgAudioTrigger();
  }

  /**
   * 检查外设是否有音频触发持续时间参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkTriggerHoldTimeArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null && extenderData.isCpuType() && version.hasExtArgTriggerHoldTime();
  }

  /**
   * 检查外设是否有事件使能参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkEventEnableArgExist(Version version, ExtenderData extenderData) {
    return extenderData != null && extenderData.isConType() && version.hasExtArgEventEnable();
  }

  /**
   * 检查外设是否有图像质量参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkVideoQpArgExist(Version version, ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    boolean is4k = extenderData.getExtenderStatusInfo().getVideoResolutionType()
        .equals(CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_30HZ) || extenderData
        .getExtenderStatusInfo().getVideoResolutionType()
        .equals(CaesarConstants.Extender.ExtenderVideoResolutionType.VIDEO_4K_60HZ);
    return extenderData.isCpuType() && is4k;
  }

  /**
   * 检查外设是否有高压缩率参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkHighCompressionRatioArgExist(Version version,
                                                          ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    return extenderData.isCpuType()
        && version.hasExtArgHighCompressRatio();
  }

  /**
   * 检查外设是否有osd菜单参数.
   *
   * @param version      主机版本
   * @param extenderData 外设
   * @return 如有返回true
   */
  public static boolean checkOsdMenuArgExist(Version version, ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    return extenderData.isConType() && version.hasExtArgOsdMenu();
  }

  /**
   * 检查外设是否有icron使能参数.
   */
  public static boolean checkIcronEnableArgExist(Version version, ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    CaesarConstants.Extender.SpecialExtenderType specialExtType =
        extenderData.getExtenderStatusInfo().getSpecialExtType();
    return version.hasExtArgIcronEnable()
        && specialExtType == CaesarConstants.Extender.SpecialExtenderType.HW_ATC
        && extenderData.getExtenderStatusInfo().getHwSpecial().isIcron();
  }

  /**
   * 检查外设是否有Uart波特率参数.
   */
  public static boolean checkUartBaudRateArgExist(Version version, ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    CaesarConstants.Extender.SpecialExtenderType specialExtType =
        extenderData.getExtenderStatusInfo().getSpecialExtType();
    return version.hasExtArgUartBaudrate() && specialExtType == CaesarConstants.Extender.SpecialExtenderType.HW_ATC;
  }

  /**
   * 检查外设是否有双屏使能参数.
   */
  public static boolean checkDoubleDpEnableArgExist(Version version, ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    CaesarConstants.Extender.SpecialExtenderType specialExtType =
        extenderData.getExtenderStatusInfo().getSpecialExtType();
    return version.hasExtArgDoubledpEnable() && specialExtType == CaesarConstants.Extender.SpecialExtenderType.HW_ATC;
  }

  /**
   * 从map读取事件使能参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgEventEnable(ExtenderData extenderData, int level1, int level2,
                                           Map<Integer, byte[]> result) {
    ExtBooleanArg eventEnable =
        Utilities.extractExtArg(
            result, CaesarExtArg.EXT_ARG_EVENT_ENABLE.getValue(), ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_EVENT_ENABLE, eventEnable != null);
    if (eventEnable == null) {
      LOG.warning(String.format("Get event enable error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_EVENT_ENABLE, eventEnable.getValue());
    }
  }

  /**
   * 从map读取触发持续时间参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgTriggerHoldTime(ExtenderData extenderData, int level1, int level2,
                                               Map<Integer, byte[]> result) {
    Number triggerHoldTime =
        Utilities.extractExtArg(result, CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME.getValue());
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME, triggerHoldTime != null);
    if (triggerHoldTime == null) {
      LOG.warning(String.format("Get trigger hold time error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_TRIGGER_HOLD_TIME, triggerHoldTime);
    }
  }

  /**
   * 从map读取音频触发参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgAudioTrigger(ExtenderData extenderData, int level1, int level2,
                                            Map<Integer, byte[]> result) {
    ExtAudioTrigger audioTrigger =
        Utilities.extractExtArg(
            result, CaesarExtArg.EXT_ARG_AUDIO_TRIGGER.getValue(), ExtAudioTrigger.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER, audioTrigger != null);
    if (audioTrigger == null) {
      LOG.warning(String.format("Get audio trigger error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_AUDIO_TRIGGER, audioTrigger);
    }
  }

  /**
   * 从map读取触屏使能参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgTouchEnable(ExtenderData extenderData, int level1, int level2,
                                           Map<Integer, byte[]> result) {
    ExtBooleanArg touchEnable = Utilities
        .extractExtArg(result, CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID.getValue(),
            ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID, touchEnable != null);
    if (touchEnable == null) {
      LOG.warning(String.format("Get touch enable error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_TOUCHING_SCREEN, touchEnable.getValue());
    }
  }

  /**
   * 从map读取触发图像质量参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgVideoQp(ExtenderData extenderData, int level1, int level2,
                                       Map<Integer, byte[]> result) {
    ExtVideoQp videoQp = Utilities
        .extractExtArg(result, CaesarExtArg.EXT_ARG_VIDEO_QP_ID.getValue(),
            ExtVideoQp.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_VIDEO_QP_ID, videoQp != null);
    if (videoQp == null) {
      LOG.warning(String.format("Get hdmi selection error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_VIDEO_QP, videoQp);
    }
  }

  /**
   * 从map读取usb使能参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgUsgEnable(ExtenderData extenderData, int level1, int level2,
                                         Map<Integer, byte[]> result) {
    ExtBooleanArg usbEnable = Utilities
        .extractExtArg(result, CaesarExtArg.EXT_ARG_USB_ENABLE_ID.getValue(),
            ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_USB_ENABLE_ID, usbEnable != null);
    if (usbEnable == null) {
      LOG.warning(String.format("Get usb enable error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_STATUS_USB_DISK_ENABLE, usbEnable.getValue());
    }
  }

  /**
   * 从map读取模拟音频输入参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgAnalogInput(ExtenderData extenderData, int level1, int level2,
                                           Map<Integer, byte[]> result) {
    ExtenderAnalogAudioInput input = Utilities
        .extractExtArg(result, CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID.getValue(),
            ExtenderAnalogAudioInput.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get analog audio input error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_ANALOG_AUDIO_INPUT, input);
    }
  }

  /**
   * 从map读取高压缩率参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readHighCompressionRatio(ExtenderData extenderData, int level1, int level2,
                                              Map<Integer, byte[]> result) {
    ExtBooleanArg input = Utilities
        .extractExtArg(result, CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO.getValue(),
            ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get high compression ratio error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_HIGH_COMPRESSION_RATIO, input.getValue());
    }
  }

  /**
   * 从map读取osd菜单参数.
   *
   * @param extenderData 外设
   * @param level1       slot
   * @param level2       port
   * @param result       所有参数集合
   */
  public static void readExtArgOsdMenuEnable(ExtenderData extenderData, int level1, int level2,
                                              Map<Integer, byte[]> result) {
    ExtBooleanArg input =
        Utilities.extractExtArg(result, CaesarExtArg.EXT_ARG_OSD_MENU.getValue(),
            ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_OSD_MENU, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get OSD menu error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_OSD_MENU, input.getValue());
    }
  }

  /**
   * 从map读取icron使能参数.
   */
  public static void readExtArgIcronEnable(ExtenderData extenderData, int level1, int level2,
                                           Map<Integer, byte[]> result) {
    ExtBooleanArg input =
        Utilities.extractExtArg(result, CaesarExtArg.EXT_ARG_ICRON_ENABLE.getValue(),
            ExtBooleanArg.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_ICRON_ENABLE, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get icron enable error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_ICRON_ENABLE, input.getValue());
    }
  }

  /**
   * 从map读取uart波特率参数.
   */
  public static void readExtArgUartBaudrate(ExtenderData extenderData, int level1, int level2,
                                            Map<Integer, byte[]> result) {
    ExtUartBaudRate input =
        Utilities.extractExtArg(result, CaesarExtArg.EXT_ARG_UART_BAUD_RATE.getValue(),
            ExtUartBaudRate.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_UART_BAUD_RATE, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get uart baudrate error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_UART_BAUDRATE, input);
    }
  }

  /**
   * 从map读取双DP使能参数.
   */
  public static void readExtArgDoubleDpEnable(ExtenderData extenderData, int level1, int level2,
                                              Map<Integer, byte[]> result) {
    ExtDpMode input =
        Utilities.extractExtArg(result, CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE.getValue(),
            ExtDpMode.class);
    extenderData.setExtArgExist(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE, input != null);
    if (input == null) {
      LOG.warning(
          String.format("Get double dp enable error. %d:%d", level1, level2));
    } else {
      extenderData.setProperty(ExtenderData.PROPERTY_DOUBLEDP_ENABLE, input);
    }
  }
}
