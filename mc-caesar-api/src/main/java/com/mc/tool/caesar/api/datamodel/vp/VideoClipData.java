package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.datamodel.CpuData;
import java.awt.Rectangle;

/**
 * .
 */
public class VideoClipData {

  private int clipX = 0;
  private int clipY = 0;
  private int clipWidth = 1;
  private int clipHeight = 1;
  private int outX = 0;
  private int outY = 0;
  private int outWidth = 1;
  private int outHeight = 1;
  private double alpha = 1.0;
  private int scaleH = 256;
  private int scaleL = 256;
  private CpuData cpuData;

  private Rectangle destRect = new Rectangle(); // 要剪切出来的clip窗口在大屏中的位置
  private Rectangle videoRect = new Rectangle(); // 整个视频窗口在大屏中的位置

  private int videoIndex;

  public int getClipX() {
    return clipX;
  }

  public void setClipX(int clipX) {
    this.clipX = clipX;
  }

  public int getClipY() {
    return clipY;
  }

  public void setClipY(int clipY) {
    this.clipY = clipY;
  }

  public int getClipWidth() {
    return clipWidth;
  }

  public void setClipWidth(int clipWidth) {
    this.clipWidth = clipWidth;
  }

  public int getClipHeight() {
    return clipHeight;
  }

  public void setClipHeight(int clipHeight) {
    this.clipHeight = clipHeight;
  }

  public int getOutX() {
    return outX;
  }

  public void setOutX(int outX) {
    this.outX = outX;
  }

  public int getOutY() {
    return outY;
  }

  public void setOutY(int outY) {
    this.outY = outY;
  }

  public int getOutWidth() {
    return outWidth;
  }

  public void setOutWidth(int outWidth) {
    this.outWidth = outWidth;
  }

  public int getOutHeight() {
    return outHeight;
  }

  public void setOutHeight(int outHeight) {
    this.outHeight = outHeight;
  }

  public double getAlpha() {
    return alpha;
  }

  public void setAlpha(double alpha) {
    this.alpha = alpha;
  }

  public int getScaleH() {
    return scaleH;
  }

  public void setScaleH(int scaleH) {
    this.scaleH = scaleH;
  }

  public int getScaleL() {
    return scaleL;
  }

  public void setScaleL(int scaleL) {
    this.scaleL = scaleL;
  }

  public CpuData getCpuData() {
    return cpuData;
  }

  public void setCpuData(CpuData cpuData) {
    this.cpuData = cpuData;
  }

  public void setVideoIndex(int index) {
    videoIndex = index;
  }

  public int getVideoIndex() {
    return videoIndex;
  }

  public void setDestRect(Rectangle destRect) {
    this.destRect = (Rectangle) destRect.clone();
  }

  public Rectangle getDestRect() {
    return (Rectangle) this.destRect.clone();
  }

  public void setVideoRect(Rectangle videoRect) {
    this.videoRect = (Rectangle) videoRect.clone();
  }

  public Rectangle getVideoRect() {
    return (Rectangle) this.videoRect.clone();
  }
}
