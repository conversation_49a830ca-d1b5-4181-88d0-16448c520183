package com.mc.tool.caesar.api.datamodel;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * .
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class ScreenOffsetConfig {

  /**
   * 激活TX偏移配置状态, 0:关闭、1：打开.
   */
  private int activeOffset = 0;
  private int totalResolutionX = 0;
  private int totalResolutionY = 0;
  //双HDMI使用，排列：0：左右、1：上下
  //private int arrangement = 0;
  private ScreenInfo screenInfo1 = new ScreenInfo();
  private ScreenInfo screenInfo2 = new ScreenInfo();
}
