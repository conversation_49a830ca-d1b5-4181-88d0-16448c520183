package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Idable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class MultiScreenData extends AbstractData
    implements DataObject, CaesarCommunicatable {

  public static final String PROPERTY_STATUS = "MultiScreenData.Status";
  public static final String PROPERTY_NAME = "MultiScreenData.Name";
  public static final String PROPERTY_TYPE = "MultiScreenData.Type";
  public static final String PROPERTY_CTRLCONID = "MultiScreenData.CtrlConId";
  public static final String PROPERTY_CTRLCONPORT = "MultiScreenData.CtrlConPort";
  public static final String PROPERTY_HORIZONTALNUMBER = "MultiScreenData.HorizontalNumber";
  public static final String PROPERTY_VERTICALNUMBER = "MultiScreenData.VerticalNumber";
  public static final String PROPERTY_FRAME = "MultiScreenData.Frame";
  public static final String PROPERTY_CONINFO = "MultiScreenData.ConInfo";
  public static final String PROPERTY_CONINFO_ID = "MultiScreenData.ConInfo.Id";
  public static final String PROPERTY_LAYOUT = "MultiScreenData.Layout";
  public static final String PROPERTY_ENABLE_MULTIVIEW_LINKAGE =
      "MultiScreenData.EnableMultiViewLinkage";

  @Expose
  private int status;
  @Expose
  private StringProperty nameProperty = new SimpleStringProperty("");
  @Expose
  private String name = "";
  @Getter
  @Expose
  private int type; // 跨屏类型 0：鼠标跨屏 1：手动跨屏 2:异性跨屏
  @Getter
  @Expose
  private IntegerProperty horizontalNumberProperty = new SimpleIntegerProperty();
  @Getter
  @Expose
  private int horizontalNumber = 0;

  @Getter
  @Expose
  private IntegerProperty verticalNumberProperty = new SimpleIntegerProperty();
  @Getter
  @Expose
  private int verticalNumber = 0;
  @Getter
  @Expose
  private int ctrlConId;
  @Getter
  @Expose
  private int currentConId;
  @Expose
  private IntegerProperty usFrame = new SimpleIntegerProperty(); // 红框提示时间

  @Expose
  private transient MultiScreenConInfo[] conInfos =
      new MultiScreenConInfo[CaesarConstants.MultiScreen.MAX_CON];
  @Expose
  private transient MultiScreenLayout[] layouts =
      new MultiScreenLayout[CaesarConstants.MultiScreen.MAX_CON];
  @Getter
  @Expose
  private int enableMultiviewLinkage;
  @Getter
  @Expose
  private BooleanProperty enableMultiviewLinkageProperty = new SimpleBooleanProperty(false);

  /**
   * .
   */
  public MultiScreenData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      int oid, String fqn) {
    super(pcs, configDataManager, oid, fqn);
    initCommitRollback();

    for (int i = 0; i < conInfos.length; i++) {
      conInfos[i] = new MultiScreenConInfo();
    }
    for (int i = 0; i < layouts.length; i++) {
      layouts[i] = new MultiScreenLayout();
    }
  }

  @Override
  public void initDefaults() {
    super.initDefaults();
    setStatus(0);
    setName("");
    setType(0);
    setHorizontalNumber(0);
    setVerticalNumber(0);
    setCtrlConId(0);
    setCurrentConId(0);
    setUsFrame(0);
    for (MultiScreenConInfo con : this.conInfos) {
      con.setId(0);
    }
    for (MultiScreenLayout layout : this.layouts) {
      layout.setX1(0);
      layout.setX2(0);
      layout.setY1(0);
      layout.setY2(0);
    }
  }

  @Override
  public int getId() {
    return getOid() + 1;
  }

  @Override
  public String getName() {
    return name;
  }

  public void setType(int type) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    this.type = type;
  }

  public void setCurrentConId(int currentConId) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    this.currentConId = currentConId;
  }

  /**
   * .
   */
  public void setName(String name) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.name;
    this.name = name;
    PlatformUtility.runInFxThread(() -> this.nameProperty.set(name));
    firePropertyChange(MultiScreenData.PROPERTY_NAME, oldValue, this.name);
  }

  public StringProperty getNameProperty() {
    return nameProperty;
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(MultiScreenData.PROPERTY_STATUS, oldValue, this.status);
  }

  public void setStatusActive(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.MultiScreen.Status.ACTIVE));
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.MultiScreen.Status.ACTIVE);
  }

  public void setStatusNew(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled, CaesarConstants.MultiScreen.Status.NEW));
  }

  public boolean isStatusNew() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.MultiScreen.Status.NEW);
  }

  /**
   * .
   */
  public void setHorizontalNumber(int hn) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.horizontalNumber;
    this.horizontalNumber = hn;
    PlatformUtility.runInFxThread(() -> this.horizontalNumberProperty.set(hn));
    firePropertyChange(MultiScreenData.PROPERTY_HORIZONTALNUMBER, oldValue, this.horizontalNumber);
  }

  /**
   * .
   */
  public void setVerticalNumber(int vn) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.verticalNumber;
    this.verticalNumber = vn;
    PlatformUtility.runInFxThread(() -> this.verticalNumberProperty.set(vn));
    firePropertyChange(MultiScreenData.PROPERTY_VERTICALNUMBER, oldValue, this.verticalNumber);
  }

  /**
   * .
   */
  public void setCtrlConId(int ctrlConId) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.ctrlConId;
    this.ctrlConId = ctrlConId;
    firePropertyChange(MultiScreenData.PROPERTY_CTRLCONID, oldValue, this.ctrlConId);
  }

  public int getUsFrame() {
    return this.usFrame.get();
  }

  /**
   * .
   */
  public void setUsFrame(int us) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.usFrame.get();
    this.usFrame.set(us);
    firePropertyChange(MultiScreenData.PROPERTY_FRAME, oldValue, this.usFrame.get());
  }

  public IntegerProperty getUsFrameProperty() {
    return usFrame;
  }

  @Override
  public void readData(CfgReader paramCfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<MultiScreenData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(MultiScreenData.class);
    converter.readData(this, paramCfgReader);
  }

  @Override
  public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<MultiScreenData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(MultiScreenData.class);
    converter.writeData(this, paramCfgWriter);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (MultiScreenData.PROPERTY_NAME.equals(propertyName)) {
      setName((String) value);
    } else if (MultiScreenData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus((Integer) value);
    } else if (MultiScreenData.PROPERTY_TYPE.equals(propertyName)) {
      setType((Integer) value);
    } else if (MultiScreenData.PROPERTY_CONINFO_ID.equals(propertyName)) {
      setConInfoId(indizes[0], (Integer) value);
    } else if (MultiScreenData.PROPERTY_CTRLCONID.equals(propertyName)) {
      setCtrlConId((Integer) value);
    } else if (MultiScreenData.PROPERTY_FRAME.equals(propertyName)) {
      setUsFrame((Integer) value);
    } else if (MultiScreenData.PROPERTY_HORIZONTALNUMBER.equals(propertyName)) {
      setHorizontalNumber((Integer) value);
    } else if (MultiScreenData.PROPERTY_VERTICALNUMBER.equals(propertyName)) {
      setVerticalNumber((Integer) value);
    }
  }

  @Override
  public void delete() {
    delete(true);
  }

  @Override
  public void delete(boolean internalCommit) {

    initDefaults();
    if (internalCommit) {
      commit(getThreshold());
    }
  }

  /**
   * 获取所有有效的con信息.
   */
  public Collection<MultiScreenConInfo> getConInfos() {
    Collection<MultiScreenConInfo> conInfos = new ArrayList<>();
    for (int i = 0; i < this.conInfos.length; i++) {
      MultiScreenConInfo conInfo = this.getConInfo(i);
      if (null != conInfo) {
        conInfos.add(conInfo);
      }
    }
    return conInfos;
  }

  public void setConInfos(MultiScreenConInfo[] infos) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    this.conInfos = infos.clone();
  }

  public MultiScreenConInfo getConInfo(int idx) {
    return this.conInfos[idx];
  }

  /**
   * .
   */
  public void setConInfo(int idx, MultiScreenConInfo conInfo) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MultiScreenConInfo oldValue = this.conInfos[idx];
    this.conInfos[idx] = conInfo;
    firePropertyChange(MultiScreenData.PROPERTY_CONINFO, oldValue, this.conInfos[idx], idx);
  }

  /**
   * .
   */
  public void setConInfoId(int idx, int id) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.conInfos[idx].getId();
    this.conInfos[idx].setId(id);
    firePropertyChange(MultiScreenData.PROPERTY_CONINFO_ID, oldValue, id, idx);
  }

  /**
   * 获取所有有效的con信息.
   */
  public List<MultiScreenLayout> getLayouts() {
    List<MultiScreenLayout> layouts = new ArrayList<>();
    for (int i = 0; i < this.layouts.length; i++) {
      MultiScreenLayout layout = this.getLayout(i);
      if (null != layout) {
        layouts.add(layout);
      }
    }
    return layouts;
  }

  public void setLayouts(MultiScreenLayout[] layouts) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    this.layouts = layouts.clone();
  }

  public MultiScreenLayout getLayout(int idx) {
    return this.layouts[idx];
  }

  /**
   * .
   */
  public void setLayout(int idx, MultiScreenLayout layout) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MultiScreenLayout oldValue = this.layouts[idx];
    this.layouts[idx] = layout;
    firePropertyChange(MultiScreenData.PROPERTY_LAYOUT, oldValue, this.layouts[idx], idx);
  }

  /**
   * .
   */
  public void setEnableMultiviewLinkage(int enableMultiviewLinkage) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (this.enableMultiviewLinkage != enableMultiviewLinkage) {
      int oldValue = this.enableMultiviewLinkage;
      this.enableMultiviewLinkage = enableMultiviewLinkage;
      PlatformUtility.runInFxThread(
          () -> enableMultiviewLinkageProperty.set(enableMultiviewLinkage != 0));
      firePropertyChange(MultiScreenData.PROPERTY_ENABLE_MULTIVIEW_LINKAGE,
          oldValue, this.enableMultiviewLinkage);
    }
  }

  /**
   * 是否是手动跨屏.
   */
  public boolean isManual() {
    return this.type == 1;
  }

  /**
   * 是否为异型跨屏.
   */
  public boolean isIrregular() {
    return this.type == 2;
  }

  /**
   * .
   */
  @Setter
  @Getter
  public static class MultiScreenConInfo implements CaesarCommunicatable, Idable {

    @Expose
    private int conId;

    @Override
    public void readData(CfgReader paramCfgReader) throws ConfigException {
      conId = paramCfgReader.read2ByteValue();
    }

    @Override
    public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
      paramCfgWriter.write2ByteSmallEndian(conId);
    }

    public void setId(int id) {
      this.conId = id;
    }

    @Override
    public int getId() {
      return this.conId;
    }
  }

  /**
   * .
   */
  @Setter
  @Getter
  public static class MultiScreenLayout implements CaesarCommunicatable {

    @Expose
    private int x1;
    @Expose
    private int y1;
    @Expose
    private int x2;
    @Expose
    private int y2;

    @Override
    public void readData(CfgReader paramCfgReader) throws ConfigException {
      x1 = paramCfgReader.readByteValue();
      y1 = paramCfgReader.readByteValue();
      x2 = paramCfgReader.readByteValue();
      y2 = paramCfgReader.readByteValue();
    }

    @Override
    public void writeData(CfgWriter paramCfgWriter) throws ConfigException {
      paramCfgWriter.writeByte((byte) x1);
      paramCfgWriter.writeByte((byte) y1);
      paramCfgWriter.writeByte((byte) x2);
      paramCfgWriter.writeByte((byte) y2);
    }
  }

}
