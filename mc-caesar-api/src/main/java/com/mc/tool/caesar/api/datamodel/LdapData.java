package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * LdapData.
 *
 * @brief ldap配置数据.
 */
public class LdapData extends AbstractData {

  private static final Logger LOG = Logger.getLogger(LdapData.class.getName());
  public static final String PROPERTY_BASE = "SystemConfigData.LdapData.";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "SystemConfigData.LdapData.Status";
  public static final String FIELD_ADDRESS = "Address";
  // System - Network LDAP下的 LDAP服务器
  public static final String PROPERTY_ADDRESS = "SystemConfigData.LdapData.Address";
  public static final String FIELD_PORT = "Port";
  // System - Network LDAP下的 端口号
  public static final String PROPERTY_PORT = "SystemConfigData.LdapData.Port";
  public static final String FIELD_BASE_DN = "BaseDN";
  // System - Network LDAP下的 基本DN
  public static final String PROPERTY_BASE_DN = "SystemConfigData.LdapData.BaseDN";
  public static final int BASEDN_LEN = 60;
  @Expose
  private int status; // @unknown 估计已弃用
  @Expose
  private byte[] address = new byte[4]; // server address
  @Expose
  private int port; // server port
  @Expose
  private String baseDn; // example:out=user,dc=mydomain,dc=net

  /**
   * .
   */
  public LdapData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      String fqn) {
    super(pcs, configDataManager, -1, fqn);

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    setStatus(0);

    this.port = 389;
    this.baseDn = "";
  }

  public byte[] getAddress() {
    return Arrays.copyOf(this.address, this.address.length);
  }

  /**
   * .
   */
  public void setAddress(byte[] address) {
    byte[] oldValue = this.address;
    this.address = Arrays.copyOf(address, address.length);
    //
    firePropertyChange(LdapData.PROPERTY_ADDRESS, oldValue, this.address, new int[0]);
  }

  public int getPort() {
    return this.port;
  }

  /**
   * .
   */
  public void setPort(int port) {
    int oldValue = this.port;
    this.port = port;
    firePropertyChange(LdapData.PROPERTY_PORT, Integer.valueOf(oldValue),
        Integer.valueOf(this.port), new int[0]);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(LdapData.PROPERTY_STATUS, Integer.valueOf(oldValue),
        Integer.valueOf(this.status), new int[0]);
  }

  public String getBaseDn() {
    return this.baseDn;
  }

  /**
   * .
   */
  public void setBaseDn(String baseDn) {
    String oldValue = this.baseDn;
    this.baseDn = baseDn;
    firePropertyChange(LdapData.PROPERTY_BASE_DN, oldValue, this.baseDn, new int[0]);
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    // LDAP服务器
    if (LdapData.PROPERTY_ADDRESS.equals(propertyName)) {
      setAddress(byte[].class.cast(value));
    } else if (LdapData.PROPERTY_PORT.equals(propertyName)) {
      setPort(Integer.class.cast(value).intValue());
    } else if (LdapData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus(Integer.class.cast(value).intValue());
    } else if (LdapData.PROPERTY_BASE_DN.equals(propertyName)) {
      setBaseDn(String.class.cast(value));
    }
  }

  /**
   * .
   */
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    for (int idx = this.address.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "writing {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      cfgWriter.write4Byte(this.address[idx - 1]);
    }
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), "Port"});
    }
    cfgWriter.writeInteger(getPort());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), SystemData.PROPERTY_INFO});
    }
    cfgWriter.writeString(getBaseDn(), BASEDN_LEN);
  }

  /**
   * .
   */
  public void readData(CfgReader cfgReader) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    int status = cfgReader.readInteger();
    if (!isPropertyChangedByUi(LdapData.PROPERTY_STATUS)) {
      setStatus(status);
    }
    byte[] newAddress = new byte[this.address.length];
    for (int idx = newAddress.length; idx > 0; idx--) {
      if (isLoggable) {
        LOG.log(logLevel, "reading {0}.{1}[{2}]",
            new Object[]{getFqn(), "Address", Integer.valueOf(idx - 1)});
      }
      newAddress[idx - 1] = cfgReader.read4ByteValue();
    }
    if (!isPropertyChangedByUi(LdapData.PROPERTY_ADDRESS)) {
      setAddress(newAddress);
    }
    int port = cfgReader.readInteger();
    if (!isPropertyChangedByUi(LdapData.PROPERTY_PORT)) {
      setPort(port);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), LdapData.PROPERTY_BASE_DN});
    }
    String baseDn = cfgReader.readString(BASEDN_LEN);
    if (!isPropertyChangedByUi(SystemData.PROPERTY_INFO)) {
      setBaseDn(baseDn);
    }
  }
}

