package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.io.NormalStruct;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.logging.Logger;

/**
 * .
 */
public class Vp6ConfigData extends VpConConfigData {

  static final Logger LOG = Logger.getLogger(Vp6ConfigData.class.getName());

  public static final int IN_PORT_COUNT = 8;
  public static final int IN_PORT_4K_COUNT = IN_PORT_COUNT / 2;
  public static final int VIRTUAL_PORT = 8;
  public static final int OUT_PORT_COUNT = 8;
  public static final int PROCESS_UNIT_COUNT = 20;
  public static final int LAYER_PER_PORT = 6;
  public static final int LAYER_COUNT = OUT_PORT_COUNT * LAYER_PER_PORT;
  public static final int MAX_UNIQUE_LAYER_CNT = 8;
  public static final int MAX_HORZ_CUT_CNT = 8;
  public static final int INPUT_INIT_VALUE = 0;

  /**
   * .
   */
  public static class Vp6ScalerData extends NormalStruct {

    public Unsigned16 inputWidth = new Unsigned16();
    public Unsigned16 inputHeight = new Unsigned16();
    public Unsigned16 outputWidth = new Unsigned16();
    public Unsigned16 outputHeight = new Unsigned16();
  }

  /**
   * .
   */
  public static class Vp6VertCutData extends NormalStruct {

    public Unsigned16 startLine = new Unsigned16();
    public Unsigned16 videoSrc = new Unsigned16();
  }

  /**
   * .
   */
  public static class Vp6HorzCutData extends NormalStruct {

    public Unsigned16 startPx = new Unsigned16();
    public Unsigned16 endPx = new Unsigned16();
    /**
     * 纵向剪切索引，如果为无效剪切，那么设置为PROCESS_UNIT_COUNT.
     */
    public Unsigned16 vertCutIndex = new Unsigned16();
    public Unsigned16 horzCutCount = new Unsigned16();

    public Vp6HorzCutData() {
      vertCutIndex.set(PROCESS_UNIT_COUNT);
    }
  }

  /**
   * .
   */
  public static class Vp6LayerConfig extends NormalStruct {

    public Unsigned16 startPx = new Unsigned16();
    public Unsigned16 startLine = new Unsigned16();
    public Unsigned16 width = new Unsigned16();
    public Unsigned16 height = new Unsigned16();
    public Unsigned16 alpha = new Unsigned16();
  }

  /**
   * .
   */
  public static class Vp6OutputConfig extends NormalStruct {

    public Vp6ScalerData layerConfig = inner(new Vp6ScalerData());
    /**
     * 输出图层，如果为无效输出，就设置为LAYER_PER_PORT.
     */
    public Unsigned8 outputLayer = new Unsigned8();
    public Unsigned8 outputPort = new Unsigned8();

    public Vp6OutputConfig() {
      outputLayer.set((short) LAYER_PER_PORT);
    }
  }

  /**
   * .
   */
  public static class Vp6OsdConfig extends NormalStruct {

    public Unsigned32 osdColor = new Unsigned32();
    public Unsigned32 bgColor = new Unsigned32();
    public Unsigned16[] oid = array(new Unsigned16[IN_PORT_COUNT]);
    public Unsigned16 osdLeft = new Unsigned16();
    public Unsigned16 osdTop = new Unsigned16();
    public Unsigned16 osdSegHeight = new Unsigned16();
    public Unsigned16 osdSegWidth = new Unsigned16();
    public Unsigned16 alpha = new Unsigned16();
  }

  public Vp6OsdConfig osdConfig = inner(new Vp6OsdConfig());
  public Vp6ScalerData[] inputScaler = array(new Vp6ScalerData[IN_PORT_COUNT]);
  public Vp6VertCutData[] vertCutData = array(new Vp6VertCutData[PROCESS_UNIT_COUNT]);
  public Vp6HorzCutData[] horzCutData = array(new Vp6HorzCutData[PROCESS_UNIT_COUNT]);
  public Vp6OutputConfig[] outputConfig = array(new Vp6OutputConfig[PROCESS_UNIT_COUNT]);
  public Vp6LayerConfig[] layerConfig = array(new Vp6LayerConfig[LAYER_COUNT]);
  @SuppressWarnings("unused")
  @SuppressFBWarnings("URF_UNREAD_FIELD")
  private Unsigned8[] reserved = array(new Unsigned8[6]);

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append(String.format(
        "osd:[t(%d) l(%d) w(%d) h(%d) c(%x) bc(%x) a(%d) "
            + "id(0x%x 0x%x 0x%x 0x%x 0x%x 0x%x 0x%x 0x%x)]%n",
        osdConfig.osdTop.get(), osdConfig.osdLeft.get(), osdConfig.osdSegWidth.get(),
        osdConfig.osdSegHeight.get(), osdConfig.osdColor.get(), osdConfig.bgColor.get(),
        osdConfig.alpha.get(), osdConfig.oid[0].get(), osdConfig.oid[1].get(),
        osdConfig.oid[2].get(), osdConfig.oid[3].get(), osdConfig.oid[4].get(),
        osdConfig.oid[5].get(), osdConfig.oid[6].get(), osdConfig.oid[7].get()));

    StringBuilder inputScalerFormat = new StringBuilder();
    for (int i = 0; i < IN_PORT_COUNT; i++) {
      inputScalerFormat.append(String.format("%d(%d %d %d %d) ", i, inputScaler[i].inputWidth.get(),
          inputScaler[i].inputHeight.get(), inputScaler[i].outputWidth.get(),
          inputScaler[i].outputHeight.get()));
    }
    builder.append(String.format("input(iw ih ow oh):[%s]%n", inputScalerFormat));

    StringBuilder vertCutFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      vertCutFormat.append(String.format("%d(%d %d) ", i, vertCutData[i].startLine.get(),
          vertCutData[i].videoSrc.get()));
    }
    builder.append(String.format("vert_cut(start_line, video_src):[%s]%n", vertCutFormat));

    StringBuilder horzCutFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      horzCutFormat.append(String.format("%d(%d %d %d %d) ", i, horzCutData[i].startPx.get(),
          horzCutData[i].endPx.get(), horzCutData[i].horzCutCount.get(),
          horzCutData[i].vertCutIndex.get()));
    }
    builder.append(String.format("horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[%s]%n",
        horzCutFormat));

    StringBuilder outputFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      outputFormat
          .append(String.format("%d(%d %d %d %d %d %d) ", i, outputConfig[i].outputPort.get(),
              outputConfig[i].outputLayer.get(), outputConfig[i].layerConfig.inputWidth.get(),
              outputConfig[i].layerConfig.inputHeight.get(),
              outputConfig[i].layerConfig.outputWidth.get(),
              outputConfig[i].layerConfig.outputHeight.get()));
    }
    builder.append(String.format("output(oport olayer iw ih ow oh):[%s]%n", outputFormat));

    int cnt = 0;
    StringBuilder layerFormat = new StringBuilder();
    for (int i = 0; i < LAYER_COUNT; i++) {
      if (layerConfig[i].width.get() == 0 && layerConfig[i].height.get() == 0) {
        continue;
      }
      cnt++;
      layerFormat.append(String.format("%d(%d %d %d %d %d) ", i, layerConfig[i].startLine.get(),
          layerConfig[i].startPx.get(), layerConfig[i].width.get(), layerConfig[i].height.get(),
          layerConfig[i].alpha.get()));
    }
    builder.append(String.format("layer(start_line start_px w h a):%d[%s]%n", cnt, layerFormat));
    return builder.toString();
  }

  @Override
  public void print() {
    LOG.info(String.format(
        "osd:[t(%d) l(%d) w(%d) h(%d) c(%x) bc(%x) a(%d) "
            + "id(0x%x 0x%x 0x%x 0x%x 0x%x 0x%x 0x%x 0x%x)]",
        osdConfig.osdTop.get(), osdConfig.osdLeft.get(), osdConfig.osdSegWidth.get(),
        osdConfig.osdSegHeight.get(), osdConfig.osdColor.get(), osdConfig.bgColor.get(),
        osdConfig.alpha.get(), osdConfig.oid[0].get(), osdConfig.oid[1].get(),
        osdConfig.oid[2].get(), osdConfig.oid[3].get(), osdConfig.oid[4].get(),
        osdConfig.oid[5].get(), osdConfig.oid[6].get(), osdConfig.oid[7].get()));

    StringBuilder inputScalerFormat = new StringBuilder();
    for (int i = 0; i < IN_PORT_COUNT; i++) {
      inputScalerFormat.append(String.format("%d(%d %d %d %d) ", i, inputScaler[i].inputWidth.get(),
          inputScaler[i].inputHeight.get(), inputScaler[i].outputWidth.get(),
          inputScaler[i].outputHeight.get()));
    }
    LOG.info(String.format("input(iw ih ow oh):[%s]", inputScalerFormat));

    StringBuilder vertCutFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      vertCutFormat.append(String.format("%d(%d %d) ", i, vertCutData[i].startLine.get(),
          vertCutData[i].videoSrc.get()));
    }
    LOG.info(String.format("vert_cut(start_line, video_src):[%s]", vertCutFormat));

    StringBuilder horzCutFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      horzCutFormat.append(String.format("%d(%d %d %d %d) ", i, horzCutData[i].startPx.get(),
          horzCutData[i].endPx.get(), horzCutData[i].horzCutCount.get(),
          horzCutData[i].vertCutIndex.get()));
    }
    LOG.info(String.format("horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[%s]",
        horzCutFormat));

    StringBuilder outputFormat = new StringBuilder();
    for (int i = 0; i < PROCESS_UNIT_COUNT; i++) {
      outputFormat
          .append(String.format("%d(%d %d %d %d %d %d) ", i, outputConfig[i].outputPort.get(),
              outputConfig[i].outputLayer.get(), outputConfig[i].layerConfig.inputWidth.get(),
              outputConfig[i].layerConfig.inputHeight.get(),
              outputConfig[i].layerConfig.outputWidth.get(),
              outputConfig[i].layerConfig.outputHeight.get()));
    }
    LOG.info(String.format("output(oport olayer iw ih ow oh):[%s]", outputFormat));

    int cnt = 0;
    StringBuilder layerFormat = new StringBuilder();
    for (int i = 0; i < LAYER_COUNT; i++) {
      if (layerConfig[i].width.get() == 0 && layerConfig[i].height.get() == 0) {
        continue;
      }
      cnt++;
      layerFormat.append(String.format("%d(%d %d %d %d %d) ", i, layerConfig[i].startLine.get(),
          layerConfig[i].startPx.get(), layerConfig[i].width.get(), layerConfig[i].height.get(),
          layerConfig[i].alpha.get()));
    }
    LOG.info(String.format("layer(start_line start_px w h a):%d[%s]", cnt, layerFormat));
  }

}
