package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.BitFieldEntry;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.Utilities;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * 级联中矩阵的数据.
 */
public final class MatrixData extends AbstractData implements CaesarCommunicatable {

  private static final Logger LOG = Logger.getLogger(MatrixData.class.getName());
  public static final String PROPERTY_BASE = "MatrixData.";
  public static final String FIELD_STATUS = "Status";
  public static final String PROPERTY_STATUS = "MatrixData.Status";
  public static final String PROPERTY_STATUS_ONLINE = "MatrixData.Status.Online";
  public static final String PROPERTY_STATUS_GETMASTER = "MatrixData.Status.GetMaster";
  public static final String PROPERTY_STATUS_CONNECT = "MatrixData.Status.Connect";
  public static final String PROPERTY_STATUS_GETCONFIG = "MatrixData.Status.GetConfig";
  public static final String PROPERTY_STATUS_GETMODULE = "MatrixData.Status.GetModule";
  public static final String PROPERTY_STATUS_SETCON = "MatrixData.Status.SetCon";
  public static final String PROPERTY_STATUS_SETCPU = "MatrixData.Status.SetCpu";
  public static final String PROPERTY_STATUS_SETCPUCON = "MatrixData.Status.SetCpuCon";
  public static final String PROPERTY_STATUS_CONNECTPORT = "MatrixData.Status.ConnectPort";
  public static final String PROPERTY_STATUS_POWERPORT = "MatrixData.Status.PowerPort";
  public static final String PROPERTY_STATUS_GETGRIDSTATUS = "MatrixData.Status.GetGridStatus";
  public static final String PROPERTY_STATUS_GETGRIDREQUEST = "MatrixData.Status.GetGridRequest";
  public static final String PROPERTY_STATUS_MASTER = "MatrixData.Status.Master";
  public static final String PROPERTY_STATUS_UPDATEGRID = "MatrixData.Status.UpdateGrid";
  public static final String PROPERTY_STATUS_DELETE = "MatrixData.Status.Delete";
  public static final String PROPERTY_STATUS_NEWDATA = "MatrixData.Status.NewData";
  public static final String PROPERTY_STATUS_ACTIVE = "MatrixData.Status.Active";
  public static final String PROPERTY_DEVICE = "MatrixData.Device";
  public static final String PROPERTY_PORTCOUNT = "MatrixData.PortCount";
  public static final String PROPERTY_FIRSTPORT = "MatrixData.FirstPort";
  public static final String PROPERTY_LASTPORT = "MatrixData.LastPort";
  public static final String PROPERTY_FIRSTMODULE = "MatrixData.FirstModule";
  public static final String PROPERTY_LASTMODULE = "MatrixData.LastModule";
  public static final String PROPERTY_HOSTADDRESS = "MatrixData.HostAddress";
  public static final String PROPERTY_MATRIX_TYPE = "MatrixData.MatrixType";
  public static final Map<String, Collection<BitFieldEntry>> BIT_FIELD_MAP;
  private static final int HOST_LENGTH = 4;
  private static final int RESERVED_COUNT = 8;
  @Expose
  private int status; //
  @Expose
  private String device = ""; // 设备名称
  @Expose
  private int portCount = 0; // 端口的个数
  @Expose
  private int firstPort = 0; // 在级联矩阵中第一个端口的序号
  @Expose
  private int lastPort = 0; // 在级联矩阵中最后一个端口的序号
  @Expose
  private int firstModule = 0; // 在级联矩阵中第一个模块的序号
  @Expose
  private int lastModule = 0; // 在级联矩阵中最后一个模块的序号
  @Expose
  private byte[] hostAddress = new byte[]{0, 0, 0, 0}; // ip地址
  @Expose
  private int matrixType = 0; //主机类型

  static {
    Collection<BitFieldEntry> bitMapStatus = new HashSet<BitFieldEntry>();

    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_ONLINE,
        new int[]{CaesarConstants.Matrix.Status.ONLINE}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETMASTER,
        new int[]{CaesarConstants.Matrix.Status.GETMASTER}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_CONNECT,
        new int[]{CaesarConstants.Matrix.Status.CONNECT}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETCONFIG,
        new int[]{CaesarConstants.Matrix.Status.GETCONFIG}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETMODULE,
        new int[]{CaesarConstants.Matrix.Status.GETMODULE}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_SETCON,
        new int[]{CaesarConstants.Matrix.Status.SETCON}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_SETCPU,
        new int[]{CaesarConstants.Matrix.Status.SETCPU}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_SETCPUCON,
        new int[]{CaesarConstants.Matrix.Status.SETCPUCON}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_CONNECTPORT,
        new int[]{CaesarConstants.Matrix.Status.CONNECTPORT}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_POWERPORT,
        new int[]{CaesarConstants.Matrix.Status.POWERPORT}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETGRIDSTATUS,
        new int[]{CaesarConstants.Matrix.Status.GETGRIDSTATUS}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETGRIDREQUEST,
        new int[]{CaesarConstants.Matrix.Status.GETGRIDREQUEST}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_GETMASTER,
        new int[]{CaesarConstants.Matrix.Status.MASTER}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_UPDATEGRID,
        new int[]{CaesarConstants.Matrix.Status.UPDATEGRID}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_DELETE,
        new int[]{CaesarConstants.Matrix.Status.DELETE}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_NEWDATA,
        new int[]{CaesarConstants.Matrix.Status.NEWDATA}));
    bitMapStatus.add(new BitFieldEntry(MatrixData.PROPERTY_STATUS_ACTIVE,
        new int[]{CaesarConstants.Matrix.Status.ACTIVE}));

    Map<String, Collection<BitFieldEntry>> bitMaps =
        new HashMap<String, Collection<BitFieldEntry>>();

    bitMaps.put(MatrixData.PROPERTY_STATUS, Collections.unmodifiableCollection(bitMapStatus));

    BIT_FIELD_MAP = Collections.unmodifiableMap(bitMaps);
  }

  /**
   * .
   */
  public MatrixData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    super(pcs, configDataManager, oid, fqn);

    initCommitRollback();
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    setDevice("");
    setFirstModule(0);
    setFirstPort(0);
    setLastModule(0);
    setLastPort(0);
    setHostAddress(new byte[]{0, 0, 0, 0});
    setPortCount(0);
    setStatus(0);
  }

  public int getStatus() {
    return this.status;
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(MatrixData.PROPERTY_STATUS, Integer.valueOf(oldValue),
        Integer.valueOf(this.status), new int[0]);
  }

  public boolean isStatusOnline() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.ONLINE);
  }

  public boolean isStatusGetMaster() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.GETMASTER);
  }

  public boolean isStatusConnect() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.CONNECT);
  }

  public boolean isStatusGetConfig() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.GETCONFIG);
  }

  public boolean isStatusGetModule() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.GETMODULE);
  }

  public boolean isStatusSetCon() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.SETCON);
  }

  public boolean isStatusSetCpu() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.SETCPU);
  }

  public boolean isStatusSetCpuCon() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.SETCPUCON);
  }

  public boolean isStatusConnectPort() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.CONNECTPORT);
  }

  public boolean isStatusPowerPort() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.POWERPORT);
  }

  public boolean isStatusGetGridStatus() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.GETGRIDSTATUS);
  }

  public boolean isStatusMaster() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.MASTER);
  }

  public boolean isStatusUpdateGrid() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.UPDATEGRID);
  }

  public boolean isStatusDelete() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.DELETE);
  }

  public boolean isStatusNewData() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.NEWDATA);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), CaesarConstants.Matrix.Status.ACTIVE);
  }

  public void setStatusOnline(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.ONLINE}));
  }

  public void setStatusGetMaster(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.GETMASTER}));
  }

  public void setStatusConnect(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.CONNECT}));
  }

  public void setStatusGetConfig(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.GETCONFIG}));
  }

  public void setStatusGetModule(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.GETMODULE}));
  }

  public void setStatusSetCon(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.SETCON}));
  }

  public void setStatusSetCpu(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.SETCPU}));
  }

  public void setStatusSetCpuCon(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.SETCPUCON}));
  }

  public void setStatusConnectPort(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.CONNECTPORT}));
  }

  public void setStatusPowerPort(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.POWERPORT}));
  }

  public void setStatusGetGridStatus(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.GETGRIDSTATUS}));
  }

  public void setStatusMaster(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.MASTER}));
  }

  public void setStatusUpdateGrid(boolean enabled) {
    setStatus(Utilities.setBits(getStatus(), enabled,
        new int[]{CaesarConstants.Matrix.Status.UPDATEGRID}));
  }

  public void setStatusDelete(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.DELETE}));
  }

  public void setStatusNewData(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.NEWDATA}));
  }

  public void setStatusActive(boolean enabled) {
    setStatus(
        Utilities.setBits(getStatus(), enabled, new int[]{CaesarConstants.Matrix.Status.ACTIVE}));
  }

  public String getDevice() {
    return this.device;
  }

  /**
   * .
   */
  public void setDevice(String device) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.device;
    this.device = device;

    firePropertyChange(MatrixData.PROPERTY_DEVICE, oldValue, device, new int[0]);
  }

  public int getPortCount() {
    return this.portCount;
  }

  /**
   * .
   */
  public void setPortCount(int portCount) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.portCount;
    this.portCount = portCount;

    firePropertyChange(MatrixData.PROPERTY_PORTCOUNT, Integer.valueOf(oldValue),
        Integer.valueOf(portCount), new int[0]);
  }

  /**
   * 设置主机l类型.
   *
   * @param matrixType 主机类型
   */
  public void setMatrixType(int matrixType) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.matrixType;
    this.matrixType = matrixType;
    firePropertyChange(MatrixData.PROPERTY_MATRIX_TYPE, Integer.valueOf(oldValue),
        Integer.valueOf(matrixType), new int[0]);
  }

  public int getMatrixType() {
    return matrixType;
  }

  public int getFirstPort() {
    return this.firstPort;
  }

  /**
   * .
   */
  public void setFirstPort(int firstPort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.firstPort;
    this.firstPort = firstPort;

    firePropertyChange(MatrixData.PROPERTY_FIRSTPORT, Integer.valueOf(oldValue),
        Integer.valueOf(firstPort), new int[0]);
  }

  public int getLastPort() {
    return this.lastPort;
  }

  /**
   * .
   */
  public void setLastPort(int lastPort) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.lastPort;
    this.lastPort = lastPort;

    firePropertyChange(MatrixData.PROPERTY_LASTPORT, Integer.valueOf(oldValue),
        Integer.valueOf(lastPort), new int[0]);
  }

  public int getFirstModule() {
    return this.firstModule;
  }

  /**
   * .
   */
  public void setFirstModule(int firstModule) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.firstModule;
    this.firstModule = firstModule;

    firePropertyChange(MatrixData.PROPERTY_FIRSTMODULE, Integer.valueOf(oldValue),
        Integer.valueOf(firstModule), new int[0]);
  }

  public int getLastModule() {
    return this.lastModule;
  }

  /**
   * .
   */
  public void setLastModule(int lastModule) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.lastModule;
    this.lastModule = lastModule;

    firePropertyChange(MatrixData.PROPERTY_LASTMODULE, Integer.valueOf(oldValue),
        Integer.valueOf(lastModule), new int[0]);
  }

  /**
   * .
   */
  public byte[] getHostAddress() {
    if (this.hostAddress != null) {
      return (byte[]) this.hostAddress.clone();
    }
    return new byte[0];

  }

  public String getHostAddressString() {
    return IpUtil.getAddressString(getHostAddress());
  }

  /**
   * .
   */
  public void setHostAddress(byte[] hostAddress) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (hostAddress != null) {
      byte[] oldValue = this.hostAddress;
      this.hostAddress = Arrays.copyOf(hostAddress, hostAddress.length);

      firePropertyChange(MatrixData.PROPERTY_HOSTADDRESS, oldValue, hostAddress, new int[0]);
    }
  }

  @Override
  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    if (MatrixData.PROPERTY_STATUS.equals(propertyName)) {
      setStatus(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_DEVICE.equals(propertyName)) {
      setDevice(String.class.cast(value));
    } else if (MatrixData.PROPERTY_PORTCOUNT.equals(propertyName)) {
      setPortCount(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_FIRSTPORT.equals(propertyName)) {
      setFirstPort(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_LASTPORT.equals(propertyName)) {
      setLastPort(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_FIRSTMODULE.equals(propertyName)) {
      setFirstModule(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_LASTMODULE.equals(propertyName)) {
      setLastModule(Integer.class.cast(value).intValue());
    } else if (MatrixData.PROPERTY_HOSTADDRESS.equals(propertyName)) {
      setHostAddress(byte[].class.cast(value));
    }
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    int status = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_STATUS)) {
      setStatus(status);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Device"});
    }
    String device = cfgReader.readString(CaesarConstants.NAME_LEN);
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_DEVICE)) {
      setDevice(device);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "PortCount"});
    }
    int portCount = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_PORTCOUNT)) {
      setPortCount(portCount);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "FirstPort"});
    }
    int firstPort = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_FIRSTPORT)) {
      setFirstPort(firstPort);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "LastPort"});
    }
    int lastPort = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_LASTPORT)) {
      setLastPort(lastPort);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "FirstModule"});
    }
    int firstModule = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_FIRSTMODULE)) {
      setFirstModule(firstModule);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "LastModule"});
    }
    int lastModule = cfgReader.readInteger();
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_LASTMODULE)) {
      setLastModule(lastModule);
    }
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "HostAddress"});
    }
    byte[] host = cfgReader.readByteArray(HOST_LENGTH);
    if (!isPropertyChangedByUi(MatrixData.PROPERTY_HOSTADDRESS)) {
      setHostAddress(host);
    }

    int type = cfgReader.readInteger();
    setMatrixType(type);

    cfgReader.readByteArray(RESERVED_COUNT);
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    Level logLevel = Level.FINER;
    boolean isLoggable = LOG.isLoggable(logLevel);
    if (isLoggable) {
      LOG.log(logLevel, "reading {0}.{1}", new Object[]{getFqn(), "Status"});
    }
    cfgWriter.writeInteger(getStatus());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), MatrixData.PROPERTY_DEVICE});
    }
    cfgWriter.writeString(getDevice(), CaesarConstants.NAME_LEN);
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), MatrixData.PROPERTY_PORTCOUNT});
    }
    cfgWriter.writeInteger(getPortCount());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), MatrixData.PROPERTY_FIRSTPORT});
    }
    cfgWriter.writeInteger(getFirstPort());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), MatrixData.PROPERTY_LASTPORT});
    }
    cfgWriter.writeInteger(getLastPort());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}",
          new Object[]{getFqn(), MatrixData.PROPERTY_FIRSTMODULE});
    }
    cfgWriter.writeInteger(getFirstModule());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}", new Object[]{getFqn(), MatrixData.PROPERTY_LASTMODULE});
    }
    cfgWriter.writeInteger(getLastModule());
    if (isLoggable) {
      LOG.log(logLevel, "writing {0}.{1}",
          new Object[]{getFqn(), MatrixData.PROPERTY_HOSTADDRESS});
    }
    cfgWriter.writeByteArray(
        getHostAddress() == new byte[0] ? new byte[]{0, 0, 0, 0} : getHostAddress());
    cfgWriter.writeInteger(getMatrixType());
    cfgWriter.writeByteArray(new byte[RESERVED_COUNT]);
  }
}

