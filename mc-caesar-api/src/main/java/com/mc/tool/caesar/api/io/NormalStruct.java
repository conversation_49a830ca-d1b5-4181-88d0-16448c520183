package com.mc.tool.caesar.api.io;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteOrder;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * .
 */
public class NormalStruct extends Struct implements Cloneable {

  private static final Logger LOG = Logger.getLogger(NormalStruct.class.getName());

  @Override
  public ByteOrder byteOrder() {
    return (outer != null) ? outer.byteOrder() : ByteOrder.LITTLE_ENDIAN;
  }

  @Override
  public boolean isPacked() {
    return true;
  }

  /**
   * .
   */
  public void reset() {
    byte[] resetBytes = new byte[size()];
    ByteArrayInputStream bais = new ByteArrayInputStream(resetBytes);
    try {
      read(bais);
      bais.close();
    } catch (IOException ex) {
      LOG.log(Level.WARNING, "Fail to reset!", ex);
    }
  }

  @Override
  public Object clone() throws CloneNotSupportedException {
    super.clone();
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    try {
      write(baos);
      NormalStruct result = this.getClass().newInstance();
      result.read(new ByteArrayInputStream(baos.toByteArray()));
      return result;
    } catch (IOException | InstantiationException | IllegalAccessException ex) {
      throw new AssertionError(ex);
    }

  }
}
