package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.interfaces.CaesarCommunicatable;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.api.version.ApiDataConverter;
import lombok.Getter;

/**
 * FunctionKeyData.
 *
 * @brief 键盘的宏配置信息.
 */
public final class FunctionKeyData extends AbstractData implements CaesarCommunicatable {

  public static final String PROPERTY_BASE = "FunctionKeyData.";
  public static final String PROPERTY_STATUS = PROPERTY_BASE + "Status";
  public static final String PROPERTY_HOST_SUBSCRIPT = PROPERTY_BASE + "HostSubscript";
  public static final String PROPERTY_HOTKEY = PROPERTY_BASE + "Hotkey";
  public static final String PROPERTY_KEYLINE = PROPERTY_BASE + "KeyLine";
  public static final String PROPERTY_MACROCMD = PROPERTY_BASE + "MacroCmd";
  public static final String PROPERTY_MACROTYPE = PROPERTY_BASE + "MacroType";
  public static final String PROPERTY_MACRO_INTPARAM = PROPERTY_BASE + "MacroIntParam";
  public static final String PROPERTY_MACRO_STRPARAM = PROPERTY_BASE + "MacroStrParam";
  public static final String PROPERTY_MACRO_NAME = PROPERTY_BASE + "MacroName";

  @Expose
  @Getter
  private int status;
  @Expose
  @Getter
  private int hostSubscript;

  @Expose
  @Getter
  private int hotkey;

  @Expose
  @Getter
  private int keyline;

  @Expose
  @Getter
  private int macroCmd;

  @Expose
  @Getter
  private MacroType macroType = MacroType.CON;

  private int[] intParams = new int[4]; // 整型参数，4个是预留，实际上没有那么多

  private String[] strParams = new String[4]; // 字符串参数，4个是预留，实际上没有那么多

  private String macroName = "";

  /**
   * .
   */
  public FunctionKeyData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager,
      int oid, String fqn) {
    super(pcs, configDataManager, oid, fqn);

    initCommitRollback();
  }

  /**
   * .
   */
  public void setHostSubscript(int hostSubscript) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.hostSubscript;
    this.hostSubscript = hostSubscript;
    firePropertyChange(FunctionKeyData.PROPERTY_HOST_SUBSCRIPT, oldValue, this.hostSubscript);
  }

  /**
   * .
   */
  public void setStatus(int status) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.status;
    this.status = status;
    firePropertyChange(FunctionKeyData.PROPERTY_STATUS, oldValue, this.status);
  }

  public boolean isStatusActive() {
    return Utilities.areBitsSet(getStatus(), 0x80);
  }

  public void setStatusActive(boolean active) {
    setStatus(Utilities.setBits(getStatus(), active, 0x80));
  }

  /**
   * 设置热键.
   *
   * @param hotkey 热键
   */
  public void setHotkey(int hotkey) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.hotkey;
    this.hotkey = hotkey;
    firePropertyChange(FunctionKeyData.PROPERTY_HOTKEY, oldValue, this.hotkey);
  }

  /**
   * 设置宏位置.
   *
   * @param keyline 宏位置
   */
  public void setKeyline(int keyline) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.keyline;
    this.keyline = keyline;
    firePropertyChange(FunctionKeyData.PROPERTY_KEYLINE, oldValue, this.keyline);
  }

  /**
   * 设置宏命令.
   *
   * @param macroCmd 宏命令
   */
  public void setMacroCmd(int macroCmd) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    int oldValue = this.macroCmd;
    this.macroCmd = macroCmd;
    firePropertyChange(FunctionKeyData.PROPERTY_MACROCMD, oldValue, this.macroCmd);
  }

  /**
   * 设置宏类型.
   *
   * @param macroType 宏类型
   */
  public void setMacroType(MacroType macroType) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    MacroType oldValue = this.macroType;
    this.macroType = macroType;
    firePropertyChange(FunctionKeyData.PROPERTY_MACROTYPE, oldValue, this.macroType);
  }

  /**
   * 设置整型参数.
   *
   * @param index 参数索引
   * @param value 参数值
   */
  public void setIntParam(int index, int value) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (index < 0 || index >= intParams.length) {
      return;
    }
    int oldValue = intParams[index];
    intParams[index] = value;
    firePropertyChange(FunctionKeyData.PROPERTY_MACRO_INTPARAM, oldValue, value, index);
  }

  /**
   * 获取整型参数.
   *
   * @param index 参数索引
   * @return 参数值
   */
  public int getIntParam(int index) {
    if (index < 0 || index >= intParams.length) {
      return -1;
    }
    return intParams[index];
  }

  /**
   * 设置字符串参数.
   *
   * @param index 参数索引
   * @param param 参数值
   */
  public void setStringParam(int index, String param) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    if (index < 0 || index >= strParams.length) {
      return;
    }
    String oldValue = strParams[index];
    strParams[index] = param;
    firePropertyChange(FunctionKeyData.PROPERTY_MACRO_STRPARAM, oldValue, param, index);
  }

  /**
   * 获取字符串参数.
   *
   * @param index 参数索引
   * @return 参数值
   */
  public String getStringParam(int index) {
    if (index < 0 || index >= strParams.length) {
      return "";
    } else {
      return strParams[index];
    }
  }

  /**
   * 设置宏名称.
   */
  public void setMacroName(String macroName) {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    String oldValue = this.macroName;
    this.macroName = macroName;
    firePropertyChange(FunctionKeyData.PROPERTY_MACRO_NAME, oldValue, this.macroName);
  }

  public String getMacroName() {
    return macroName;
  }

  public boolean isConsole() {
    return macroType == MacroType.CON;
  }

  @Override
  public void writeData(CfgWriter cfgWriter) throws ConfigException {
    ApiDataConverter<FunctionKeyData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(FunctionKeyData.class);
    converter.writeData(this, cfgWriter);
  }

  @Override
  public void readData(CfgReader cfgReader) throws ConfigException {
    PlatformUtility.checkThreadPrefix(CaesarConstants.DATA_THREAD_PREFIX);
    ApiDataConverter<FunctionKeyData> converter = getConfigDataManager().getConfigMetaData()
        .getUtilVersion().getDataConverter(FunctionKeyData.class);
    converter.readData(this, cfgReader);
  }

  /**
   * 复制数据.
   *
   * @param functionKeyData 复制源
   */
  public void readData(FunctionKeyData functionKeyData) {
    setStatus(functionKeyData.getStatus());
    setMacroCmd(functionKeyData.getMacroCmd());
    setMacroType(functionKeyData.getMacroType());
    setHotkey(functionKeyData.getHotkey());
    setKeyline(functionKeyData.getKeyline());
    setHostSubscript(functionKeyData.getHostSubscript());
    for (int i = 0; i < intParams.length; i++) {
      setIntParam(i, functionKeyData.getIntParam(i));
    }
    for (int i = 0; i < strParams.length; i++) {
      setStringParam(i, functionKeyData.getStringParam(i));
    }
  }

  /**
   * 重置数据.
   */
  public void reset() {
    setStatus(0);
    setMacroCmd(0);
    setHotkey(0);
    setHostSubscript(0);
    setKeyline(0);
    for (int i = 0; i < intParams.length; i++) {
      setIntParam(i, 0);
    }
    for (int i = 0; i < strParams.length; i++) {
      setStringParam(i, "");
    }
  }

  /**
   * .
   */
  public enum MacroType {
    CON, USER, ERROR
  }
}

