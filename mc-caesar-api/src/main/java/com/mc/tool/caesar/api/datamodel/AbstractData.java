package com.mc.tool.caesar.api.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.caesar.api.ConfigDataManager;
import com.mc.tool.caesar.api.interfaces.BaseObject;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CustomPropertyChangeSupport;
import com.mc.tool.caesar.api.utils.DefaultCommitRollback;
import com.mc.tool.caesar.api.utils.ObjectUtility;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * AbstractData.
 */
public abstract class AbstractData extends DefaultCommitRollback
    implements BaseObject, Comparable<AbstractData> {

  public static final Threshold THRESHOLD_UI_LOCAL_CHANGES = Threshold.ONE;
  public static final Threshold THRESHOLD_LOCAL_CHANGES = Threshold.FIVE;
  private static final Logger LOG = Logger.getLogger(AbstractData.class.getName());
  protected final CustomPropertyChangeSupport pcs;
  @Expose
  private final int oid; // 对象在数组中的序号，从0开始初始值为-1，由软件生成
  private final String fqn; // 类型识别字符串，可知道此对象的棣属关系，如ConfigData对象的CpuData成员的fqn就是ConfigData.CpuData
  @Expose
  private Threshold threshold; // 回滚的threshold

  private ConfigDataManager configDataManager;

  /**
   * .
   */
  public AbstractData(CustomPropertyChangeSupport pcs, ConfigDataManager configDataManager, int oid,
      String fqn) {
    this.pcs = pcs;
    this.oid = oid;
    this.fqn = fqn;
    this.configDataManager = configDataManager;
    this.threshold = THRESHOLD_LOCAL_CHANGES;
  }

  protected CustomPropertyChangeSupport getPropertyChangeSupport() {
    return this.pcs;
  }

  public final ConfigDataManager getConfigDataManager() {
    return this.configDataManager;
  }

  protected final String getFqn() {
    return this.fqn;
  }

  @Override
  public final int getOid() {
    return this.oid;
  }

  public void setThreshold(Threshold threshold) {
    this.threshold = threshold;
  }

  public Threshold getThreshold() {
    return this.threshold;
  }

  protected final void firePropertyChange(String propertyName, Object oldValue, Object newValue,
      int... indizes) {
    if (LOG.isLoggable(Level.FINER)) {
      LOG.log(Level.FINER, "setting value.\n\tpropertyName={0}\n\toldValue={1}\n\tnewValue={2}",
          new Object[]{propertyName, ObjectUtility.formatValue(oldValue),
              ObjectUtility.formatValue(newValue)});
    }
    if (null == indizes || 0 == indizes.length) {
      addToRollBack(this.threshold, propertyName, oldValue, newValue);
      this.pcs.fireIndexedPropertyChange(propertyName, getOid(), oldValue, newValue);
    } else {
      int[] idx = new int[indizes.length + 1];
      idx[0] = getOid();
      System.arraycopy(indizes, 0, idx, 1, indizes.length);

      addToRollBack(this.threshold, propertyName, oldValue, newValue, indizes);
      this.pcs.fireMultiIndexedPropertyChange(propertyName, idx, oldValue, newValue);
    }
  }

  public void initDefaults() {

  }

  protected static int[] createArrayInt(int size) {
    int[] array = new int[size];
    Arrays.fill(array, 0);
    return array;
  }

  /**
   * 初始化一个short数组.
   */
  public static short[] createArrayShort(int size) {
    short[] array = new short[size];
    Arrays.fill(array, (short) 0);
    return array;
  }

  /**
   * rollbackImpl.
   *
   * @param propertyName 要回滚的属性
   * @param indizes      要回滚的索引
   * @param value        会滚后的值
   * @param threshold    回滚阈值
   * @brief 回滚
   * @note 当回滚的阈值跟当时修改属性时的阈值一样，或者阈值为Threshold.ALL时才能回滚
   */
  @Override
  protected final void rollbackImpl(String propertyName, int[] indizes, Object value,
      Threshold threshold) {
    Threshold oldThreshold = null;

    boolean localChange = propertyEquals(threshold, propertyName, indizes);
    if (localChange) {
      oldThreshold = getThreshold();
      setThreshold(threshold);
    }
    if (!localChange && Threshold.ALL != threshold) {
      return;
    }
    rollbackImplImpl(propertyName, indizes, value);
    if (oldThreshold != null) {
      setThreshold(oldThreshold);
    }
  }

  protected void rollbackImplImpl(String propertyName, int[] indizes, Object value) {
    LOG.log(Level.CONFIG, "Ingnoring rollback of {0}",
        propertyName + Arrays.toString(indizes) + " to the value of " + value);
  }

  /**
   * .
   *
   * @brief 检查是否相等
   * @note 只要oid与fqn一样，就是相等
   */
  @Override
  public boolean equals(Object obj) {
    if (null != obj && getClass().equals(obj.getClass())) {
      AbstractData other = (AbstractData) obj;

      return this.oid == other.oid && this.fqn.equals(other.fqn);
    }
    return false;
  }

  /**
   * .
   *
   * @param propertyName 属性名字
   * @brief 检查属性是否在threshold为THRESHOLD_UI_LOCAL_CHANGES时改变的
   */
  public boolean isPropertyChangedByUi(String propertyName) {
    return propertyEquals(THRESHOLD_UI_LOCAL_CHANGES, propertyName)
        && isPropertyChanged(Threshold.ALL, propertyName);
  }

  /**
   * .
   *
   * @param propertyName 属性名字
   * @param indizes      数组的序号，可以是0个或者多个值，内容必须跟修改时的序号完全一致
   * @brief 检查类数组类型的属性是否在threshold为THRESHOLD_UI_LOCAL_CHANGES时改变的
   */
  public boolean isPropertyChangedByUi(String propertyName, int... indizes) {
    return propertyEquals(THRESHOLD_UI_LOCAL_CHANGES, propertyName, indizes)
        && isPropertyChanged(Threshold.ALL, propertyName, indizes);
  }

  @Override
  public int hashCode() {
    int hash = 7;
    hash = 19 * hash + getClass().hashCode();
    hash = 19 * hash + this.oid;
    hash = 19 * hash + this.fqn.hashCode();
    return hash;
  }

  /**
   * .
   *
   * @param od 如果为空，返回-1，如果类型一样，返回oid的相差值，其他情况比较类型的名字
   * @brief 比较
   */
  @Override
  public int compareTo(AbstractData od) {
    if (null == od) {
      return -1;
    }
    if (getClass().equals(od.getClass())) {
      return getOid() - od.getOid();
    }
    return getClass().getSimpleName().compareTo(od.getClass().getSimpleName());
  }

  @Override
  public String toString() {
    return getClass().getSimpleName() + "{oid=" + this.oid + ", fqn=" + this.fqn + "}@"
        + Integer.toHexString(System.identityHashCode(this));
  }
}

