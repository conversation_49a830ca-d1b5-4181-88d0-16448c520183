package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;

/**
 * .
 */
public class ScreenData {

  private VpConsoleData consoleData;
  private int oid = -1;

  public static final String PROPERTY_CONSOLENAME = "consoleName";
  public static final String PROPERTY_OID = "oid";


  /**
   * .
   */
  public String getConsoleName() {
    if (consoleData == null) {
      return "[No Assignment]";
    }
    return consoleData.getName();
  }

  public VpConsoleData getConsole() {
    return consoleData;
  }

  public void setConsole(VpConsoleData vpConsoleData) {
    consoleData = vpConsoleData;
  }


  public int getOid() {
    return oid;
  }

  public void setOid(int oid) {
    this.oid = oid;
  }

}
