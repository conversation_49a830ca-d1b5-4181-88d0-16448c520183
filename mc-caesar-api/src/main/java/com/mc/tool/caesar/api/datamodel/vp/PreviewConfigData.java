package com.mc.tool.caesar.api.datamodel.vp;

import com.mc.tool.caesar.api.datamodel.ConsoleData;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Locale;

/**
 * 预览配置数据.
 *
 * <AUTHOR>
 */
public class PreviewConfigData {

  public static final String PREVIEW_CONSOLE = "PreviewConfigData.PreviewConsole";
  public static final String PREVIEW_URL = "PreviewConfigData.PreviewUrl";

  public static final String THUMBNAIL_CONSOLE = "PreviewConfigData.ThumbnailConsole";
  public static final String THUMBNAIL_URL = "PreviewConfigData.ThumbnailUrl";

  /**
   * 用于输出预览视频源的con.
   */
  private ConsoleData previewConsoleData;

  /**
   * 播放预览的url.
   */
  private URI previewRtspUrl;

  private ConsoleData thumbnailConsoleData;

  private URI thumbnailRtspUrl;

  /**
   * .
   */
  public PreviewConfigData() {
    try {
      previewRtspUrl = new URI("");
      thumbnailRtspUrl = new URI("");
    } catch (URISyntaxException ex) {
      ex.printStackTrace();
    }
  }

  public ConsoleData getPreviewConsoleData() {
    return previewConsoleData;
  }

  /**
   * .
   */
  public void setPreviewConsoleData(ConsoleData consoleData) {
    if (this.previewConsoleData == consoleData) {
      return;
    }
    if (consoleData != null && this.thumbnailConsoleData == consoleData) {
      return;
    }
    //ConsoleData oldValue = this.previewConsoleData;
    this.previewConsoleData = consoleData;
  }

  public URI getPreviewRtspUrl() {
    return previewRtspUrl;
  }

  /**
   * .
   */
  public void setPreviewRtspUrl(URI rtspUrl) {
    if (rtspUrl == this.previewRtspUrl) {
      return;
    }
    if (rtspUrl != null && this.previewRtspUrl != null && rtspUrl.equals(this.previewRtspUrl)) {
      return;
    }
    if (rtspUrl != null && !rtspUrl.toString().isEmpty()
        && !rtspUrl.toString().toLowerCase(Locale.SIMPLIFIED_CHINESE).startsWith("rtsp://")) {
      return;
    }
    if (rtspUrl != null && !rtspUrl.toString().isEmpty()
        && rtspUrl.equals(thumbnailRtspUrl)) {
      return;
    }
    //URI oldValue = this.previewRtspUrl;
    this.previewRtspUrl = rtspUrl;
  }


  public ConsoleData getThumbnailConsoleData() {
    return thumbnailConsoleData;
  }

  /**
   * .
   */
  public void setThumbnailConsoleData(ConsoleData thumbnailConsoleData) {
    if (this.thumbnailConsoleData == thumbnailConsoleData) {
      return;
    }
    if (thumbnailConsoleData != null && this.previewConsoleData == thumbnailConsoleData) {
      return;
    }
    //ConsoleData oldValue = this.thumbnailConsoleData;
    this.thumbnailConsoleData = thumbnailConsoleData;
  }

  public URI getThumbnailRtspUrl() {
    return thumbnailRtspUrl;
  }

  /**
   * .
   */
  public void setThumbnailRtspUrl(URI thumbnailRtspUrl) {
    if (thumbnailRtspUrl == this.thumbnailRtspUrl) {
      return;
    }
    if (thumbnailRtspUrl != null && !thumbnailRtspUrl.toString().isEmpty()
        && !thumbnailRtspUrl.toString().toLowerCase(Locale.SIMPLIFIED_CHINESE)
        .startsWith("rtsp://")) {
      return;
    }
    if (thumbnailRtspUrl != null && !thumbnailRtspUrl.toString().isEmpty()
        && thumbnailRtspUrl.equals(previewRtspUrl)) {
      return;
    }
    //URI oldValue = this.thumbnailRtspUrl;
    this.thumbnailRtspUrl = thumbnailRtspUrl;
  }


}
