package com.mc.tool.caesar.api.version.v4x;

import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupDataType;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgReader;
import com.mc.tool.caesar.api.utils.CfgWriter;
import com.mc.tool.caesar.api.version.ApiDataConverter;

/**
 * .
 */
public class GroupDataConverter4x implements ApiDataConverter<TxRxGroupData> {

  private final int dataSize;

  public GroupDataConverter4x(int dataSize) {
    this.dataSize = dataSize;
  }

  @Override
  public void readData(TxRxGroupData data, CfgReader cfgReader) throws ConfigException {
    final int startAvailable = cfgReader.available();
    String name = cfgReader.readString(CaesarConstants.NAME_LEN);
    data.setName(name);
    int status = cfgReader.readByteValue();
    data.setStatus(status & 1);
    int type = cfgReader.readByteValue();
    data.setType(TxRxGroupDataType.valueOf(type));
    int endAvailable = cfgReader.available();
    int readedSize = startAvailable - endAvailable;
    int reserveSize = dataSize - readedSize;
    cfgReader.readByteArray(reserveSize);
  }

  @Override
  public void writeData(TxRxGroupData data, CfgWriter cfgWriter) throws ConfigException {
    final long startSize = cfgWriter.getSize();
    cfgWriter.writeString(data.getName(), CaesarConstants.NAME_LEN);
    cfgWriter.writeByte((byte) (data.getStatus()));
    cfgWriter.writeByte((byte) (data.getType().getValue()));
    long endSize = cfgWriter.getSize();
    int writedSize = (int) (endSize - startSize);
    int reservedSize = dataSize - writedSize;
    cfgWriter.writeByteArray(new byte[reservedSize]);
  }
}
