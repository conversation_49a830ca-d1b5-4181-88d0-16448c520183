package com.mc.tool.caesar.api.datamodel;

import com.mc.tool.caesar.api.io.NormalStruct;

/**
 * .
 */
public class ResolutionData extends NormalStruct {

  private Unsigned16 width = new Unsigned16();
  private Unsigned16 height = new Unsigned16();
  private Unsigned16 rate = new Unsigned16();

  public long getWidth() {
    return width.get();
  }

  public void setWidth(int width) {
    this.width.set(width);
  }

  public long getHeight() {
    return height.get();
  }

  public void setHeight(int height) {
    this.height.set(height);
  }

  public int getRate() {
    return rate.get();
  }

  public void setRate(int rate) {
    this.rate.set(rate);
  }

  public boolean isValid() {
    return getWidth() != 0 && getHeight() != 0;
  }
}
