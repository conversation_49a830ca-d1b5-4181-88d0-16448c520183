ConfigData.checkArrays=阵列大小无效{0},开始重新启动
ConfigData.warning.tooNew.message=<html>\r\n<b>配置文件冲突！</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>当前文件版本：</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='200'>当前工具支持的版本:</td>\r\n        <td><b>%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>配置文件是新版的，你需要下载一个更新的版本:\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr><td width='120'>文件版本 V03.03：</td><td>工具版本 *******或者更旧</td></tr>\r\n    <tr><td width='120'>文件版本 V03.04：</td><td>工具版本 0.0.0.6 </td></tr>\r\n</table>
ConfigData.warning.tooNew.title=配置文件冲突
ConfigData.warning.unsupported.message=<html>\r\n<b>当前工具不支持此固件版本.</b>\r\n<br><br>\r\n<table cellspacing='0' cellpadding='0'>\r\n    <tr>\r\n        <td width='200'>当前固件版本：</td>\r\n        <td><b>&nbsp;&nbsp;&nbsp;%s</b></td>\r\n    </tr>\r\n    <tr>\r\n        <td width='240'>当前工具支持的版本：</td>\r\n        <td><b>&gt;&nbsp;%s</b></td>\r\n    </tr>\r\n</table>\r\n<br>请使用工具版本*******。
ConfigData.warning.unsupported.title=不支持的固件版本！
ConsoleData.CPU=CPU 连接
ConsoleData.CPU.Tooltip=CPU 连接
ConsoleData.ConsoleVirtual=操控台分配
ConsoleData.ConsoleVirtual.Tooltip=操控台分配
ConsoleData.ID=ID
ConsoleData.ID.Tooltip=ID
ConsoleData.Name=名称
ConsoleData.Name.Tooltip=名称
ConsoleData.RDCPU=红色. CPU 连接
ConsoleData.RDCPU.Tooltip=冗余 CPU 连接
ConsoleData.ScanTime=轮询时间[sec]
ConsoleData.ScanTime.Tooltip=轮询时间
ConsoleData.Status.AllowLogin=允许用户 ACL
ConsoleData.Status.AllowLogin.Tooltip=允许用户 ACL
ConsoleData.Status.AllowScan=允许CPU接入端轮询
ConsoleData.Status.AllowScan.Tooltip=允许CPU接入端轮询
ConsoleData.Status.ForceLogin=强制登录
ConsoleData.Status.ForceLogin.Tooltip=强制登录
ConsoleData.Status.ForceMacro=展示宏列表
ConsoleData.Status.ForceMacro.Tooltip=展示宏列表
ConsoleData.Status.ForceScan=强制CPU接入端轮询
ConsoleData.Status.ForceScan.Tooltip=强制CPU接入端轮询
ConsoleData.Status.LOSFrame=LOS架构
ConsoleData.Status.LOSFrame.Tooltip=丢失信号帧
ConsoleData.Status.OsdDisabled=OSD已禁用
ConsoleData.Status.OsdDisabled.Tooltip=OSD已禁用
ConsoleData.Status.PortMode=端口模式
ConsoleData.Status.PortMode.Tooltip=操作模式允许手动切换到每个主机端口1-99.
ConsoleData.Status.Redundant=关闭冗余
ConsoleData.Status.Redundant.Tooltip=禁用冗余CON扩展端
ConsoleData.Status.Virtual=虚拟设备
ConsoleData.Status.Virtual.Tooltip=虚拟设备
ControlGroupData.Arrangement=排列
ControlGroupData.Arrangement.Tooltip=一行四个显示器或两行每行两个显示器
ControlGroupData.Control1=控制
ControlGroupData.Control1.Tooltip=控制
ControlGroupData.Control2=控制
ControlGroupData.Control2.Tooltip=控制
ControlGroupData.Control3=控制
ControlGroupData.Control3.Tooltip=控制
ControlGroupData.Control4=控制
ControlGroupData.Control4.Tooltip=控制
ControlGroupData.ControlCon=控制 CON
ControlGroupData.ControlCon.Tooltip=当前控制 CON
ControlGroupData.Enabled1=激活
ControlGroupData.Enabled1.Tooltip=激活
ControlGroupData.Enabled2=激活
ControlGroupData.Enabled2.Tooltip=激活
ControlGroupData.Enabled3=激活
ControlGroupData.Enabled3.Tooltip=激活
ControlGroupData.Enabled4=激活
ControlGroupData.Enabled4.Tooltip=激活
ControlGroupData.Frame1=提示框[秒]
ControlGroupData.Frame1.Tooltip=提示框
ControlGroupData.Frame2=提示框[秒]
ControlGroupData.Frame2.Tooltip=提示框
ControlGroupData.Frame3=提示框[秒]
ControlGroupData.Frame3.Tooltip=提示框
ControlGroupData.Frame4=提示框[秒]
ControlGroupData.Frame4.Tooltip=提示框
ControlGroupData.Manual=手动
ControlGroupData.Manual.Tooltip=切换到使用热键的手动切换.<br>禁用鼠标自动漫游切换多屏拓展器
ControlGroupData.Name1=名称
ControlGroupData.Name1.Tooltip=名称
ControlGroupData.Name2=名称
ControlGroupData.Name2.Tooltip=名称
ControlGroupData.Name3=名称
ControlGroupData.Name3.Tooltip=名称
ControlGroupData.Name4=名称
ControlGroupData.Name4.Tooltip=名称
ControlGroupData.Owner1=所有者
ControlGroupData.Owner1.Tooltip=所有者
ControlGroupData.Owner2=所有者
ControlGroupData.Owner2.Tooltip=所有者
ControlGroupData.Owner3=所有者
ControlGroupData.Owner3.Tooltip=所有者
ControlGroupData.Owner4=所有者
ControlGroupData.Owner4.Tooltip=所有者
CpuData.Console=CON连接
CpuData.Console.Tooltip=CON连接
CpuData.ID=ID
CpuData.ID.Tooltip=ID
CpuData.Name=名称
CpuData.Name.Tooltip=名称
CpuData.RealCpu=CPU 分配
CpuData.RealCpu.Tooltip=CPU 分配
CpuData.Status.AllowPrivate=允许私有模式
CpuData.Status.AllowPrivate.Tooltip=允许私有模式
CpuData.Status.FixFrame=Fix架构
CpuData.Status.FixFrame.Tooltip=Fix架构
CpuData.Status.ForcePrivate=强制私有模式
CpuData.Status.ForcePrivate.Tooltip=强制私有模式
CpuData.Status.Virtual=虚拟设备
CpuData.Status.Virtual.Tooltip=虚拟设备
DisplayData.C_Speed=双击时间[毫秒]
DisplayData.C_Speed.Tooltip=调整双击的时间间隙
DisplayData.H_Speed=鼠标水平速度[1/x]
DisplayData.H_Speed.Tooltip=调整鼠标水平速度
DisplayData.Keyboard=键盘布局
DisplayData.Keyboard.Tooltip=根据键盘设置OSD键盘布局
DisplayData.V_Speed=鼠标垂直速度[1/x]
DisplayData.V_Speed.Tooltip=调整鼠标垂直速度
DisplayData.VideoMode=视频模式
DisplayData.VideoMode.Tooltip=视频模式
EdidData.Audio=Audio 音频
EdidData.Audio.Tooltip=Audio 音频
EdidData.EdidVersion=EDIE版本
EdidData.EdidVersion.Tooltip=EDIE版本
EdidData.HorizontalActivePixel=H.有效像素
EdidData.HorizontalActivePixel.Tooltip=水平有效像素
EdidData.HorizontalFrequency=H.频率[kHz]
EdidData.HorizontalFrequency.Tooltip=H.频率[kHz]
EdidData.ManufacturerID=制作商名称
EdidData.ManufacturerID.Tooltip=制作商名称
EdidData.ManufacturerProductCode=产品编码
EdidData.ManufacturerProductCode.Tooltip=产品编码
EdidData.ManufacturerWeek=制作商周期
EdidData.ManufacturerWeek.Tooltip=制作商周期
EdidData.ManufacturerYear=制造商年份
EdidData.ManufacturerYear.Tooltip=制造商年份
EdidData.PixelClock=像素时钟[Mhz]
EdidData.PixelClock.Tooltip=像素时钟
EdidData.SerialNumber=序列号
EdidData.SerialNumber.Tooltip=序列号
EdidData.State=EDID校验和
EdidData.State.Invalid=无效
EdidData.State.Reset=重置
EdidData.State.Tooltip=EDID校验和
EdidData.State.Valid=无效
EdidData.VerticalBlankingPixel=V. 活动线
EdidData.VerticalBlankingPixel.Tooltip=垂直活动线
EdidData.VerticalFrequency=V.频率[Hz]
EdidData.VerticalFrequency.Tooltip=V.频率
ExtenderData.Con=CON分配
ExtenderData.Con.Tooltip=CON分配
ExtenderData.Cpu=CPU 分配
ExtenderData.Cpu.Tooltip=CPU分配
ExtenderData.CpuCon=CPU/CON分配
ExtenderData.CpuCon.Tooltip=CPU/CON分配
ExtenderData.ID=ID
ExtenderData.ID.Tooltip=ID
ExtenderData.Name=名称
ExtenderData.Name.Tooltip=名称
ExtenderData.Port=端口
ExtenderData.Port.Tooltip=端口
ExtenderData.RDPort=冗余端口
ExtenderData.RDPort.Tooltip=冗余端口
ExtenderData.Status.FixPort=固定
ExtenderData.Status.FixPort.Tooltip=固定
ExtenderData.Status.UsbDiskEnable=U盘使能
ExtenderData.Status.UsbDiskEnable.Tooltip=U盘使能
MatrixData.Active=激活
MatrixData.Device=设备
MatrixData.Ports=端口
MultiScreenData.CtrlConId=控制 CON
MultiScreenData.CtrlConId.Tooltip=控制 CON
MultiScreenData.Frame=提示框
MultiScreenData.Frame.Tooltip=提示框时间
MultiScreenData.HorizontalNumber=列
MultiScreenData.HorizontalNumber.Tooltip=列
MultiScreenData.Name=名称
MultiScreenData.Name.Tooltip=多屏控制组的名称
MultiScreenData.Type=模式
MultiScreenData.Type.Tooltip=跨屏类型 0：鼠标跨屏  1：手动跨屏
MultiScreenData.VerticalNumber=行
MultiScreenData.VerticalNumber.Tooltip=行
OsdData.Col=水平位置 [10 px]
OsdData.Col.Tooltip=OSD水平位置. 范围: -128 ... +127
OsdData.OsdPosition=OSD 位置预设
OsdData.OsdPosition.Tooltip=OSD位置预设
OsdData.Preview=预设
OsdData.Preview.Tooltip=预设
OsdData.Row=垂直位置 [10 px]
OsdData.Row.Tooltip=OSD垂直位置. 范围: -128 ... +127
OsdData.Status.Active=启用连接信息展示
OsdData.Status.Active.Tooltip=启用连接信息显示当前连接状态
OsdData.Status.Select=启用mini OSD菜单
OsdData.Status.Select.Tooltip=<html>当执行密钥序列打开OSD时,<br>用于切换的CPU接入端将结果显示在CON管控端OSD的选择列表。
OsdData.Status.Update=启用连接信息更新
OsdData.Status.Update.Tooltip=连接升级当淡入CON管控端的OSD
OsdData.Timeout=展示时间[sec]
OsdData.Timeout.Tooltip=OSD渐变的持续时间（0 =无限制）
SystemConfigData.AccessData.ForceBits.ConAccess=允许新的CON管控端
SystemConfigData.AccessData.ForceBits.ConAccess.Tooltip=开启新建的CON对所有CPU的权限
SystemConfigData.AccessData.ForceBits.ConLock=允许操控台ACL
SystemConfigData.AccessData.ForceBits.ConLock.Tooltip=开启CON对他所有有访问权的CPU列表的权限
SystemConfigData.AccessData.ForceBits.CpuDisconnct=自动断开
SystemConfigData.AccessData.ForceBits.CpuDisconnct.Tooltip=从当前的CPU接入端打开OSD从而断开操控台
SystemConfigData.AccessData.ForceBits.Login=强制用户登录
SystemConfigData.AccessData.ForceBits.Login.Tooltip=要求用户登录进入OSD
SystemConfigData.AccessData.ForceBits.UserAccess=允许新用户
SystemConfigData.AccessData.ForceBits.UserAccess.Tooltip=开启新建的用户对所有CPU的权限
SystemConfigData.AccessData.ForceBits.UserConAnd=AND 用户/CON 访问控制列表
SystemConfigData.AccessData.ForceBits.UserConAnd.Tooltip=AND 用户和 CON 访问控制列表 (减少访问)
SystemConfigData.AccessData.ForceBits.UserConOr=OR用户/CON 访问控制列表
SystemConfigData.AccessData.ForceBits.UserConOr.Tooltip=AND 用户 和 CON 访问控制列表 (增加访问)
SystemConfigData.AccessData.ForceBits.UserLock=允许用户ACL
SystemConfigData.AccessData.ForceBits.UserLock.Tooltip=开启用户对他所有有控制权的CPU列表的权限
SystemConfigData.AccessData.TimeoutDisplay=OSD超时[秒]
SystemConfigData.AccessData.TimeoutDisplay.Tooltip=指定不活动时自动退出OSD(0 = 未激活的)
SystemConfigData.AccessData.TimeoutLogout=自动退出[分钟]
SystemConfigData.AccessData.TimeoutLogout.Tooltip=指定不活动时用户自动退出(0= 未激活的 -1=不受限制的)
SystemConfigData.AutoIdData.AutoId.RealConId=真实的CON管控端ID
SystemConfigData.AutoIdData.AutoId.RealConId.Tooltip=起始ID为真实的CON管控端自动定义
SystemConfigData.AutoIdData.AutoId.RealCpuId=真实的CPU接入端ID
SystemConfigData.AutoIdData.AutoId.RealCpuId.Tooltip=起始ID为真实的CPU接入端自动定义
SystemConfigData.AutoIdData.AutoId.VirtualConId=虚拟的CON管控端ID
SystemConfigData.AutoIdData.AutoId.VirtualConId.Tooltip=起始ID为虚拟的CON管控端自动定义
SystemConfigData.AutoIdData.AutoId.VirtualCpuId=虚拟的CPU接入端ID
SystemConfigData.AutoIdData.AutoId.VirtualCpuId.Tooltip=起始ID为虚拟的CPU接入端自动定义
SystemConfigData.AutoIdData.ForceBits.AutoConfig=循序自动配置
SystemConfigData.AutoIdData.ForceBits.AutoConfig.Tooltip=定义新的EXT单元至新的CPU接入端或CON管控端
SystemConfigData.LdapData.Address=LDAP服务器
SystemConfigData.LdapData.Address.Tooltip=LDAP 服务器
SystemConfigData.LdapData.BaseDN=基本DN
SystemConfigData.LdapData.BaseDN.Tooltip=例子: ou=用户,dc=域名,dc=网络
SystemConfigData.LdapData.Port=端口
SystemConfigData.LdapData.Port.Tooltip=端口
SystemConfigData.MatrixGridData.ForceBits.Grid=允许主机级联
SystemConfigData.MatrixGridData.ForceBits.Grid.Tooltip=启用/禁用矩阵级联需要重新启动
SystemConfigData.NetworkData.Address=IP地址
SystemConfigData.NetworkData.Address.Tooltip=IP地址
SystemConfigData.NetworkData.Gateway=网关
SystemConfigData.NetworkData.Gateway.Tooltip=网关
SystemConfigData.NetworkData.MacAddress=MAC地址
SystemConfigData.NetworkData.MacAddress.Tooltip=Mac地址
SystemConfigData.NetworkData.Netmask=子网掩码
SystemConfigData.NetworkData.Netmask.Tooltip=子网掩码
SystemConfigData.NetworkData.NetworkBits.Api=API服务 
SystemConfigData.NetworkData.NetworkBits.Api.Tooltip=开启API服务(端口号:5555)
SystemConfigData.NetworkData.NetworkBits.Dhcp=DHCP
SystemConfigData.NetworkData.NetworkBits.Dhcp.Tooltip=通过DHCP服务器自动配置网络参数
SystemConfigData.NetworkData.NetworkBits.Ftp=FTP服务
SystemConfigData.NetworkData.NetworkBits.Ftp.Tooltip=启用FTP服务作为配置文件转移
SystemConfigData.NetworkData.NetworkBits.Ldap=LDAP
SystemConfigData.NetworkData.NetworkBits.Ldap.Tooltip=允许 LDAP
SystemConfigData.NetworkData.NetworkBits.Snmp=SNMP代理
SystemConfigData.NetworkData.NetworkBits.Snmp.Tooltip=启用SNMP代理GET请求和陷阱。
SystemConfigData.NetworkData.NetworkBits.Sntp=SNTP
SystemConfigData.NetworkData.NetworkBits.Sntp.Tooltip=允许网络时间服务同步
SystemConfigData.NetworkData.NetworkBits.Syslog=系统日志
SystemConfigData.NetworkData.NetworkBits.Syslog.Tooltip=允许状态报告系统日志消息
SystemConfigData.NetworkData.SnmpAgentPort=端口
SystemConfigData.NetworkData.SnmpAgentPort.Tooltip=端口
SystemConfigData.SnmpData.Address=SNMP服务
SystemConfigData.SnmpData.Address.Tooltip=SNMP服务
SystemConfigData.SnmpData.Port=端口
SystemConfigData.SnmpData.Port.Tooltip=端口
SystemConfigData.SnmpData.SnmpBits.FanTray1=风扇槽1
SystemConfigData.SnmpData.SnmpBits.FanTray1.Tooltip=关于风扇槽1状态通知
SystemConfigData.SnmpData.SnmpBits.FanTray2=风扇槽2
SystemConfigData.SnmpData.SnmpBits.FanTray2.Tooltip=关于风扇槽2状态的通知
SystemConfigData.SnmpData.SnmpBits.InsertBoard=插入I/O板卡
SystemConfigData.SnmpData.SnmpBits.InsertBoard.Tooltip=关于一个新的I/O板卡插入到插槽的通知
SystemConfigData.SnmpData.SnmpBits.InsertExtender=插入扩展器
SystemConfigData.SnmpData.SnmpBits.InsertExtender.Tooltip=关于新连接扩展器到主机的通知
SystemConfigData.SnmpData.SnmpBits.InvalidBoard=无效I/O板卡
SystemConfigData.SnmpData.SnmpBits.InvalidBoard.Tooltip=关于非正常工作的I/O板卡的通知
SystemConfigData.SnmpData.SnmpBits.PowerSupply1=电源供应器1
SystemConfigData.SnmpData.SnmpBits.PowerSupply1.Tooltip=关于电源供应器1状态的通知
SystemConfigData.SnmpData.SnmpBits.PowerSupply2=电源供应器2
SystemConfigData.SnmpData.SnmpBits.PowerSupply2.Tooltip=关于电源供应器2状态的通知
SystemConfigData.SnmpData.SnmpBits.PowerSupply3=电源供应器3
SystemConfigData.SnmpData.SnmpBits.PowerSupply3.Tooltip=关于电源供应器3状态的通知
SystemConfigData.SnmpData.SnmpBits.RemoveBoard=移除I/O板卡
SystemConfigData.SnmpData.SnmpBits.RemoveBoard.Tooltip=关于从卡槽上移除I/O板卡的通知
SystemConfigData.SnmpData.SnmpBits.RemoveExtener=移除扩展器
SystemConfigData.SnmpData.SnmpBits.RemoveExtener.Tooltip=关于从主机上移除扩展器的通知
SystemConfigData.SnmpData.SnmpBits.Status=状态
SystemConfigData.SnmpData.SnmpBits.Status.Tooltip=关于主机状态的通知
SystemConfigData.SnmpData.SnmpBits.SwitchCommand=切换命令
SystemConfigData.SnmpData.SnmpBits.SwitchCommand.Tooltip=关于主机进行切换操作的通知
SystemConfigData.SnmpData.SnmpBits.Temperature=温度
SystemConfigData.SnmpData.SnmpBits.Temperature.Tooltip=关于主机内部温度的通知
SystemConfigData.SnmpData.SnmpBits.Trap=允许过滤
SystemConfigData.SnmpData.SnmpBits.Trap.Tooltip=允许过滤
SystemConfigData.SntpData.Address=SNTP 服务器
SystemConfigData.SntpData.Address.Tooltip=SNTP服务器
SystemConfigData.SntpData.RealTimeClock=日期和时间
SystemConfigData.SntpData.RealTimeClock.Tooltip=日期和实时时钟的时间
SystemConfigData.SntpData.TimeZone=时区
SystemConfigData.SntpData.TimeZone.Tooltip=时区
SystemConfigData.SwitchData.ForceBits.AutoConnect=CPU接入端自动连接
SystemConfigData.SwitchData.ForceBits.AutoConnect.Tooltip=允许通过鼠标或键盘连接到下一个有效的CPU接入端
SystemConfigData.SwitchData.ForceBits.ConDisconnect=强制断开
SystemConfigData.SwitchData.ForceBits.ConDisconnect.Tooltip=强制执行操作模式访问CPU接入端，其他控制台断开连接
SystemConfigData.SwitchData.ForceBits.CpuConnect=强制连接
SystemConfigData.SwitchData.ForceBits.CpuConnect.Tooltip=强制执行操作模式访问CPU接入端，其他控制台保留视频连接
SystemConfigData.SwitchData.ForceBits.CpuWatch=允许视频共享
SystemConfigData.SwitchData.ForceBits.CpuWatch.Tooltip=允许共享视频登录至CPU接入端
SystemConfigData.SwitchData.ForceBits.FKeySingle=单步骤宏控制
SystemConfigData.SwitchData.ForceBits.FKeySingle.Tooltip=单步骤执行宏控制
SystemConfigData.SwitchData.ForceBits.KeyboardConnect=键盘鼠标连接
SystemConfigData.SwitchData.ForceBits.KeyboardConnect.Tooltip=允许通过键盘鼠标激活CPU接入端控制要求
SystemConfigData.SwitchData.ForceBits.MouseConnect=鼠标连接
SystemConfigData.SwitchData.ForceBits.MouseConnect.Tooltip=允许通过鼠标激活CPU接入端控制要求
SystemConfigData.SwitchData.TimeoutDisconnect=CPU接入端超时[分钟]
SystemConfigData.SwitchData.TimeoutDisconnect.Tooltip=指定不活动期间在当前CPU后,CPU将自动断开连接(0 =未激活的)
SystemConfigData.SwitchData.TimeoutShare=释放时间[秒]
SystemConfigData.SwitchData.TimeoutShare.Tooltip=指定不活动期间从另一个控制台接受CPU控制的要求
SystemConfigData.SyslogData.Address=信息日志服务
SystemConfigData.SyslogData.Address.Tooltip=信息日志服务
SystemConfigData.SyslogData.Facility=设备
SystemConfigData.SyslogData.Facility.Tooltip=系统日志设备
SystemConfigData.SyslogData.Port=端口
SystemConfigData.SyslogData.Port.Tooltip=系统日志端口
SystemConfigData.SyslogData.SysLevel=日志级别
SystemConfigData.SyslogData.SysLevel.Debug=调试
SystemConfigData.SyslogData.SysLevel.Debug.Tooltip=调试
SystemConfigData.SyslogData.SysLevel.Error=错误
SystemConfigData.SyslogData.SysLevel.Error.Tooltip=错误
SystemConfigData.SyslogData.SysLevel.Info=信息
SystemConfigData.SyslogData.SysLevel.Info.Tooltip=信息
SystemConfigData.SyslogData.SysLevel.Notice=通知
SystemConfigData.SyslogData.SysLevel.Notice.Tooltip=通知
SystemConfigData.SyslogData.SysLevel.Tooltip=日志级别
SystemConfigData.SyslogData.SysLevel.Warning=警告
SystemConfigData.SyslogData.SysLevel.Warning.Tooltip=警告
SystemConfigData.SyslogData.Syslog.Enabled=允许系统日志
SystemConfigData.SyslogData.Syslog.Enabled.Tooltip=启用状态报告系统日志消息
SystemConfigData.SystemData.Device=设备
SystemConfigData.SystemData.Device.Tooltip=网络环境的主机名称（推荐字符：a-z, A-Z, 0-9, -）
SystemConfigData.SystemData.ForceBits.AutoSave=自动保存
SystemConfigData.SystemData.ForceBits.AutoSave.Tooltip=自动保存主机状态
SystemConfigData.SystemData.ForceBits.ComEcho=允许COM数据回传
SystemConfigData.SystemData.ForceBits.ComEcho.Tooltip=通过通讯端口回传所有切换命令
SystemConfigData.SystemData.ForceBits.Default=加载默认
SystemConfigData.SystemData.ForceBits.Default.Tooltip=当执行一个冷启动或重启主机,总是激活默认配置存储
SystemConfigData.SystemData.ForceBits.EchoOnly=只回传
SystemConfigData.SystemData.ForceBits.EchoOnly.Tooltip=只同步主机的回传信息
SystemConfigData.SystemData.ForceBits.Invalid=无效的I/O板卡
SystemConfigData.SystemData.ForceBits.Invalid.Tooltip=操作过程中必须关闭，仅允许在主机升级
SystemConfigData.SystemData.ForceBits.LanEcho=允许LAN口数据回传
SystemConfigData.SystemData.ForceBits.LanEcho.Tooltip=通过网络端口回传所有切换命令
SystemConfigData.SystemData.ForceBits.OldEcho=允许旧格式回传
SystemConfigData.SystemData.ForceBits.OldEcho.Tooltip=回传旧格式的内部切换命令
SystemConfigData.SystemData.ForceBits.OnlineConfig=在线配置
SystemConfigData.SystemData.ForceBits.OnlineConfig.Tooltip=在管理软件激活在线配置
SystemConfigData.SystemData.ForceBits.Redundancy=允许冗余
SystemConfigData.SystemData.ForceBits.Redundancy.Tooltip=启用冗余扩展器自动切换
SystemConfigData.SystemData.ForceBits.RemoveSlave=移除I/O板卡 
SystemConfigData.SystemData.ForceBits.RemoveSlave.Tooltip=当丢失第二张CPU控制板卡（576），移除 I/O 板卡
SystemConfigData.SystemData.ForceBits.Slave=子主机
SystemConfigData.SystemData.ForceBits.Slave.Tooltip=允许在级联环境下热键控制
SystemConfigData.SystemData.ForceBits.Synchronize=同步
SystemConfigData.SystemData.ForceBits.Synchronize.Tooltip=主机与主用主机同步
SystemConfigData.SystemData.ForceBits.UartEnable=使能第三方串口
SystemConfigData.SystemData.ForceBits.UartEnable.Tooltip=使能第三方串口
SystemConfigData.SystemData.Info=信息
SystemConfigData.SystemData.Info.Tooltip=当前主机配置的描述
SystemConfigData.SystemData.MasterIP=主用主机IP地址
SystemConfigData.SystemData.MasterIP.Tooltip=设置主用主机的网络地址
SystemConfigData.SystemData.Name=名称
SystemConfigData.SystemData.Name.Tooltip=当前主机配置的名称
SystemData.InternalLogLevel=日志级别
SystemData.InternalLogLevel.Debug=调试
SystemData.InternalLogLevel.Debug.Tooltip=调试
SystemData.InternalLogLevel.Error=错误
SystemData.InternalLogLevel.Error.Tooltip=错误
SystemData.InternalLogLevel.Info=信息
SystemData.InternalLogLevel.Info.Tooltip=信息
SystemData.InternalLogLevel.Notice=通知
SystemData.InternalLogLevel.Notice.Tooltip=通知
SystemData.InternalLogLevel.Tooltip=日志级别
SystemData.InternalLogLevel.Warning=警告
SystemData.InternalLogLevel.Warning.Tooltip=警告
SystemData.Multicast=组播
SystemData.Multicast.Tooltip=级联组播或者广播 (***************)
UserData.FullName=全名
UserData.FullName.Tooltip=名字姓氏
UserData.ID=ID
UserData.ID.Tooltip=ID
UserData.Name=名字
UserData.Name.Tooltip=名字
UserData.Password=密码
UserData.Password.Tooltip=密码
UserData.Rights.ADMIN=管理员
UserData.Rights.ADMIN.Tooltip=管理员
UserData.Rights.FTP=FTP
UserData.Rights.FTP.Tooltip=FTP
UserData.Rights.LDAP=LDAP 用户
UserData.Rights.LDAP.Tooltip=LDAP用户
UserData.Rights.POWER=权限用户
UserData.Rights.POWER.Tooltip=权限用户
UserData.Rights.SUPER=超级用户
UserData.Rights.SUPER.Tooltip=超级用户
UserGroupData.ID=ID
UserGroupData.ID.Tooltip=ID
UserGroupData.Name=名字
UserGroupData.Name.Tooltip=名字

