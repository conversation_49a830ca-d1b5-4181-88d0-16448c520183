package com.mc.graph;

import com.mc.graph.event.ExtraActionEvent;
import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.ExtraActionInfo;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.util.NodeUtil;
import com.mc.graph.util.SelectableNode;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.ObservableList;
import javafx.event.EventHandler;
import javafx.geometry.Bounds;
import javafx.scene.Cursor;
import javafx.scene.Node;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.MenuItem;
import javafx.scene.effect.ColorAdjust;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Border;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.BorderStrokeStyle;
import javafx.scene.layout.BorderWidths;
import javafx.scene.layout.CornerRadii;
import javafx.scene.layout.Region;
import javafx.scene.shape.StrokeLineCap;
import javafx.scene.shape.StrokeLineJoin;
import javafx.scene.shape.StrokeType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DefaultCellBehavior implements CellBehavior {
  private CellSkin cellSkin;
  private GraphController controller;

  private double mouseX;
  private double mouseY;
  private double lastMouseX;
  private double lastMouseY;
  private double nodeX = 0;
  private double nodeY = 0;

  private boolean resizeTop;
  private boolean resizeLeft;
  private boolean resizeBottom;
  private boolean resizeRight;

  private ChangeListener<? super Boolean> borderChangeListener;
  private BooleanProperty draggingProperty = new SimpleBooleanProperty();
  private EventHandler<MouseEvent> mousePressedHandler;
  private EventHandler<MouseEvent> mouseDragedHandler;
  private EventHandler<MouseEvent> mouseClickedHandler;
  private EventHandler<MouseEvent> mouseRightClickedHandler;
  private EventHandler<MouseEvent> mouseReleasedHandler;
  private EventHandler<MouseEvent> mouseMovedHandler;

  public DefaultCellBehavior(GraphController controller, CellSkin cellSkin) {
    this.cellSkin = cellSkin;
    this.controller = controller;
  }

  private void init() {
    final Border prevBorder = cellSkin.getRegion().getBorder();
    borderChangeListener = (ov, oldValue, newValue) -> {
      Region control = cellSkin.getRegion();
      if (cellSkin.isSelected()) {
        BorderStroke borderStroke = cellSkin.getSelectedBorderStroke();
        if (borderStroke == null) {
          BorderStrokeStyle style = new BorderStrokeStyle(StrokeType.OUTSIDE, StrokeLineJoin.MITER,
              StrokeLineCap.BUTT, 10, 0, null);
          borderStroke = new BorderStroke(cellSkin.getSelectionBorderColor(), style,
              new CornerRadii(3), new BorderWidths(2));
        }
        control.setBorder(new Border(borderStroke));
        if (cellSkin.isSelectionEffectEnabled()) {
          ColorAdjust effect = new ColorAdjust(-0.25, 0.2, 0.8, 0);
          control.setEffect(effect);
        }
      } else if (cellSkin.getCell().hoverProperty().get()) {
        BorderStrokeStyle style = new BorderStrokeStyle(StrokeType.OUTSIDE, StrokeLineJoin.MITER,
            StrokeLineCap.BUTT, 10, 0, null);
        control.setBorder(new Border(new BorderStroke(cellSkin.getSelectionBorderColor(), style,
            new CornerRadii(4), new BorderWidths(1))));
        if (cellSkin.isSelectionEffectEnabled()) {
          ColorAdjust effect = new ColorAdjust(-0.25, 0.2, 0.8, 0);
          control.setEffect(effect);
        }
      } else {
        control.setBorder(prevBorder);
        control.setEffect(null);
      }
    };

    cellSkin.selectedProperty().addListener(borderChangeListener);
    cellSkin.getCell().hoverProperty().addListener(borderChangeListener);


    cellSkin.getRegion().setFocusTraversable(true);

    initMouseEventHandlers();
  }

  private void initMouseEventHandlers() {
    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_PRESSED, mousePressedHandler = event -> {
      final Node n = cellSkin.getRegion();

      mouseX = event.getSceneX();
      mouseY = event.getSceneY();
      lastMouseX = mouseX;
      lastMouseY = mouseY;

      nodeX = n.getLayoutX();
      nodeY = n.getLayoutY();
      if (!cellSkin.isSelected()) {
        if (!event.isControlDown()) {
          controller.getSelectionModel().deselectAll();
        }
        controller.getSelectionModel().select(cellSkin, true);
      } else if (cellSkin.isSelected() && event.isControlDown()) {
        controller.getSelectionModel().select(cellSkin, false);
      }
      event.consume();
    });

    // Event Listener for MouseDragged
    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_DRAGGED, mouseDragedHandler = (event) -> {
      onMouseDragged(event);
    });

    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_RELEASED,
        mouseReleasedHandler = (event) -> {
          if (cellSkin.isSelected() && !draggingProperty.get()
              && event.getButton() == MouseButton.PRIMARY) {
            if (!event.isControlDown()) {
              controller.getSelectionModel().deselectAll();
            }
            controller.getSelectionModel().select(cellSkin, true);
          }
          event.consume();
        });
    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_CLICKED,
        mouseClickedHandler = (event) -> {
          setDragging(false);
          cellSkin.getRegion().requestFocus();
          event.consume();
        });

    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_CLICKED,
        mouseRightClickedHandler = (event) -> {
          if (event.getButton() == MouseButton.SECONDARY) {
            onContextMenu(event);
          }
        });

    cellSkin.getRegion().addEventHandler(MouseEvent.MOUSE_MOVED, mouseMovedHandler = (event) -> {
      onMouseMoved(event);
    });

  }

  private void onContextMenu(MouseEvent event) {
    ObservableList<SelectableNode> items = controller.getSelectionModel().getSelectedItems();

    List<ExtraActionInfo> infos = null;
    List<CellObject> targets = new ArrayList<>();
    for (SelectableNode node : items) {
      CellObject cellObject = null;
      CellSkin cellSkin = NodeUtil.selectedNode2CellSkin(node);
      if (cellSkin != null) {
        cellObject = cellSkin.getCell();
      }
      if (cellObject == null) {
        log.warn("Can not find the CellObject from the SelectableNode.");
        if (infos != null) {
          infos.clear();
        }
        break;
      }
      if (cellObject.getBindedObject() == null) {
        if (infos != null) {
          infos.clear();
        }
        break;
      }
      targets.add(cellObject);

      if (infos == null) {
        infos = new ArrayList<>(cellObject.getBindedObject().getExtraActions());
      } else {
        // 删除不一样的项
        Map<String, ExtraActionInfo> checkInfos = new HashMap<>();
        for (ExtraActionInfo info : cellObject.getBindedObject().getExtraActions()) {
          checkInfos.put(info.getActionId(), info);
        }

        List<ExtraActionInfo> newInfos = new ArrayList<>();
        for (ExtraActionInfo info : infos) {
          if (checkInfos.containsKey(info.getActionId())
              && checkInfos.get(info.getActionId()).getActionName().equals(info.getActionName())) {
            newInfos.add(info);
          }
        }

        infos = newInfos;
      }
    }

    if (infos == null || infos.isEmpty()) {
      return;
    }

    ContextMenu menu = new ContextMenu();
    for (ExtraActionInfo info : infos) {
      MenuItem item = new MenuItem(info.getActionName());
      item.setOnAction((mouseevent) -> {
        controller.getEventBus().post(new ExtraActionEvent(info.getActionId(), targets));
      });
      menu.getItems().add(item);
    }
    menu.show(cellSkin.getRegion(), event.getScreenX(), event.getScreenY());
  }

  private void onMouseDragged(MouseEvent event) {
    final Region n = cellSkin.getRegion();

    final double parentScaleX = n.getParent().localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY = n.getParent().localToSceneTransformProperty().getValue().getMyy();

    double offsetX = event.getSceneX() - mouseX;
    double offsetY = event.getSceneY() - mouseY;
    double layoutOffsetX = offsetX / parentScaleX;
    double layoutOffsetY = offsetY / parentScaleY;

    Bounds boundsInScene = n.localToScene(n.getBoundsInLocal());

    double sceneX = boundsInScene.getMinX();
    double sceneY = boundsInScene.getMinY();

    if (controller.isCellMovable() && !isResizing() && cellSkin.isMovable()) {
      if (cellSkin.getCell().getXProperty().isBound()) {
        cellSkin.getCell().getXProperty().unbind();
      }
      if (cellSkin.getCell().getYProperty().isBound()) {
        cellSkin.getCell().getYProperty().unbind();
      }
      n.setLayoutX(nodeX + layoutOffsetX);
      n.setLayoutY(nodeY + layoutOffsetY);

      setDragging(true);

      if (cellSkin.isSelected()) {
        dragSelectedWindows((event.getSceneX() - lastMouseX) / parentScaleX,
            (event.getSceneY() - lastMouseY) / parentScaleY);
      }
    } else if (controller.isCellResizable() && isResizing()) {
      if (resizeTop) {
        double insetOffset = n.getInsets().getTop() * 0.5;
        double ydiff = sceneY / parentScaleY + insetOffset - event.getSceneY() / parentScaleY;
        double newHeight = n.getPrefHeight() + ydiff;
        if (newHeight > n.minHeight(0)) {
          n.setLayoutY(n.getLayoutY() - ydiff);
          n.setPrefHeight(newHeight);
        }
      }
      if (resizeLeft) {
        double insetOffset = n.getInsets().getLeft() * 0.5;
        double xdiff = sceneX / parentScaleX + insetOffset - event.getSceneX() / parentScaleX;
        double newWidth = n.getPrefWidth() + xdiff;
        if (newWidth > n.minWidth(0)) {
          n.setLayoutX(n.getLayoutX() - xdiff);
          n.setPrefWidth(newWidth);
        }
      }

      if (resizeBottom) {
        double insetOffset = n.getInsets().getBottom() * 0.5;
        double ydiff = event.getSceneY() / parentScaleY - sceneY / parentScaleY - insetOffset;
        double newHeight = ydiff;
        newHeight = Math.max(newHeight, n.minHeight(0));
        if (newHeight < n.maxHeight(0)) {
          n.setPrefHeight(newHeight);
        }
      }
      if (resizeRight) {
        double insetOffset = n.getInsets().getRight() * 0.5;
        double xdiff = event.getSceneX() / parentScaleX - sceneX / parentScaleY - insetOffset;
        double newWidth = xdiff;
        newWidth = Math.max(newWidth, n.minWidth(0));
        if (newWidth < n.maxWidth(0)) {
          n.setPrefWidth(newWidth);
        }
      }
    }

    lastMouseX = event.getSceneX();
    lastMouseY = event.getSceneY();

    event.consume();
  }

  private void onMouseMoved(MouseEvent event) {
    if (!controller.isCellResizable()) {
      return;
    }
    final Region n = cellSkin.getRegion();

    final double scaleX = n.localToSceneTransformProperty().getValue().getMxx();
    final double scaleY = n.localToSceneTransformProperty().getValue().getMyy();

    final double border = cellSkin.getResizableBorderWidth() * scaleX;

    double diffMinX =
        Math.abs(n.getLayoutBounds().getMinX() - event.getX());
    double diffMinY =
        Math.abs(n.getLayoutBounds().getMinY() - event.getY());
    double diffMaxX =
        Math.abs(n.getLayoutBounds().getMaxX() - event.getX());
    double diffMaxY =
        Math.abs(n.getLayoutBounds().getMaxY() - event.getY());

    boolean left = diffMinX * scaleX < border;
    boolean top = diffMinY * scaleY < border;
    boolean right = diffMaxX * scaleX < border;
    boolean bottom = diffMaxY * scaleY < border;

    resizeTop = false;
    resizeLeft = false;
    resizeBottom = false;
    resizeRight = false;

    if (cellSkin.isResizeble() && controller.isCellResizable()) {
      if (left && !top && !bottom) {
        n.setCursor(Cursor.W_RESIZE);
        resizeLeft = true;
      } else if (left && top && !bottom) {
        n.setCursor(Cursor.NW_RESIZE);
        resizeLeft = true;
        resizeTop = true;
      } else if (left && !top && bottom) {
        n.setCursor(Cursor.SW_RESIZE);
        resizeLeft = true;
        resizeBottom = true;
      } else if (right && !top && !bottom) {
        n.setCursor(Cursor.E_RESIZE);
        resizeRight = true;
      } else if (right && top && !bottom) {
        n.setCursor(Cursor.NE_RESIZE);
        resizeRight = true;
        resizeTop = true;
      } else if (right && !top && bottom) {
        n.setCursor(Cursor.SE_RESIZE);
        resizeRight = true;
        resizeBottom = true;
      } else if (top && !left && !right) {
        n.setCursor(Cursor.N_RESIZE);
        resizeTop = true;
      } else if (bottom && !left && !right) {
        n.setCursor(Cursor.S_RESIZE);
        resizeBottom = true;
      } else {
        n.setCursor(Cursor.DEFAULT);
      }
    } else {
      n.setCursor(Cursor.DEFAULT);
    }

    n.autosize();
    event.consume();
  }

  private boolean isResizing() {
    return resizeTop || resizeRight || resizeBottom || resizeLeft;
  }

  private void dragSelectedWindows(double offsetForAllX, double offsetForAllY) {
    for (SelectableNode selectableNode : controller.getSelectionModel().getSelectedItems()) {

      if (selectableNode == cellSkin || !(selectableNode instanceof CellSkin)) {
        continue;
      }

      CellSkin selectedWindow = (CellSkin) selectableNode;

      if (cellSkin.getRegion().getParent().equals(selectedWindow.getRegion().getParent()) 
          && selectedWindow.isMovable()) {

        selectedWindow.getRegion()
            .setLayoutX(selectedWindow.getRegion().getLayoutX() + offsetForAllX);
        selectedWindow.getRegion()
            .setLayoutY(selectedWindow.getRegion().getLayoutY() + offsetForAllY);
      }
    } // end for sN
  }


  private void setDragging(boolean dragging) {
    draggingProperty().set(dragging);
  }

  private BooleanProperty draggingProperty() {
    return draggingProperty;
  }

  @Override
  public void createConnectorBehavior(ConnectorSkin skin) {
    controller.createConnectorBehavior(skin);
  }

  @Override
  public void attach() {
    init();
  }

  @Override
  public void detach() {
    cellSkin.selectedProperty().removeListener(borderChangeListener);
    cellSkin.getCell().hoverProperty().removeListener(borderChangeListener);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_PRESSED, mousePressedHandler);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_DRAGGED, mouseDragedHandler);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_RELEASED, mouseReleasedHandler);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_CLICKED, mouseClickedHandler);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_CLICKED, mouseRightClickedHandler);
    cellSkin.getRegion().removeEventHandler(MouseEvent.MOUSE_MOVED, mouseMovedHandler);
  }
}
