package com.mc.graph.connector;

import com.mc.graph.interfaces.ColorComponent;
import com.mc.graph.interfaces.Connector;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.scene.paint.Color;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * 可以统计多个Connector的属性并通知外部.
 *
 */
public class ConnectorIntegration {
  private Set<Connector> connectors = new HashSet<>();

  private BooleanProperty highLightProperty = null;
  private ChangeListener<Boolean> highLightChangeListener;
  private ChangeListener<ColorComponent> highLightColorChangeListener;

  private ObjectProperty<ColorComponent> highLightColorProperty = null;

  /**
   * Constructor.
   */
  public ConnectorIntegration() {
    highLightChangeListener = (obs, oldVal, newVal) -> {
      if (highLightProperty != null) {
        highLightProperty.set(isHighLight());
      }
      if (highLightColorProperty != null) {
        highLightColorProperty.set(getHighLightColor());
      }
    };

    highLightColorChangeListener = (obs, oldVal, newVal) -> {
      if (highLightColorProperty != null) {
        highLightColorProperty.set(getHighLightColor());
      }
    };
  }

  /**
   * 设置要统计的Connectors.
   */
  public void setConnectors(Collection<Connector> connectors) {
    Set<Connector> newSet = new HashSet<>(connectors);
    if (!this.connectors.equals(newSet)) {
      // 更新高亮的监听与值
      for (Connector connector : this.connectors) {
        connector.highLightProperty().removeListener(highLightChangeListener);
        connector.highLightColorProperty().removeListener(highLightColorChangeListener);
      }
      this.connectors.clear();
      this.connectors.addAll(newSet);
      for (Connector connector : this.connectors) {
        connector.highLightProperty().addListener(highLightChangeListener);
        connector.highLightColorProperty().addListener(highLightColorChangeListener);
      }
    }
    if (highLightProperty != null) {
      highLightProperty.set(isHighLight());
    }
    if (highLightColorProperty != null) {
      highLightColorProperty.set(getHighLightColor());
    }
  }

  /**
   * 绑定多个Connector的highlight的值到目标属性.
   * 
   * @param value 目标属性
   */
  public void bindHighLightValue(BooleanProperty value) {
    highLightProperty = value;
    if (highLightProperty != null) {
      highLightProperty.set(isHighLight());
    }
  }

  /**
   * 绑定多个Connector的hightlight color的值到目标属性.
   * @param value 目标属性
   */
  public void bindHighLightColorValue(ObjectProperty<ColorComponent> value) {
    highLightColorProperty = value;
    if (highLightColorProperty != null) {
      highLightColorProperty.set(getHighLightColor());
    }
  }

  protected boolean isHighLight() {
    boolean result = false;
    for (Connector connector : connectors) {
      result |= connector.highLightProperty().get();
    }
    return result;
  }

  protected ColorComponent getHighLightColor() {
    Set<ColorComponent> colors = new HashSet<>();
    for (Connector connector : connectors) {
      if (!connector.highLightProperty().get()) {
        continue;
      }
      if (connector.highLightColorProperty().get() == null) {
        continue;
      }
      colors.add(connector.highLightColorProperty().get());
    }
    return mergeColorComponent(colors);
  }

  protected ColorComponent mergeColorComponent(Collection<ColorComponent> colors) {
    Set<Color> colorSet = new HashSet<>();
    for (ColorComponent colorComponent : colors) {
      colorSet.addAll(colorComponent.getRealColorComponets());
    }
    return mergeColor(colorSet);
  }

  protected ColorComponent mergeColor(Collection<Color> colors) {
    ColorComponent component = new ColorComponent();
    if (colors.isEmpty()) {
      return component;
    }
    double red = 0;
    double green = 0;
    double blue = 0;
    StringBuilder colorBuilder = new StringBuilder();
    for (Color item : colors) {
      red += item.getRed();
      green += item.getGreen();
      blue += item.getBlue();
      colorBuilder.append(String.format("#%02X%02X%02X;", (int) (item.getRed() * 255),
          (int) (item.getGreen() * 255), (int) (item.getBlue() * 255)));
    }
    red /= colors.size();
    green /= colors.size();
    blue /= colors.size();
    component.setMergedColor(Color.color(red, green, blue));
    component.setColorComponents(colorBuilder.toString());
    return component;
  }

  /**
   * 销毁.
   */
  public void destroy() {
    for (Connector connector : this.connectors) {
      connector.highLightProperty().removeListener(highLightChangeListener);
      connector.highLightColorProperty().removeListener(highLightColorChangeListener);
    }
    connectors.clear();
  }


}
