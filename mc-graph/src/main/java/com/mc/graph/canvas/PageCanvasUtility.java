package com.mc.graph.canvas;

import javafx.geometry.Point2D;
import javafx.geometry.Rectangle2D;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

/**
 * 用于PageCanvas的实用函数集合.
 */
public class PageCanvasUtility {

  /**
   * 获取areas中包含point的所有区域.
   *
   * @param areas areas
   * @param point point
   * @return 包含point的所有区域
   */
  public static Collection<Rectangle2D> getIntersectedAreas(Collection<Rectangle2D> areas,
                                                            Point2D point) {
    List<Rectangle2D> result = new ArrayList<>();
    for (Rectangle2D area : areas) {
      if (area.contains(point)) {
        result.add(area);
      }
    }
    return result;
  }

  /**
   * 获取areas中与rect相交的所有区域.
   *
   * @param areas areas
   * @param rect  rect
   * @return 所有相交区域
   */
  public static Collection<Rectangle2D> getIntersectedAreas(Collection<Rectangle2D> areas,
                                                            Rectangle2D rect) {
    List<Rectangle2D> result = new ArrayList<>();
    for (Rectangle2D area : areas) {
      if (area.intersects(rect)) {
        result.add(area);
      }
    }
    return result;
  }

  /**
   * 获取包含location的所有区域的索引.
   *
   * @param areas    areas
   * @param location location
   * @return 索引的集合
   */
  public static Collection<Integer> getIntersectedAreasIndex(Collection<Rectangle2D> areas,
                                                             Point2D location) {
    List<Integer> result = new ArrayList<>();
    int index = 0;
    for (Rectangle2D area : areas) {
      if (area.contains(location)) {
        result.add(index);
      }
      index++;
    }
    return result;
  }

  /**
   * 合并区域.
   *
   * @param areas areas
   * @return 合并后的区域
   */
  public static Rectangle2D mergeAreas(Collection<Rectangle2D> areas) {
    double minX = Double.MAX_VALUE;
    double minY = Double.MAX_VALUE;
    double maxX = Double.MIN_VALUE;
    double maxY = Double.MIN_VALUE;
    for (Rectangle2D item : areas) {
      minX = Math.min(minX, item.getMinX());
      minY = Math.min(minY, item.getMinY());
      maxX = Math.max(maxX, item.getMaxX());
      maxY = Math.max(maxY, item.getMaxY());
    }
    return new Rectangle2D(minX, minY, maxX - minX, maxY - minY);
  }

  /**
   * 获取一个网格的范围.
   *
   * @param widths       网格的所有宽度
   * @param heights      网格的所有高度
   * @param gridLocation 要计算的网格的位置
   * @return 网格的范围
   */
  public static Rectangle2D getGridRect(List<Integer> widths, List<Integer> heights,
                                        Point2D gridLocation) {
    if (gridLocation.getX() < 0 || gridLocation.getX() >= widths.size() || gridLocation.getY() < 0
        || gridLocation.getY() >= heights.size()) {
      return new Rectangle2D(0, 0, 0, 0);
    }
    double xpos = getColumnStartPos(widths, (int) gridLocation.getX());
    double ypos = getRowStartPos(heights, (int) gridLocation.getY());
    return new Rectangle2D(xpos, ypos, widths.get((int) gridLocation.getX()),
        heights.get((int) gridLocation.getY()));
  }

  /**
   * 计算特定位置所在的列.
   *
   * @param widths 所有列的宽度
   * @param xpos   横坐标位置
   * @return 列索引，从0开始，如果超出范围，那么返回列的size.
   */
  public static int indexOfColumn(List<Integer> widths, double xpos) {
    for (int i = 0; i < widths.size(); i++) {
      if (xpos < widths.get(i)) {
        return i;
      }
      xpos -= widths.get(i);
    }
    return widths.size();
  }

  /**
   * 计算特定位置所在的行.
   *
   * @param heights 所有的行的高度
   * @param ypos    纵坐标位置
   * @return 行索引，从0开始，如果超出范围，那么返回行的size.
   */
  public static int indexOfRow(List<Integer> heights, double ypos) {
    for (int i = 0; i < heights.size(); i++) {
      if (ypos < heights.get(i)) {
        return i;
      }
      ypos -= heights.get(i);
    }
    return heights.size();
  }

  /**
   * 获取指定列的开始位置.
   *
   * @param widths 所有列的宽度
   * @param index  列索引，从0开始
   * @return 列的开始位置，如果列索引超出范围，返回0
   */
  public static int getColumnStartPos(List<Integer> widths, int index) {
    if (index >= widths.size()) {
      return 0;
    }
    int xpos = 0;
    for (int i = 0; i < index; i++) {
      xpos += widths.get(i);
    }
    return xpos;
  }

  /**
   * 获取指定行的开始位置.
   *
   * @param heights 所有行的高度
   * @param index   行索引，从0开始
   * @return 行的开始位置，如果索引超出范围，返回0
   */
  public static int getRowStartPos(List<Integer> heights, int index) {
    if (index >= heights.size()) {
      return 0;
    }
    int ypos = 0;
    for (int i = 0; i < index; i++) {
      ypos += heights.get(i);
    }
    return ypos;
  }

  /**
   * 获取指定列的结束位置(exclusive).
   *
   * @param widths 所有列的宽度
   * @param index  列索引，从0开始
   * @return 列的开始位置，如果索引超出范围，返回0
   */
  public static int getColumnEndPos(List<Integer> widths, int index) {
    if (index >= widths.size()) {
      return 0;
    }
    int xpos = 0;
    for (int i = 0; i <= index; i++) {
      xpos += widths.get(i);
    }
    return xpos;
  }

  /**
   * 获取指定行的结束位置(exclusive).
   *
   * @param heights 所有行的高度
   * @param index   行索引，从0开始
   * @return 行的开始位置，如果索引超出范围，返回0
   */
  public static int getRowEndPos(List<Integer> heights, int index) {
    if (index >= heights.size()) {
      return 0;
    }
    int ypos = 0;
    for (int i = 0; i <= index; i++) {
      ypos += heights.get(i);
    }
    return ypos;
  }

  /**
   * 获取整页宽度.
   *
   * @param areas 所有显示区域
   * @return 整页宽度
   */
  public static int getPageWidth(Collection<Rectangle2D> areas) {
    int result = 0;
    for (Rectangle2D rect : areas) {
      result = (int) Math.max(rect.getMaxX(), result);
    }
    return result;
  }

  /**
   * 获取整页高度.
   *
   * @param areas 所有显示区域.
   * @return 整页高度
   */
  public static int getPageHeight(Collection<Rectangle2D> areas) {
    int result = 0;
    for (Rectangle2D rect : areas) {
      result = (int) Math.max(rect.getMaxY(), result);
    }
    return result;
  }

  /**
   * 获取最小的网格宽度.
   *
   * @param areas 所有区域
   * @return 最小的网格的宽度
   */
  public static int getMinGridWidth(Collection<Rectangle2D> areas) {
    if (areas.size() == 0) {
      return 100;
    }
    return areas.stream().map((item) -> (int) item.getWidth())
        .min(Comparator.comparingInt((value) -> value)).get();
  }

  /**
   * 获取最小的网格高度.
   *
   * @param areas 所有区域
   * @return 最小的网格的高度
   */
  public static int getMinGridHeight(Collection<Rectangle2D> areas) {
    if (areas.size() == 0) {
      return 100;
    }
    return areas.stream().map((item) -> (int) item.getHeight())
        .min(Comparator.comparingInt((value) -> value)).get();
  }

  public static int getGridRows(Collection<Integer> heights) {
    return heights.size();
  }

  public static int getGridColumns(Collection<Integer> widths) {
    return widths.size();
  }

  /**
   * 集合中所有的值是否都大于0.
   *
   * @param values 值的集合
   * @return 如果值都大于0，返回true
   */
  public static boolean isAllGreaterThanZero(Collection<Integer> values) {
    for (int value : values) {
      if (value <= 0) {
        return false;
      }
    }
    return true;
  }

  /**
   * 根据网格的宽度与高度创建区域.
   *
   * @param gridWidths 网格宽度
   * @param gridHeights 网格高度
   * @return 区域集合
   */
  public static Collection<Rectangle2D> createGridRectangles(List<Integer> gridWidths,
                                                             List<Integer> gridHeights) {
    List<Rectangle2D> result = new ArrayList<>();
    int ypos = 0;
    for (int i = 0; i < gridHeights.size(); i++) {
      int xpos = 0;
      for (int j = 0; j < gridWidths.size(); j++) {
        result.add(new Rectangle2D(xpos, ypos, gridWidths.get(j), gridHeights.get(i)));
        xpos += gridWidths.get(j);
      }
      ypos += gridHeights.get(i);
    }
    return result;
  }
}
