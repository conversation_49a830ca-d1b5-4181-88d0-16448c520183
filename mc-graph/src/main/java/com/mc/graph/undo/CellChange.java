package com.mc.graph.undo;

import com.mc.graph.McGraphModel;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.UndoableChange;

public class CellChange implements UndoableChange {
  private McGraphModel model;
  private CellObject cellObject;
  protected int index;
  /**
   * whether to add.
   */
  private boolean previous;

  /**
   * Cell change info.
   * @param model  graph model
   * @param cell   cell to add or remove
   * @param index  cell's index in model
   * @param add    add or remove
   */
  public CellChange(McGraphModel model, CellObject cell, int index, boolean add) {
    this.model = model;
    this.cellObject = cell;
    this.index = index;
    this.previous = add;
  }

  @Override
  public void execute() {
    if (previous) {
      model.innserInsertCell(cellObject, index);
    } else {
      model.innerRemoveCell(cellObject);
    }
    previous = !previous;
  }

}
