package com.mc.graph.undo;

import com.mc.graph.interfaces.UndoableChange;
import com.mc.graph.interfaces.UndoableEdit;

import java.util.ArrayList;
import java.util.List;

public class DefaultUndoableEdit implements UndoableEdit {
  private List<UndoableChange> changeList = new ArrayList<>();

  @Override
  public void undo() {
    int count = changeList.size();
    for (int i = count - 1; i >= 0; i--) {
      changeList.get(i).execute();
    }
  }

  @Override
  public void redo() {
    for (UndoableChange change : changeList) {
      change.execute();
    }
  }

  @Override
  public void addChange(UndoableChange change) {
    changeList.add(change);
  }

}
