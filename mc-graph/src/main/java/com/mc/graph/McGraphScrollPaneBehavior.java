/*
 * Copyright (c) 2010, 2014, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.mc.graph;

import com.sun.javafx.scene.control.behavior.BehaviorBase;
import com.sun.javafx.scene.control.behavior.KeyBinding;
import javafx.geometry.NodeOrientation;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * Behavior for ScrollPane. TODO: the function variables are a poor way to couple to the rest of the
 * system. This technique avoids a direct dependency on the skin class. However, this should really
 * be coupled through the control itself instead of directly to the skin.
 */
public class McGraphScrollPaneBehavior extends BehaviorBase<ScrollPane> {


  public McGraphScrollPaneBehavior(ScrollPane scrollPane) {
    super(scrollPane, SCROLL_PANE_BINDINGS);
  }

  public void horizontalUnitIncrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).hsbIncrement();
  }

  public void horizontalUnitDecrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).hsbDecrement();
  }

  public void verticalUnitIncrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).vsbIncrement();
  }

  void verticalUnitDecrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).vsbDecrement();
  }

  void horizontalPageIncrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).hsbPageIncrement();
  }

  void horizontalPageDecrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).hsbPageDecrement();
  }

  void verticalPageIncrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).vsbPageIncrement();
  }

  void verticalPageDecrement() {
    ((McGraphCanvasSkin) getControl().getSkin()).vsbPageDecrement();
  }

  void verticalHome() {
    ScrollPane sp = getControl();
    sp.setHvalue(sp.getHmin());
    sp.setVvalue(sp.getVmin());
  }

  void verticalEnd() {
    ScrollPane sp = getControl();
    sp.setHvalue(sp.getHmax());
    sp.setVvalue(sp.getVmax());
  }


  /**
   * Content Dragged.
   * 
   * @param deltaX deltax
   * @param deltaY deltay
   */
  public void contentDragged(double deltaX, double deltaY) {
    // negative when dragged to the right/bottom
    ScrollPane scroll = getControl();
    if (!scroll.isPannable()) {
      return;
    }
    if (deltaX < 0 && scroll.getHvalue() != 0
        || deltaX > 0 && (scroll.getHvalue() - scroll.getHmax()) != 0) {
      scroll.setHvalue(scroll.getHvalue() + deltaX);
    }
    if (deltaY < 0 && scroll.getVvalue() != 0
        || deltaY > 0 && (scroll.getVvalue() - scroll.getVmax()) != 0) {
      scroll.setVvalue(scroll.getVvalue() + deltaY);
    }
  }

  static final String TRAVERSE_DEBUG = "TraverseDebug";
  static final String HORIZONTAL_UNITDECREMENT = "HorizontalUnitDecrement";
  static final String HORIZONTAL_UNITINCREMENT = "HorizontalUnitIncrement";
  static final String VERTICAL_UNITDECREMENT = "VerticalUnitDecrement";
  static final String VERTICAL_UNITINCREMENT = "VerticalUnitIncrement";
  static final String VERTICAL_PAGEDECREMENT = "VerticalPageDecrement";
  static final String VERTICAL_PAGEINCREMENT = "VerticalPageIncrement";
  static final String VERTICAL_HOME = "VerticalHome";
  static final String VERTICAL_END = "VerticalEnd";

  /**
   * We manually handle focus traversal keys due to the ScrollPane binding the left/right/up/down
   * keys specially.
   */
  static final List<KeyBinding> SCROLL_PANE_BINDINGS = new ArrayList<>();

  static {
    // TODO XXX DEBUGGING ONLY
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.F4, TRAVERSE_DEBUG).alt().ctrl().shift());

    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.LEFT, HORIZONTAL_UNITDECREMENT));
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.RIGHT, HORIZONTAL_UNITINCREMENT));

    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.UP, VERTICAL_UNITDECREMENT));
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.DOWN, VERTICAL_UNITINCREMENT));

    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.PAGE_UP, VERTICAL_PAGEDECREMENT));
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.PAGE_DOWN, VERTICAL_PAGEINCREMENT));
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.SPACE, VERTICAL_PAGEINCREMENT));

    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.HOME, VERTICAL_HOME));
    SCROLL_PANE_BINDINGS.add(new KeyBinding(KeyCode.END, VERTICAL_END));
  }

  protected /* final */ String matchActionForEvent(KeyEvent event) {
    // TODO - untested code doesn't seem to get triggered (key eaten?)
    String action = super.matchActionForEvent(event);
    if (action != null) {
      if (event.getCode() == KeyCode.LEFT) {
        if (getControl().getEffectiveNodeOrientation() == NodeOrientation.RIGHT_TO_LEFT) {
          action = "HorizontalUnitIncrement";
        }
      } else if (event.getCode() == KeyCode.RIGHT
          && getControl().getEffectiveNodeOrientation() == NodeOrientation.RIGHT_TO_LEFT) {
        action = "HorizontalUnitDecrement";
      }
    }
    return action;
  }

  @Override
  protected void callAction(String name) {
    switch (name) {
      case HORIZONTAL_UNITDECREMENT:
        horizontalUnitDecrement();
        break;
      case HORIZONTAL_UNITINCREMENT:
        horizontalUnitIncrement();
        break;
      case VERTICAL_UNITDECREMENT:
        verticalUnitDecrement();
        break;
      case VERTICAL_UNITINCREMENT:
        verticalUnitIncrement();
        break;
      case VERTICAL_PAGEDECREMENT:
        verticalPageDecrement();
        break;
      case VERTICAL_PAGEINCREMENT:
        verticalPageIncrement();
        break;
      case VERTICAL_HOME:
        verticalHome();
        break;
      case VERTICAL_END:
        verticalEnd();
        break;
      default:
        super.callAction(name);
        break;
    }
  }

  public void mouseClicked() {
    getControl().requestFocus();
  }

  @Override
  public void mousePressed(MouseEvent event) {
    super.mousePressed(event);
    getControl().requestFocus();
  }
}
