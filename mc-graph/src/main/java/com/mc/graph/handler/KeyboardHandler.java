package com.mc.graph.handler;

import com.mc.graph.action.DeleteCellAction;
import com.mc.graph.interfaces.GraphController;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;



/**
 * Keyboard handler.
 */
public class KeyboardHandler {
  private GraphController controller;
  private Map<KeyCodeCombination, EventHandler<ActionEvent>> actionMap = new HashMap<>();

  public KeyboardHandler(GraphController controller) {
    this.controller = controller;
    initActionMap();
  }

  protected void initActionMap() {
    actionMap.put(new KeyCodeCombination(KeyCode.DELETE), new DeleteCellAction(controller));
  }

  /**
   * Install the keyboard handler to graph canvas.
   */
  public void install() {
    controller.getGraphCanvas().getContainer().setFocusTraversable(true);
    controller.getGraphCanvas().getContainer().addEventHandler(MouseEvent.MOUSE_CLICKED,
        (event) -> controller.getGraphCanvas().getContainer().requestFocus());
    controller.getGraphCanvas().getContainer().addEventHandler(KeyEvent.KEY_PRESSED, (event) -> {
      for (Entry<KeyCodeCombination, EventHandler<ActionEvent>> entry : actionMap.entrySet()) {
        if (entry.getKey().match(event)) {
          entry.getValue().handle(null);
        }
      }
    });
  }
}
