package com.mc.graph.interfaces;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.collections.ObservableList;

import java.util.Map;

public interface CellObject extends GraphObject {
  CellBindedObject getBindedObject();
  
  void setBindedObject(CellBindedObject object);
  
  ObjectProperty<CellBindedObject> getBindedObjectProperty();
  
  ObjectProperty<Object> getValueProperty();
  
  DoubleProperty getXProperty();
  
  DoubleProperty getYProperty();
  
  DoubleProperty getAngleProperty();
  
  DoubleProperty getWidthProperty();
  
  DoubleProperty getHeightProperty();
  
  BooleanProperty getSelectedProperty();
  
  /**
   * Get the connectors.
   * @return id and connector map.
   */
  ObservableList<Connector> getConnectors();
  
  Connector getConnector(Object id);
  
  boolean addConnector(Connector ...connector);
  
  /**
   * 删除connecor.
   * @param ids 要删除的connector的id
   * @return 已删除的connector的列表
   */
  Connector[] removeConnector(Object... ids);
  
  void setType(String type);
  
  String getType();
  
  CellObject cloneCell();
  
  Map<String, CellPropertyUpdater> getPropertyUpdaters();
  
}
