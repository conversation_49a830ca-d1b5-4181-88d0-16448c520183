package com.mc.graph.interfaces;

import javafx.beans.property.DoubleProperty;
import javafx.scene.Parent;

public interface SkinFactory {
  String DEFAULT_CELL = "defaultCell";
  String DEFAULT_LINK = "defaultLink";

  /**
   * Create new cell skin.
   * 
   * @param cellObject cell to be skin
   * @param parent parent to add the skin
   * @param container container to add cells
   * @param skinManager skin manager
   * @return created skin
   */
  CellSkin createCellSkin(CellObject cellObject, Parent parent, Parent container,
      SkinManager skinManager);
  
  /**
   * Create new cell skin.
   * 
   * @param cellObject cell to be skin
   * @param parent parent to add the skin
   * @param container container to add cells
   * @param skinManager skin manager
   * @param cellScaleProperty cell scale factor
   * @return created skin
   */
  CellSkin createScaleCellSkin(CellObject cellObject, Parent parent, Parent container,
      SkinManager skinManager, DoubleProperty cellScaleProperty);

  ConnectorSkin createConnectorSkin(String type, Parent parent);

  NewLinkSkin createNewLinkSkin(String type, Connector sender, Parent parent, Parent container,
      SkinManager skinManager);

  /**
   * Create new link skin.
   * 
   * @param link link to be skin
   * @param parent parent to add the skin
   * @param container TODO
   * @param skinManager skin manager
   * @return created skin
   */
  LinkSkin createLinkSkin(LinkObject link, Parent parent, Parent container,
      SkinManager skinManager);
}
