package com.mc.graph;

import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorBehavior;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.util.NodeUtil;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.scene.Parent;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * Abstract connector skin.
 */
public abstract class AbstractConnectorSkin implements ConnectorSkin {
  protected Connector connector;
  @Getter
  protected List<ConnectorBehavior> behaviors = new ArrayList<>();

  protected Parent parent;
  protected Parent container;
  protected DoubleProperty containerXposProperty = new SimpleDoubleProperty();
  protected DoubleProperty containerYposProperty = new SimpleDoubleProperty();
  protected SimpleBooleanProperty linkableProperty = new SimpleBooleanProperty(true);

  /**
   * Constructor.
   *
   * @param connector the connector to be skinned
   * @param parent    parent to add the skin
   * @param container TODO
   */
  public AbstractConnectorSkin(Connector connector, Parent parent, Parent container) {
    this.parent = parent;
    this.container = container;
    setConnector(connector);
    initNode();
  }

  protected abstract void initNode();

  @Override
  public void setConnector(Connector connector) {
    this.connector = connector;
  }

  @Override
  public Connector getConnector() {
    return connector;
  }

  @Override
  public void addBehavior(ConnectorBehavior behavior) {
    behaviors.add(behavior);
    behavior.attach();
  }


  @Override
  public void add() {
    NodeUtil.addToParent(parent, getNode());
    getNode().toFront();
  }


  @Override
  public void remove() {
    NodeUtil.removeFromParent(getNode());
  }

  @Override
  public DoubleProperty getContainerXposProperty() {
    return containerXposProperty;
  }

  @Override
  public DoubleProperty getContainerYposProperty() {
    return containerYposProperty;
  }

  @Override
  public BooleanProperty linkableProperty() {
    return linkableProperty;
  }

  @Override
  public Parent getParent() {
    return parent;
  }

  @Override
  public void destroy() {
    for (ConnectorBehavior behavior : behaviors) {
      behavior.detach();
    }
    behaviors.clear();
    containerXposProperty.unbind();
    containerYposProperty.unbind();
    linkableProperty.unbind();

    parent = null;
    container = null;
    connector = null;
  }

}
