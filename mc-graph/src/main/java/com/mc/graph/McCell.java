package com.mc.graph;

import com.mc.graph.connector.ConnectorIntegration;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

class Mc<PERSON>ell extends AbstractGraphObject implements CellObject {
  private String type = "";

  private ObjectProperty<CellBindedObject> bindedObjectProperty = new SimpleObjectProperty<>();
  private SimpleDoubleProperty xposProperty = new SimpleDoubleProperty();
  private SimpleDoubleProperty yposProperty = new SimpleDoubleProperty();
  private SimpleDoubleProperty angleProperty = new SimpleDoubleProperty();
  private SimpleDoubleProperty widthProperty = new SimpleDoubleProperty();
  private SimpleDoubleProperty heightProperty = new SimpleDoubleProperty();
  private SimpleBooleanProperty selectedProperty = new SimpleBooleanProperty();
  private SimpleObjectProperty<Object> valueProperty = new SimpleObjectProperty<>();

  private ObservableList<Connector> connectors = FXCollections.observableArrayList();
  private Map<ConnectorIdentifier, Connector> connectorMap = new HashMap<>();
  private ObservableList<Connector> readOnlyConnectors = 
      FXCollections.unmodifiableObservableList(connectors);
  
  private ConnectorIntegration connectorIntegration = new ConnectorIntegration();
  
  private Map<String, CellPropertyUpdater> propertyUpdaters = new HashMap<>();

  public McCell() {
    connectorIntegration.bindHighLightValue(highLightProperty());
    connectorIntegration.bindHighLightColorValue(highLightColorProperty());
  }
  
  @Override
  public CellBindedObject getBindedObject() {
    return bindedObjectProperty.get();
  }

  @Override
  public void setBindedObject(CellBindedObject object) {
    if (bindedObjectProperty.get() != object) {
      unbindObject(object);
      bindedObjectProperty.set(object);

      ObservableList<ConnectorIdentifier> ids = object.getConnectorId();
      Connector[] connectorArray = new Connector[ids.size()];
      for (int i = 0; i < ids.size(); i++) {
        connectorArray[i] = new McConnector(ids.get(i), this);
      }
      addConnector(connectorArray);

      ids.addListener(new CellConnectorChangeListener(this));
    }
  }

  private void unbindObject(CellBindedObject object) {
    if (object == null) {
      return;
    }
    object.getConnectorId().removeListener(new CellConnectorChangeListener(this));
  }

  @Override
  public ObjectProperty<Object> getValueProperty() {
    return valueProperty;
  }

  @Override
  public DoubleProperty getXProperty() {
    return xposProperty;
  }

  @Override
  public DoubleProperty getYProperty() {
    return yposProperty;
  }  

  @Override
  public DoubleProperty getAngleProperty() {
    return angleProperty;
  }

  @Override
  public DoubleProperty getWidthProperty() {
    return widthProperty;
  }

  @Override
  public DoubleProperty getHeightProperty() {
    return heightProperty;
  }

  @Override
  public BooleanProperty getSelectedProperty() {
    return selectedProperty;
  }

  @Override
  public ObservableList<Connector> getConnectors() {
    return readOnlyConnectors;
  }

  @Override
  public boolean addConnector(Connector ...connector) {
    Set<ConnectorIdentifier> idSet = connectorMap.keySet();
    
    List<Connector> toAddItems = Arrays.asList(connector).stream()
        .filter((item) -> !idSet.contains(item.getId()))
        .collect(Collectors.toList());
    for (Connector item : toAddItems) {
      connectorMap.put(item.getId(), item);
    }
    connectors.addAll(toAddItems);
    return true;
  }

  @Override
  public CellObject cloneCell() {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public void setType(String type) {
    this.type = type;
  }

  @Override
  public String getType() {
    return type;
  }

  @Override
  public ObjectProperty<CellBindedObject> getBindedObjectProperty() {
    return bindedObjectProperty;
  }

  @Override
  public Connector getConnector(Object id) {
    return connectorMap.get(id);
  }

  @Override
  public Connector[] removeConnector(Object... ids) {
    List<Connector> result = new ArrayList<>();
    for (Object id : ids) {
      Connector item = connectorMap.get(id);
      if (item != null) {
        result.add(item);
        connectorMap.remove(id);
      }
    }
    connectors.removeAll(result);
    return result.toArray(new Connector[0]);
  }

  @Override
  public ConnectorIntegration getConnectorIntegration() {
    return connectorIntegration;
  }

  @Override
  public void destroy() {
    connectorIntegration.destroy();
    for (CellPropertyUpdater updater : propertyUpdaters.values()) {
      updater.destroy();
    }
    propertyUpdaters.clear();
    unbindObject(bindedObjectProperty.get());
  }

  @Override
  public Map<String, CellPropertyUpdater> getPropertyUpdaters() {
    return propertyUpdaters;
  }

  static class CellConnectorChangeListener implements ListChangeListener<ConnectorIdentifier> {
    private final CellObject cell;

    public CellConnectorChangeListener(CellObject cell) {
      this.cell = cell;
    }

    @Override
    public void onChanged(Change<? extends ConnectorIdentifier> change) {
      while (change.next()) {
        if (change.getRemovedSize() > 0) {
          cell.removeConnector(change.getRemoved().toArray());
        }
        if (change.getAddedSize() > 0) {
          Connector[] addedArray = new Connector[change.getAddedSize()];
          int index = 0;
          for (ConnectorIdentifier id : change.getAddedSubList()) {
            addedArray[index] = new McConnector(id, cell);
            index++;
          }
          cell.addConnector(addedArray);
        }
      }
    }

    @Override
    public int hashCode() {
      return cell.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
      if (obj instanceof CellConnectorChangeListener) {
        return cell == ((CellConnectorChangeListener)obj).cell;
      } else {
        return false;
      }
    }

  }

}