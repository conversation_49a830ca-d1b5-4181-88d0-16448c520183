package com.mc.graph;

import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.LinkUtil;
import com.mc.graph.util.NodeUtil;
import javafx.scene.Parent;
import javafx.scene.shape.Path;
import lombok.Getter;

public class DefaultLinkSkin implements LinkSkin {
  protected Parent parent;
  protected Parent container;
  protected LinkObject link;
  protected Path connectionPath;
  protected SkinManager skinManager;
  
  @Getter
  protected WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * Contructor.
   * 
   * @param link link to be skin.
   * @param parent parent to add components.
   * @param container TODO
   * @param skinManager skin manager
   */
  public DefaultLinkSkin(LinkObject link, Parent parent, Parent container,
      SkinManager skinManager) {
    this.link = link;
    this.parent = parent;
    this.container = container;
    this.skinManager = skinManager;
    init();
  }

  private void init() {
    initConnnectionPath();
  }

  protected void initConnnectionPath() {
    final ConnectorSkin senderNode = skinManager.getConnectorSkin(link.getConnectors().getKey());
    final ConnectorSkin receiverNode =
        skinManager.getConnectorSkin(link.getConnectors().getValue());
    connectionPath = LinkUtil.createCurveLinkPath(senderNode, receiverNode, parent, container);
    connectionPath.visibleProperty().bind(link.visibleProperty());
  }

  @Override
  public void add() {
    NodeUtil.addToParent(parent, connectionPath);
  }

  @Override
  public void remove() {
    NodeUtil.removeFromParent(connectionPath);
  }

  @Override
  public Parent getParent() {
    return parent;
  }

}
