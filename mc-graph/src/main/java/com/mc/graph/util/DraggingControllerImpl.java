package com.mc.graph.util;

import com.mc.graph.util.MouseControlUtil.DraggableParams;
import javafx.event.EventHandler;
import javafx.geometry.Point2D;
import javafx.scene.Node;
import javafx.scene.input.MouseEvent;

class DraggingControllerImpl {

  private double nodeX;
  private double nodeY;
  private double mouseX;
  private double mouseY;
  private EventHandler<MouseEvent> mouseDraggedEventHandler;
  private EventHandler<MouseEvent> mousePressedEventHandler;
  private boolean centerNode = false;
  private DraggableParams params;

  public DraggingControllerImpl() {
    //
  }

  public void apply(Node node, EventHandlerGroup<MouseEvent> draggedEvtHandler,
      EventHandlerGroup<MouseEvent> pressedEvtHandler, boolean centerNode, DraggableParams params) {
    init(node, params);
    draggedEvtHandler.addHandler(mouseDraggedEventHandler);
    pressedEvtHandler.addHandler(mousePressedEventHandler);
    this.centerNode = centerNode;
  }

  private void init(final Node node, DraggableParams params) {
    this.params = params;
    mouseDraggedEventHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {

        performDrag(node, event);

        event.consume();
      }
    };

    mousePressedEventHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {

        performDragBegin(node, event);
        event.consume();
      }
    };
  }

  public void performDrag(Node node, MouseEvent event) {
    if (node.getParent() == null) {
      return;
    }
    final double parentScaleX =
        node.getParent().localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY =
        node.getParent().localToSceneTransformProperty().getValue().getMyy();

    // Get the exact moved X and Y
    double offsetX = event.getSceneX() - mouseX;
    double offsetY = event.getSceneY() - mouseY;

    nodeX += offsetX;
    nodeY += offsetY;

    if (params != null) {
      if (nodeX < params.getMinXposProperty().get()) {
        nodeX = params.getMinXposProperty().get();
      } else if (nodeX > params.getMaxXposProperty().get()) {
        nodeX = params.getMaxXposProperty().get();
      }

      if (nodeY < params.getMinYposProperty().get()) {
        nodeY = params.getMinYposProperty().get();
      } else if (nodeY > params.getMaxYposProperty().get()) {
        nodeY = params.getMaxYposProperty().get();
      }
    }

    double scaledX;
    double scaledY;

    if (centerNode) {
      Point2D p2d = node.getParent().sceneToLocal(mouseX, mouseY);
      scaledX = p2d.getX();
      scaledY = p2d.getY();
    } else {
      scaledX = nodeX * 1 / (parentScaleX);
      scaledY = nodeY * 1 / (parentScaleY);
    }

    node.setLayoutX(scaledX);
    node.setLayoutY(scaledY);

    // again set current Mouse x AND y position
    mouseX = event.getSceneX();
    mouseY = event.getSceneY();

  }

  public void performDragBegin(Node node, MouseEvent event) {

    final double parentScaleX =
        node.getParent().localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY =
        node.getParent().localToSceneTransformProperty().getValue().getMyy();

    // record the current mouse X and Y position on Node
    mouseX = event.getSceneX();
    mouseY = event.getSceneY();

    if (centerNode) {
      Point2D p2d = node.getParent().sceneToLocal(mouseX, mouseY);
      nodeX = p2d.getX();
      nodeY = p2d.getY();
    } else {
      nodeX = node.getLayoutX() * parentScaleX;
      nodeY = node.getLayoutY() * parentScaleY;
    }

    node.toFront();
  }
}
