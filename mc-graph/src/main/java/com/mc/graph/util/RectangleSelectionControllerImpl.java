package com.mc.graph.util;

import javafx.event.EventHandler;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.shape.Rectangle;

import java.util.ArrayList;
import java.util.List;

class RectangleSelectionControllerImpl {

  private Rectangle rectangle;
  private double firstX;
  private double firstY;
  private double secondX;
  private double secondY;
  private EventHandler<MouseEvent> mouseDraggedEventHandler;
  private EventHandler<MouseEvent> mousePressedHandler;
  private EventHandler<MouseEvent> mouseReleasedHandler;

  private final List<SelectableNode> selectedNodes = new ArrayList<>();
  
  private final GraphSelectionModel clipboard;

  public RectangleSelectionControllerImpl(GraphSelectionModel clipboard) {
    this.clipboard = clipboard;
  }

  public void apply(Parent root, Rectangle rect, EventHandlerGroup<MouseEvent> draggedEvtHandler,
      EventHandlerGroup<MouseEvent> pressedEvtHandler,
      EventHandlerGroup<MouseEvent> releasedEvtHandler) {
    init(root, rect);
    draggedEvtHandler.addHandler(mouseDraggedEventHandler);
    pressedEvtHandler.addHandler(mousePressedHandler);
    releasedEvtHandler.addHandler(mouseReleasedHandler);
  }

  private void init(final Parent root, final Rectangle rect) {

    this.rectangle = rect;

    mouseDraggedEventHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (event.getButton() != MouseButton.PRIMARY) {
          return;
        }
        performDrag(root, event);
        event.consume();
      }
    };

    mousePressedHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (event.getButton() != MouseButton.PRIMARY) {
          return;
        }
        performDragBegin(root, event);
        event.consume();
      }
    };

    mouseReleasedHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (event.getButton() != MouseButton.PRIMARY) {
          return;
        }
        performDragEnd(root, event);
        event.consume();
      }
    };
  }

  public void performDrag(Parent root, MouseEvent event) {

    rectangle.setVisible(true);

    final double parentScaleX = root.localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY = root.localToSceneTransformProperty().getValue().getMyy();

    final double translateX = -root.localToSceneTransformProperty().getValue().getTx();
    final double translateY = -root.localToSceneTransformProperty().getValue().getTy();

    secondX = event.getSceneX();
    secondY = event.getSceneY();

    firstX = Math.max(firstX, 0);
    firstY = Math.max(firstY, 0);

    secondX = Math.max(secondX, 0);
    secondY = Math.max(secondY, 0);

    double xpos = Math.min(firstX, secondX);
    double ypos = Math.min(firstY, secondY);

    double width = Math.abs(secondX - firstX);
    double height = Math.abs(secondY - firstY);

    rectangle.setX(xpos / parentScaleX + translateX / parentScaleX);
    rectangle.setY(ypos / parentScaleY + translateY / parentScaleY);
    rectangle.setWidth(width / parentScaleX);
    rectangle.setHeight(height / parentScaleY);

    if (clipboard.getSelectionMode().get() == GraphSelectionModel.SelectionMode.NODE) {
      selectIntersectingNodes(root, !event.isControlDown());
    }

  }

  private void selectIntersectingNodes(Parent root, boolean deselect) {

    List<Node> selectableNodes =
        root.getChildrenUnmodifiable().filtered(n -> n.getUserData() instanceof SelectableNode);

    boolean rectBigEnough = rectangle.getWidth() > 1 || rectangle.getHeight() > 1;

    for (Node n : selectableNodes) {
      boolean selectN =
          rectangle.intersects(rectangle.parentToLocal(n.localToParent(n.getBoundsInLocal())));

      SelectableNode sn = (SelectableNode) n.getUserData();

      if (deselect || potentiallySelected(sn) || selectN && rectBigEnough) {
        clipboard.select(sn, selectN && rectBigEnough);
      }
    }

  }

  public void performDragBegin(Parent root, MouseEvent event) {

    selectedNodes.addAll(clipboard.getSelectedItems());

    if (rectangle.getParent() != null) {
      return;
    }

    // record the current mouse X and Y position on Node
    firstX = event.getSceneX();
    firstY = event.getSceneY();
    
    rectangle.setUserData(NodeUtil.NODE_IGNORE);
    NodeUtil.addToParent(root, rectangle);

    rectangle.setWidth(0);
    rectangle.setHeight(0);
    rectangle.toFront();

    rectangle.setVisible(false);
  }

  public void performDragEnd(Parent root, MouseEvent event) {
    if (rectangle.getParent() != null) {
      if (clipboard.getSelectionMode().get() == GraphSelectionModel.SelectionMode.NODE) {
        selectIntersectingNodes(root, !event.isControlDown());
      }
      if (clipboard instanceof GraphSelectionModelImpl) {
        Rectangle area = new Rectangle();
        area.setX(rectangle.getX());
        area.setY(rectangle.getY());
        area.setWidth(rectangle.getWidth());
        area.setHeight(rectangle.getHeight());
        ((GraphSelectionModelImpl)clipboard).fireChange(clipboard.getSelectionMode().get(), area);
      }
      NodeUtil.removeFromParent(rectangle);
    }
    selectedNodes.clear();
  }

  private boolean potentiallySelected(SelectableNode sn) {
    return !selectedNodes.contains(sn)
        && clipboard.getSelectedItems().contains(sn);
  }
}