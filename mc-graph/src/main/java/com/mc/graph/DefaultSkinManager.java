package com.mc.graph;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.SkinManager;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class DefaultSkinManager implements SkinManager {
  private Map<CellObject, CellSkin> cellSkins = new HashMap<>();
  private Map<LinkObject, LinkSkin> linkSkins = new HashMap<>();
  private Map<Connector, ConnectorSkin> connectorSkins = new HashMap<>();
  
  public DefaultSkinManager() {

  }
  
  @Override
  public CellSkin getCellSkin(CellObject cell) {
    if (cellSkins.containsKey(cell)) {
      return cellSkins.get(cell);
    } else {
      return null;
    }
  }

  @Override
  public void setCellSkin(CellObject cell, CellSkin skin) {
    cellSkins.put(cell, skin);
  }

  @Override
  public LinkSkin getLinkSkin(LinkObject link) {
    if (linkSkins.containsKey(link)) {
      return linkSkins.get(link);
    } else {
      return null;
    }
  }

  @Override
  public void setLinkSkin(LinkObject link, LinkSkin skin) {
    linkSkins.put(link, skin);
  }

  @Override
  public ConnectorSkin getConnectorSkin(Connector connector) {
    if (connectorSkins.containsKey(connector)) {
      return connectorSkins.get(connector);
    } else {
      return null;
    }
  }

  @Override
  public void setConnectorSkin(Connector connector, ConnectorSkin skin) {
    connectorSkins.put(connector, skin);
  }

  @Override
  public void removeCellSkin(CellObject cell) {
    if (cellSkins.containsKey(cell)) {
      CellSkin skin = cellSkins.get(cell);
      skin.destroy();
      cellSkins.remove(cell);
    }
    
    for (Connector connector : cell.getConnectors()) {
      if (connectorSkins.containsKey(connector)) {
        ConnectorSkin skin = connectorSkins.get(connector);
        skin.destroy();
        connectorSkins.remove(connector);
      }
    }
  }

  @Override
  public void removeLinkSkin(LinkObject link) {
    if (linkSkins.containsKey(link)) {
      LinkSkin skin = linkSkins.get(link);
      skin.destroy();
      linkSkins.remove(link);
    }
  }

  @Override
  public Collection<CellSkin> getAllCellSkin() {
    return cellSkins.values();
  }

  @Override
  public void destroy() {
    for (CellSkin cellSkin : cellSkins.values()) {
      for (ConnectorSkin skin : cellSkin.getConnectorSkins()) {
        skin.destroy();
      }
      cellSkin.destroy();
    }
    cellSkins.clear();
    for (LinkSkin linkSkin : linkSkins.values()) {
      linkSkin.destroy();
    }
    linkSkins.clear();
    for (ConnectorSkin connectorSkin : connectorSkins.values()) {
      connectorSkin.destroy();
    }
    connectorSkins.clear();
  }

}
