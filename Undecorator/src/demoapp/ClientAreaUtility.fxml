<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import java.net.*?>
<?import java.util.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.web.*?>

<VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" pickOnBounds="true" prefHeight="300.0" prefWidth="600.0" style="-fx-background-color:transparent;" xmlns:fx="http://javafx.com/fxml/1" xmlns="http://javafx.com/javafx/8">
  <children>
    <HBox prefHeight="32.0" prefWidth="640.0" />
    <AnchorPane id="draggableNode" maxHeight="-1.0" maxWidth="-1.0" pickOnBounds="false" prefHeight="-1.0" prefWidth="-1.0" VBox.vgrow="ALWAYS">
      <children>
        <SplitPane dividerPositions="0.40034364261168387" focusTraversable="true" prefHeight="227.0" prefWidth="584.0" AnchorPane.bottomAnchor="10.0" AnchorPane.leftAnchor="9.0" AnchorPane.rightAnchor="7.0" AnchorPane.topAnchor="14.0">
          <items>
            <AnchorPane prefHeight="233.0" prefWidth="217.0">
              <children>
                <Label layoutX="14.0" layoutY="14.0" text="Name:" />
                <TextField disable="false" layoutY="11.0" prefWidth="276.0" text="Utility Stage Style" visible="true" AnchorPane.leftAnchor="77.0" AnchorPane.rightAnchor="14.0" />
                <TableView prefHeight="229.0" prefWidth="340.0" AnchorPane.bottomAnchor="13.0" AnchorPane.leftAnchor="14.0" AnchorPane.rightAnchor="13.0" AnchorPane.topAnchor="43.0">
                  <columns>
                    <TableColumn prefWidth="75.0" text="Column X" />
                    <TableColumn maxWidth="5000.0" minWidth="10.0" prefWidth="94.0" text="Column X" />
                  </columns>
                </TableView>
              </children>
            </AnchorPane>
            <TabPane prefHeight="200.0" prefWidth="200.0" tabClosingPolicy="UNAVAILABLE">
              <tabs>
                <Tab text="Form">
                  <content>
                    <GridPane>
                      <children>
                        <Button disable="true" mnemonicParsing="false" text="Press me" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        <Label text="Field one:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label text="Field two :" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Label text="Field three:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Hyperlink text="http://docs.oracle.com/javafx/2/api/javafx/fxml/doc-files/introduction_to_fxml.html" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <TextArea prefWidth="200.0" text="should type something here..." wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                      </children>
                      <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="133.0" minWidth="10.0" prefWidth="86.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="223.0" minWidth="10.0" prefWidth="212.0" />
                      </columnConstraints>
                      <padding>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                      </padding>
                      <rowConstraints>
                        <RowConstraints maxHeight="84.0" minHeight="10.0" prefHeight="29.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="141.0" minHeight="10.0" prefHeight="141.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="85.0" minHeight="10.0" prefHeight="85.0" vgrow="SOMETIMES" />
                      </rowConstraints>
                    </GridPane>
                  </content>
                </Tab>
              </tabs>
            </TabPane>
          </items>
        </SplitPane>
      </children>
    </AnchorPane>
    <AnchorPane prefHeight="49.0" prefWidth="640.0">
      <children>
        <Button defaultButton="true" mnemonicParsing="false" onAction="#handleUtilityAction" text="OK" AnchorPane.bottomAnchor="14.0" AnchorPane.rightAnchor="14.0" AnchorPane.topAnchor="14.0" />
        <Button cancelButton="true" mnemonicParsing="false" onAction="#handleUtilityAction" text="Cancel" AnchorPane.bottomAnchor="14.0" AnchorPane.rightAnchor="64.0" AnchorPane.topAnchor="14.0" />
      </children>
    </AnchorPane>
  </children>
  <stylesheets>
    <URL value="@demoapp.css" />
  </stylesheets>
   <padding>
      <Insets bottom="5.0" />
   </padding>
</VBox>
