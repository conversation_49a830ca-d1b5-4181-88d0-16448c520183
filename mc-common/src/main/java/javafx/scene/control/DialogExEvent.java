/*
 * Copyright (c) 2014, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package javafx.scene.control;

import javafx.beans.NamedArg;
import javafx.event.Event;
import javafx.event.EventTarget;
import javafx.event.EventType;

public class DialogExEvent extends Event {

  private static final long serialVersionUID = 20140716L;

  /**
   * Common supertype for all dialog event types.
   */
  public static final EventType<DialogExEvent> ANY =
      new EventType<DialogExEvent>(Event.ANY, "DIALOGEX");

  /**
   * This event occurs on dialog just before it is shown.
   */
  public static final EventType<DialogExEvent> DIALOGEX_SHOWING =
      new EventType<DialogExEvent>(DialogExEvent.ANY, "DIALOGEX_SHOWING");

  /**
   * This event occurs on dialog just after it is shown.
   */
  public static final EventType<DialogExEvent> DIALOGEX_SHOWN =
      new EventType<DialogExEvent>(DialogExEvent.ANY, "DIALOGEX_SHOWN");

  /**
   * This event occurs on dialog just before it is hidden.
   */
  public static final EventType<DialogExEvent> DIALOGEX_HIDING =
      new EventType<DialogExEvent>(DialogExEvent.ANY, "DIALOGEX_HIDING");

  /**
   * This event occurs on dialog just after it is hidden.
   */
  public static final EventType<DialogExEvent> DIALOGEX_HIDDEN =
      new EventType<DialogExEvent>(DialogExEvent.ANY, "DIALOGEX_HIDDEN");

  /**
   * This event is delivered to a dialog when there is an external request to close that dialog. If
   * the event is not consumed by any installed dialog event handler, the default handler for this
   * event closes the corresponding dialog.
   */
  public static final EventType<DialogExEvent> DIALOGEX_CLOSE_REQUEST =
      new EventType<DialogExEvent>(DialogExEvent.ANY, "DIALOGEX_CLOSE_REQUEST");

  /**
   * Construct a new {@code Event} with the specified event source, target and type. If the source
   * or target is set to {@code null}, it is replaced by the {@code NULL_SOURCE_TARGET} value.
   *
   * @param source the event source which sent the event
   * @param eventType the event type
   */
  public DialogExEvent(final @NamedArg("source") DialogEx<?> source,
      final @NamedArg("eventType") EventType<? extends Event> eventType) {
    super(source, source, eventType);
  }

  /**
   * Returns a string representation of this {@code DialogExEvent} object.
   * 
   * @return a string representation of this {@code DialogExEvent} object.
   */
  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder("DialogExEvent [");

    sb.append("source = ").append(getSource());
    sb.append(", target = ").append(getTarget());
    sb.append(", eventType = ").append(getEventType());
    sb.append(", consumed = ").append(isConsumed());

    return sb.append("]").toString();
  }

  @Override
  public DialogExEvent copyFor(Object newSource, EventTarget newTarget) {
    return (DialogExEvent) super.copyFor(newSource, newTarget);
  }

  /**
   * Creates a copy of the given event with the given fields substituted.
   * 
   * @param newSource the new source of the copied event
   * @param newTarget the new target of the copied event
   * @param type the new eventType
   * @return the event copy with the fields substituted
   */
  public DialogExEvent copyFor(Object newSource, EventTarget newTarget,
      EventType<DialogExEvent> type) {
    DialogExEvent event = copyFor(newSource, newTarget);
    event.eventType = type;
    return event;
  }

  @SuppressWarnings("unchecked")
  @Override
  public EventType<DialogExEvent> getEventType() {
    return (EventType<DialogExEvent>) super.getEventType();
  }
}
