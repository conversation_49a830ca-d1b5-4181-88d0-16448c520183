package javafx.collections.transformation;

import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;

import java.util.List;

public class SimpleListChange<E> extends ListChangeListener.Change<E> {
  private final int from;
  private final int to;

  /**
   * Constructor. 
   */
  public SimpleListChange(ObservableList<E> list, int from, int to) {
    super(list);
    this.from = from;
    this.to = to;
  }

  @Override
  public boolean next() {
    return false;
  }

  @Override
  public void reset() {

  }

  @Override
  public int getFrom() {
    return from;
  }

  @Override
  public int getTo() {
    return to;
  }

  @Override
  public List<E> getRemoved() {
    return null;
  }

  @Override
  protected int[] getPermutation() {
    return new int[0];
  }

}
