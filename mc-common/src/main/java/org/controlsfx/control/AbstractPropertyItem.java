package org.controlsfx.control;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public abstract class AbstractPropertyItem implements PropertySheet.Item {
  @Setter
  protected String category = "";
  @Setter
  protected String description = "";
  @Setter
  @Getter
  protected String id = "";
  @Setter
  protected boolean editable = true;
  @Getter
  protected StringProperty nameProperty = new SimpleStringProperty();

  @Override
  public String getCategory() {
    return category;
  }
  
  public void setName(String name) {
    nameProperty.set(name);
  }
  
  /**
   * 删除，为了避免内存泄漏.删除后也可以继续使用.
   */
  public abstract void delete();

  @Override
  public String getName() {
    return nameProperty.get();
  }

  @Override
  public String getDescription() {
    return description;
  }

  @Override
  public boolean isEditable() {
    return editable;
  }
}
