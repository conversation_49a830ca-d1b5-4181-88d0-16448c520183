package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ObservableValue;
import javafx.collections.ObservableList;

import java.util.Optional;

public class ListPropertyItem extends AbstractPropertyItem {
  
  private final ObjectProperty<ObservableList<String>> value;
  private ObjectProperty<ObservableList<String>> observableValue;

  public ListPropertyItem(ObservableList<String> value) {
    this.value = new SimpleObjectProperty<ObservableList<String>>(value);
  }

  @Override
  public Class<?> getType() {
    return ObservableList.class;
  }

  @Override
  public Object getValue() {
    if (value == null) {
      return null;
    }
    return value.get();
  }

  @SuppressWarnings("unchecked")
  @Override
  public void setValue(Object value) {
    if (this.value != null && value instanceof ObservableList<?>) {
      this.value.set((ObservableList<String>) value);
    }
    
  }

  @Override
  public Optional<ObservableValue<? extends Object>> getObservableValue() {
    if (observableValue == null) {
      observableValue = new SimpleObjectProperty<ObservableList<String>>();
      observableValue.bind(value);
    }
    return Optional.of(observableValue);
  }
  

  @Override
  public void delete() {
    if (observableValue != null) {
      observableValue.unbind();
      observableValue = null;
    }
  }

}
