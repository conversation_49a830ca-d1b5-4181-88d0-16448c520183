package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ObservableValue;

import java.util.Optional;

public class ObjectPropertyItem<T> extends AbstractPropertyItem {

  protected final Class<T> clazz;
  protected final ObjectProperty<T> value;
  protected final boolean needToUnbindValue;
  protected ObjectProperty<T> observableValue = null;

  public ObjectPropertyItem(ObjectProperty<T> value, Class<T> clazz) {
    this(value, clazz, false);
  }
  
  /**
   * 数值属性.
   * @param value 属性值
   * @param clazz 属性值类型
   * @param needToUnbindValue 当要销毁时是否需要unbind属性值
   */
  public ObjectPropertyItem(ObjectProperty<T> value, Class<T> clazz, 
      boolean needToUnbindValue) {
    this.value = value;
    this.clazz = clazz;
    this.needToUnbindValue = needToUnbindValue;
  }

  @Override
  public Class<?> getType() {
    return clazz;
  }

  @Override
  public Object getValue() {
    if (value == null) {
      return null;
    }
    return value.get();
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }

  @Override
  public Optional<ObservableValue<?>> getObservableValue() {
    if (observableValue == null) {
      observableValue = createObservableValue();
      observableValue.bind(value);
    }
    return Optional.of(observableValue);
  }

  protected ObjectProperty<T> createObservableValue() {
    return new SimpleObjectProperty<>();
  }

  @Override
  public void delete() {
    if (observableValue != null) {
      observableValue.unbind();
      observableValue = null;
    }
    if (value != null && needToUnbindValue) {
      value.unbind();
    }
  }
}
