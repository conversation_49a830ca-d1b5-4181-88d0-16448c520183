/*
 * Copyright (c) 2013, dooApp <<EMAIL>> All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted
 * provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice, this list of conditions
 * and the following disclaimer. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the documentation and/or other
 * materials provided with the distribution. Neither the name of do<PERSON><PERSON><PERSON> nor the names of its
 * contributors may be used to endorse or promote products derived from this software without
 * specific prior written permission. THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
 * CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON><PERSON><PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDI<PERSON>, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */

package com.dooapp.fxform;

import com.dooapp.fxform.controller.PropertyEditorController;
import com.dooapp.fxform.model.DefaultElementProvider;
import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.model.ElementProvider;
import com.dooapp.fxform.model.impl.BufferedElement;
import com.dooapp.fxform.model.impl.BufferedPropertyElement;
import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.FXFormSkin;
import com.dooapp.fxform.view.factory.DefaultFactoryProvider;
import com.dooapp.fxform.view.factory.DefaultLabelFactoryProvider;
import com.dooapp.fxform.view.factory.DefaultTooltipFactoryProvider;
import com.dooapp.fxform.view.factory.FactoryProvider;
import com.dooapp.fxform.view.skin.DefaultSkin;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.ListChangeListener;
import javafx.scene.Scene;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FXForm<T> extends AbstractFXForm {

  private final Logger logger = Logger.getLogger(FXForm.class.getName());
  private final ObjectProperty<T> source = new SimpleObjectProperty<T>();

  private final ObjectProperty<ElementProvider> elementProvider =
      new SimpleObjectProperty<ElementProvider>();
  private BooleanProperty isChangeProperty = new SimpleBooleanProperty(false);
  private BooleanBinding isChangeBinding;

  public FXForm() {
    this(new DefaultFactoryProvider());
  }

  public FXForm(T source) {
    this(source, new DefaultFactoryProvider());
  }

  public FXForm(FactoryProvider editorFactoryProvider) {
    this(null, new DefaultLabelFactoryProvider(), new DefaultTooltipFactoryProvider(),
        editorFactoryProvider);
  }

  public FXForm(T source, FactoryProvider editorFactoryProvider) {
    this(source, new DefaultLabelFactoryProvider(), new DefaultTooltipFactoryProvider(),
        editorFactoryProvider);
  }

  public FXForm(FactoryProvider labelFactoryProvider, FactoryProvider tooltipFactoryProvider,
      FactoryProvider editorFactoryProvider) {
    this(null, labelFactoryProvider, tooltipFactoryProvider, editorFactoryProvider);
  }

  /**
   * constructor.
   */
  public FXForm(final T source, FactoryProvider labelFactoryProvider,
      FactoryProvider tooltipFactoryProvider, FactoryProvider editorFactoryProvider) {
    super();
    initBundle();
    elementsProperty().addListener(new ListChangeListener<Element>() {

      @Override
      public void onChanged(Change<? extends Element> change) {
        if (change.next()) {
          isChangeBinding =
              Bindings.or(new SimpleBooleanProperty(false), new SimpleBooleanProperty(false));
          for (Element element : getElements()) {
            if (element instanceof BufferedPropertyElement) {
              isChangeBinding = isChangeBinding.or(((BufferedPropertyElement) element).isChange());
            }
          }
          isChangeProperty.unbind();
          isChangeProperty.bind(isChangeBinding);
        }
      }
    });
    setLabelFactoryProvider(labelFactoryProvider);
    setTooltipFactoryProvider(tooltipFactoryProvider);
    setEditorFactoryProvider(editorFactoryProvider);
    setElementProvider(new DefaultElementProvider());
    this.source.addListener(new ChangeListener<T>() {
      public void changed(ObservableValue<? extends T> observableValue, T oldSource, T newSource) {
        if (newSource == null) {
          elementsProperty().unbind();
          elementsProperty().clear();
        } else if (oldSource == null || newSource.getClass() != oldSource.getClass()) {
          elementsProperty().unbind();
          elementsProperty().bind(getElementProvider().getElements(sourceProperty()));
        }
      }
    });
    this.setSkin(new DefaultSkin(this));
    getClassLevelValidator().beanProperty().bind(sourceProperty());
    setSource(source);
  }

  public T getSource() {
    return source.get();
  }

  public void setSource(T source) {
    this.source.set(source);
  }

  public ObjectProperty<T> sourceProperty() {
    return source;
  }

  /**
   * Auto loading of default resource bundle and css file.
   */
  private void initBundle() {
    final StackTraceElement element = getCallingClass();
    String bundle = element.getClassName();
    if (getResourceBundle() == null) {
      try {
        setResourceBundle(ResourceBundle.getBundle(bundle));
      } catch (MissingResourceException ex) {
        logger.log(Level.FINE, "");
      }
    }
    sceneProperty().addListener(new ChangeListener<Scene>() {
      public void changed(ObservableValue<? extends Scene> observableValue, Scene scene,
          Scene scene1) {
        String path = element.getClassName().replaceAll("\\.", "/") + ".css";
        URL css = FXForm.class.getClassLoader().getResource(path);
        if (css != null && observableValue.getValue() != null) {
          getScene().getStylesheets().add(css.toExternalForm());
        }
      }
    });
  }

  /**
   * Retrieve the calling class in which the form is being created.
   *
   * @return the StackTraceElement representing the calling class
   */
  private StackTraceElement getCallingClass() {
    StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
    int index = 1;
    while (stackTrace[index].getClassName().equals(getClass().getName())) {
      index++;
    }
    return stackTrace[index];
  }

  public ElementProvider getElementProvider() {
    return elementProvider.get();
  }

  public ObjectProperty<ElementProvider> elementProviderProperty() {
    return elementProvider;
  }

  public void setElementProvider(ElementProvider elementProvider) {
    this.elementProvider.set(elementProvider);
  }

  /**
   * Returns true if all from values are valid.
   *
   * @return true if all from values are valid
   */
  public boolean isValid() {
    return getConstraintViolations().isEmpty();
  }

  /**
   * Commits the form content to the Java bean if the form is buffered.
   *
   * @return true if the form values could be committed or false if one of the form values is
   *         invalid
   */
  public boolean commit() {
    if (isValid()) {
      for (Element element : getElements()) {
        if (element instanceof BufferedPropertyElement) {
          ((BufferedPropertyElement) element).commit();
        }
      }

      return true;
    } else {
      return false;
    }
  }

  /**
   * Reloads the form content from the Java bean if the form is buffered.
   */
  public void reload() {
    for (Element element : getElements()) {
      if (element instanceof BufferedElement) {
        ((BufferedElement) element).reload();
        ((PropertyEditorController) getController(element).getEditorController()).updateView();
      }
    }
  }
  
  public BooleanProperty isChange() {
    return isChangeProperty;
  }
  
  /**
   * 设备数据更新时刷新.
   */
  public void refresh() {
    for (Element element : getElements()) {
      if (element instanceof BufferedPropertyElement) {
        ((BufferedPropertyElement) element).refresh();
        ((PropertyEditorController) getController(element).getEditorController()).updateView();
      }
    }
  }

  /**
   * 元素是否有变化.
   * @param name 元素名称.
   * @return 如果有变化，返回true.
   */
  public boolean isElementChange(String name) {
    for (Element element : getElements()) {
      if (element.getName().equals(name)) {
        if (element instanceof BufferedPropertyElement) {
          return ((BufferedPropertyElement)element).isChange().get();
        } else {
          return false;
        }
      }
    }
    return false;
  }

  /**
   * 根据名称找元素.
   * @param name 名称.
   * @return 如果找到，返回该element，找不到就返回null
   */
  public Element findElementByName(String name) {
    for (Element item : getElements()) {
      if (item.getName().equals(name)) {
        return item;
      }
    }
    return null;
  }

  /**
   * 根据名称找编辑控件.
   * @param name 名称.
   * @return 编辑控件，如果找不到，返回null
   */
  public FXFormNode getEditor(String name) {
    return ((FXFormSkin)getSkin()).getEditor(findElementByName(name));
  }

  /**
   * 根据元素找编辑控件.
   * @param item 元素.
   * @return 编辑控件，如果找不到，返回null
   */
  public FXFormNode getEditor(Element item) {
    return ((FXFormSkin)getSkin()).getEditor(item);
  }

  /**
   * 根据名称找标签控件.
   * @param name 名称.
   * @return 标签控件，如果找不到，返回null
   */
  public FXFormNode getLabel(String name) {
    return ((FXFormSkin)getSkin()).getLabel(findElementByName(name));
  }


  /**
   * 根据元素找标签控件.
   * @param item 元素.
   * @return 标签控件，如果找不到，返回null
   */
  public FXFormNode getLabel(Element item) {
    return ((FXFormSkin)getSkin()).getLabel(item);
  }

  /**
   * 根据名称找提示控件.
   * @param name 名称.
   * @return 提示控件，如果找不到，返回null
   */
  public FXFormNode getTooltip(String name) {
    return ((FXFormSkin)getSkin()).getTooltip(findElementByName(name));
  }

  /**
   * 根据元素找提示控件.
   * @param item 元素.
   * @return 提示控件，如果找不到，返回null
   */
  public FXFormNode getTooltip(Element item) {
    return ((FXFormSkin)getSkin()).getTooltip(item);
  }

  /**
   * 根据名称找元素.
   *
   * @return 如果找到，返回该element，找不到就返回null
   */
  public List<Element> findBooleanElement() {
    List<Element> elements = new ArrayList<>();
    for (Element item : getElements()) {
      if (item.getValue() instanceof Boolean) {
        elements.add(item);
      }
    }
    return elements;
  }
}
