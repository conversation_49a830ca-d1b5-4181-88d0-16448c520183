package com.dooapp.fxform.builder;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.ReadOnlyFXForm;
import com.dooapp.fxform.filter.CategorizeFilter;
import com.dooapp.fxform.filter.ReorderFilter;
import com.dooapp.fxform.filter.field.ExcludeFieldFilter;
import com.dooapp.fxform.filter.field.FieldFilter;
import com.dooapp.fxform.filter.field.PrivateFinalStaticFilter;
import com.dooapp.fxform.model.BufferedElementFactory;
import com.dooapp.fxform.model.DefaultElementFactory;
import com.dooapp.fxform.model.DefaultElementProvider;
import com.dooapp.fxform.model.ElementFactory;
import com.dooapp.fxform.reflection.FieldProvider;
import com.dooapp.fxform.reflection.impl.ReflectionFieldProvider;
import com.dooapp.fxform.view.skin.FxmlSkin2;
import com.dooapp.fxform.view.skin.InlineSkin;
import javafx.fxml.Initializable;
import javafx.scene.control.Skin;

import java.net.URL;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.ResourceBundle;

public class FXFormBuilder2<B extends FXFormBuilder2<?>> {
  private Skin<?> skin;

  private Object source;

  private String[] includeFilters;

  private String[] reorderFilter;

  private String[] categorizeFilter;

  private String[] excludeFilters;

  private ResourceBundle resourceBundle;
  
  private ResourceBundle extraResourceBundle;

  // private FieldFilter[] fieldFilters;

  private URL fxmlUrl;

  private boolean readOnly;

  private boolean bufferUserInput;
  private boolean bufferBeanChanges;

  private Initializable controller;

  /**
   * build.
   */
  public FXForm<Object> build() {
    FXForm<Object> res;
    DefaultElementProvider elementProvider;

    FieldProvider fieldProvider = new ReflectionFieldProvider();
    ElementFactory elementFactory = new DefaultElementFactory();

    if (bufferUserInput || bufferBeanChanges) {
      elementFactory =
          new BufferedElementFactory(elementFactory, bufferUserInput, bufferBeanChanges);
    }

    if (includeFilters != null) {
      elementProvider = new DefaultElementProvider(elementFactory, fieldProvider, includeFilters);
    } else {
      elementProvider = new DefaultElementProvider(elementFactory, fieldProvider);
    }
    // if (fieldFilters != null) {
    // for (FieldFilter fieldFilter : fieldFilters) {
    // elementProvider.getFilters().add(fieldFilter);
    // }
    // } else {
    // elementProvider.getFilters().addAll(handleDefaultFieldFilters());
    // }
    elementProvider.getFilters().addAll(handleDefaultFieldFilters());
    if (readOnly) {
      res = new ReadOnlyFXForm<>();
    } else {
      res = new FXForm<>();
    }
    res.setElementProvider(elementProvider);
    if (skin == null) {
      handleDefaultSkin(res);
    } else {
      res.setSkin(skin);
    }
    if (resourceBundle != null) {
      res.setResourceBundle(resourceBundle);
    }
    if (excludeFilters != null) {
      elementProvider.getFilters().add(new ExcludeFieldFilter(excludeFilters));
    }
    if (reorderFilter != null) {
      res.addFilters(new ReorderFilter(reorderFilter));
    }
    if (categorizeFilter != null) {
      res.addFilters(new CategorizeFilter(categorizeFilter));
    }
    if (source != null) {
      res.setSource(source);
    }
    if (fxmlUrl != null && extraResourceBundle != null) {
      res.setSkin(new FxmlSkin2(res, fxmlUrl, controller, extraResourceBundle));
    } else if (fxmlUrl != null) {
      res.setSkin(new FxmlSkin2(res, fxmlUrl, controller));
    }
    return res;
  }

  protected Collection<? extends FieldFilter> handleDefaultFieldFilters() {
    List<FieldFilter> filters = new LinkedList<>();
    filters.add(new PrivateFinalStaticFilter());
    return filters;
  }

  protected void handleDefaultSkin(FXForm<?> fxForm) {
    fxForm.setSkin(new InlineSkin(fxForm));
  }

  public B fxml(URL url) {
    this.fxmlUrl = url;
    return (B)this;
  }

  public B controller(Initializable controller) {
    this.controller = controller;
    return (B)this;
  }

  public B skin(Skin skin) {
    this.skin = skin;
    return (B)this;
  }

  public B source(Object source) {
    this.source = source;
    return (B)this;
  }

  public B include(String... includeFilters) {
    this.includeFilters = includeFilters;
    return (B)this;
  }

  public B exclude(String... excludeFilters) {
    this.excludeFilters = excludeFilters;
    return (B)this;
  }

  public B reorder(String... reorderFilter) {
    this.reorderFilter = reorderFilter;
    return (B)this;
  }

  /**
   * .
   */
  public B includeAndReorder(String... strings) {
    this.includeFilters = strings;
    this.reorderFilter = strings;
    return (B)this;
  }

  public B resourceBundle(ResourceBundle resourceBundle) {
    this.resourceBundle = resourceBundle;
    return (B)this;
  }

  public B extraResourceBundle(ResourceBundle resourceBundle) {
    this.extraResourceBundle = resourceBundle;
    return (B)this;
  }

  public B readOnly(boolean readOnly) {
    this.readOnly = readOnly;
    return (B)this;
  }

  public B categorize(String... strings) {
    this.categorizeFilter = strings;
    return (B)this;
  }

  /**
   * .
   */
  public B categorizeAndInclude(String... strings) {
    this.categorizeFilter = strings;
    List<String> includes = new LinkedList<>();
    for (String s : strings) {
      if (!s.startsWith("-")) {
        includes.add(s);
      }
    }
    this.includeFilters = includes.toArray(new String[0]);
    return (B)this;
  }

  /**
   * Specifies whether the user input and/or bean value changes shall be buffered until commit() or
   * reload() are called. Without buffering the user input is immediately written to the bean and
   * bean value changes immediately appear in the form.
   *
   * @param userInput set to true if you want the user input to be buffered instead of writing it
   *        immediately to the bean
   * @param beanChanges set to true if you want to avoid that form fields are automatically updated
   *        on bean property changes
   * @return this builder
   */
  public B buffered(boolean userInput, boolean beanChanges) {
    this.bufferUserInput = userInput;
    this.bufferBeanChanges = beanChanges;
    return (B)this;
  }

}
