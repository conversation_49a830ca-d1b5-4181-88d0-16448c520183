package com.dooapp.fxform.view.property;

import com.dooapp.fxform.view.control.ConstraintLabel;
import javafx.beans.property.Property;
import javafx.scene.Node;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ChoiceBox;
import javafx.scene.control.ColorPicker;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.Slider;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.ToggleButton;
import tornadofx.control.DateTimePicker;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

public class DefaultPropertyProvider implements PropertyProvider {

  private static final Map<Class<? extends Node>, PropertyProvider> GLOBAL_MAP =
      new HashMap<Class<? extends Node>, PropertyProvider>();

  protected final Map<Class<? extends Node>, PropertyProvider> map =
      new HashMap<Class<? extends Node>, PropertyProvider>();

  @Override
  public Property getProperty(Node node) {
    if (node == null) {
      return null;
    }
    for (Entry<Class<? extends Node>, PropertyProvider> entry : GLOBAL_MAP.entrySet()) {
      if (entry.getKey().isAssignableFrom(node.getClass())) {
        return GLOBAL_MAP.get(entry.getKey()).getProperty(node);
      }
    }
    for (Entry<Class<? extends Node>, PropertyProvider> entry : map.entrySet()) {
      if (entry.getKey().isAssignableFrom(node.getClass())) {
        return map.get(entry.getKey()).getProperty(node);
      }
    }
    return null;
  }

  /**
   * DefaultPropertyProvider.
   */
  public DefaultPropertyProvider() {
    map.put(Label.class, new LabelPropertyProvider());
    map.put(ConstraintLabel.class, new ConstraintLabelPropertyProvider());
    map.put(TextField.class, new TextFieldPropertyProvider());
    map.put(TextArea.class, new TextAreaPropertyProvider());
    map.put(ChoiceBox.class, new ChoiceBoxPropertyProvider());
    map.put(ComboBox.class, new ComboBoxPropertyProvider());
    map.put(CheckBox.class, new CheckBoxPropertyProvider());
    map.put(ToggleButton.class, new ToggleButtonPropertyProvider());
    map.put(Slider.class, new SliderPropertyProvider());
    map.put(TableView.class, new TableViewPropertyProvider());
    map.put(ColorPicker.class, new ColorPickerPropertyProvider());
    map.put(DateTimePicker.class, new DateTimePickerPropertyProvider());
  }

  /**
   * Register a global property provider.
   *
   * @param clazz the Class of Node to register a property provider for
   * @param globalProvider the provider to register
   */
  public static void addGlobalProvider(Class<? extends Node> clazz,
      PropertyProvider globalProvider) {
    GLOBAL_MAP.put(clazz, globalProvider);
  }

  private static class LabelPropertyProvider implements PropertyProvider<Label> {

    @Override
    public Property getProperty(Label node) {
      return node.textProperty();
    }
  }

  private static class ConstraintLabelPropertyProvider
      implements PropertyProvider<ConstraintLabel> {

    @Override
    public Property getProperty(ConstraintLabel node) {
      return node.constraintProperty();
    }
  }

  private static class TextFieldPropertyProvider implements PropertyProvider<TextField> {

    @Override
    public Property getProperty(TextField node) {
      return node.textProperty();
    }
  }

  private static class TextAreaPropertyProvider implements PropertyProvider<TextArea> {

    @Override
    public Property getProperty(TextArea node) {
      return node.textProperty();
    }
  }

  private static class ChoiceBoxPropertyProvider implements PropertyProvider<ChoiceBox> {

    @Override
    public Property getProperty(ChoiceBox node) {
      return new ChoiceBoxDefaultProperty(node);
    }
  }

  private static class ComboBoxPropertyProvider implements PropertyProvider<ComboBox> {

    @Override
    public Property getProperty(ComboBox node) {
      return new ComboBoxDefaultProperty(node);
    }
  }

  private static class CheckBoxPropertyProvider implements PropertyProvider<CheckBox> {

    @Override
    public Property getProperty(CheckBox node) {
      return node.selectedProperty();
    }
  }

  private static class ToggleButtonPropertyProvider implements PropertyProvider<ToggleButton> {

    @Override
    public Property getProperty(ToggleButton node) {
      return node.selectedProperty();
    }
  }

  private static class SliderPropertyProvider implements PropertyProvider<Slider> {

    @Override
    public Property getProperty(Slider node) {
      return node.valueProperty();
    }
  }

  private static class TableViewPropertyProvider implements PropertyProvider<TableView> {

    @Override
    public Property getProperty(TableView node) {
      return new TableViewProperty(node);
    }
  }

  private static class ColorPickerPropertyProvider implements PropertyProvider<ColorPicker> {

    @Override
    public Property getProperty(ColorPicker node) {
      return node.valueProperty();
    }
  }
  
  private static class DateTimePickerPropertyProvider implements PropertyProvider<DateTimePicker> {

    @Override
    public Property getProperty(DateTimePicker node) {
      return node.dateTimeValueProperty();
    }
  }
}
