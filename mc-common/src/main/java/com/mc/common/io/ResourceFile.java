package com.mc.common.io;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

public class ResourceFile {
  private final File file;
  private final String resource;
  private final boolean isFile;
  
  
  /**
   * 创建文件.
   * @param file 文件
   * @return resource file
   */
  public static ResourceFile fromFile(File file) {
    if (file == null) {
      return null;
    } else {
      return new ResourceFile(file);
    }
  }
  
  /**
   * 创建资源.
   * @param resource 资源的路径.
   * @return resource file
   */
  public static ResourceFile fromResource(String resource) {
    if (resource == null) {
      return null;
    } else {
      return new ResourceFile(resource);
    }
  }
  
  private ResourceFile(File file) {
    this.file = file;
    this.resource = null;
    isFile = true;
  }
  
  private ResourceFile(String resource) {
    this.file = null;
    this.resource = resource;
    isFile = false;
  }
  
  /**
   * 文件是否存在.
   * @return 如果存在返回true
   */
  public boolean exists() {
    if (isFile) {
      return file.exists();
    } else {
      return true;
    }
  }
  
  /**
   * 获取绝对路径.
   * @return 绝对路径
   */
  public String getAbsolutePath() {
    if (isFile) {
      return file.getAbsolutePath();
    } else {
      return resource;
    }
  }
  
  /**
   * 获取文件名.
   * @return 文件名
   */
  public String getName() {
    if (isFile) {
      return file.getName();
    } else {
      String[] temp = resource.split("/");
      return temp[temp.length - 1];
    }
  }
  
  /**
   * 创建一个input stream来读取文件.
   * @return inputstream
   * @throws FileNotFoundException 文件找不到
   */
  public InputStream createInputStream() throws FileNotFoundException {
    if (isFile) {
      return new FileInputStream(file);
    } else {
      return Thread.currentThread().getContextClassLoader().getResourceAsStream(resource);
    }
  }
}
