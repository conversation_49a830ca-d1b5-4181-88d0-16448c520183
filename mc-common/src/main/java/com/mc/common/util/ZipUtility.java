package com.mc.common.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtility {
  private static final Logger LOG = Logger.getLogger(ZipUtility.class.getName());

  public static void zipFile(ZipOutputStream zos, File fileToBeZipped) {
    zipFile(zos, fileToBeZipped, "");
  }

  /**
   * 添加文件到压缩包.
   *
   * @param zos 压缩包.
   * @param fileToBeZipped 添加的文件，添加后此文件会被删除
   */
  public static void zipFile(ZipOutputStream zos, File fileToBeZipped, String path) {
    File file = fileToBeZipped;
    try {
      if (file.isDirectory()) {
        Files.walkFileTree(fileToBeZipped.toPath(), new SimpleFileVisitor<Path>() {
          public FileVisitResult visitFile(Path file, BasicFileAttributes attrs)
                  throws IOException {
            zos.putNextEntry(
                    new ZipEntry(
                            Paths.get(path)
                                    .resolve(fileToBeZipped.toPath().getFileName())
                                    .resolve(fileToBeZipped.toPath().relativize(file).toString())
                                    .toString()));
            Files.copy(file, zos);
            zos.closeEntry();
            return FileVisitResult.CONTINUE;
          }
        });
      } else {
        zos.putNextEntry(new ZipEntry(path + file.getName()));
        Files.copy(fileToBeZipped.toPath(), zos);
        zos.closeEntry();
      }
    } catch (IOException exception) {
      LOG.log(Level.WARNING, "Fail to zip!", exception);
    } finally {
      if (!file.delete()) {
        LOG.log(Level.WARNING, "Fail to delete file {}!", file.getName());
      }
    }
  }
}
