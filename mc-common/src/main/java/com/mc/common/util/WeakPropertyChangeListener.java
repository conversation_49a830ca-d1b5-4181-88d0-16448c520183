package com.mc.common.util;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;

public class WeakPropertyChangeListener implements PropertyChangeListener {
  private final WeakReference<PropertyChangeListener> weakRef;
  
  public WeakPropertyChangeListener(PropertyChangeListener listener) {
    weakRef = new WeakReference<>(listener);
  }
  
  @Override
  public void propertyChange(PropertyChangeEvent evt) {
    PropertyChangeListener listener = weakRef.get();
    if (listener != null) {
      listener.propertyChange(evt);
    }
  }

  public void clear() {
    weakRef.clear();
  }
}
