package com.mc.common.control;

import javafx.scene.Node;
import javafx.scene.control.Tooltip;
import javafx.util.Duration;

public class TextTooltipHelper {
  private static TextTooltipBehavior BEHAVIOR =
      new TextTooltipBehavior(new Duration(1000), new Duration(5000), new Duration(200), false);

  public static void install(Node node) {
    BEHAVIOR.install(node);
  }

  /**
   * Removes the association of the given {@link Tooltip} on the specified {@link Node}. Hence
   * hovering on the node will no longer result in showing of the tooltip.
   * 
   * @see Tooltip
   */
  public static void uninstall(Node node) {
    BEHAVIOR.uninstall(node);
  }
}
