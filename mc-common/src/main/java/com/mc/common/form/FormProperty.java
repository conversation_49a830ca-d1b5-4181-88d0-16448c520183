package com.mc.common.form;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.Property;

public interface FormProperty<T> extends Property<T> {
  /**
   * 值是否已被外部修改.
   * @return 如果已修改，返回true.
   */
  boolean isChanged();
  
  /**
   * 把当前的值设置到绑定的值.
   */
  void setToBinding();
  
  /**
   * 把值恢复到绑定的值.
   */
  void reset();
  
  /**
   * 是否已改变的属性值
   * @return.
   */
  BooleanProperty changedProperty();
}
