package com.mc.common.validation.constraints;

import com.mc.common.validation.constraints.impl.EvenImpl;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {EvenImpl.class})
public @interface Even {
  /**
   * 获取校验错误的文本.
   * @return 校验文本
   */
  String message() default "{com.mc.common.validation.constraints.even.message}";

  /**
   * Groups.
   * @return groups
   */
  Class<?>[] groups() default {};

  /**
   * Payload.
   * @return payloads
   */
  Class<? extends Payload>[] payload() default {};
}
