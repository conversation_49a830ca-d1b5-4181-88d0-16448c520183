<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.mc</groupId>
	<artifactId>mc-common</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>mc-common</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.checkstyle.version>3.0.0</maven.checkstyle.version>
		<maven.jxr.version>3.1.1</maven.jxr.version>
		<maven.pmd.version>3.9.0</maven.pmd.version>
		<maven.findbugs.version>3.0.5</maven.findbugs.version>
		<controlsfx.version>8.40.18</controlsfx.version>
		<maven.compiler.version>3.9.0</maven.compiler.version>
		<lombok.version>1.18.26</lombok.version>
	</properties>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jxr-plugin</artifactId>
				<version>${maven.jxr.version}</version>
			</plugin>
		</plugins>
	</reporting>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven.compiler.version}</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>utf-8</encoding>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>${maven.checkstyle.version}</version>
				<configuration>
					<configLocation>checkstyle-checker.xml</configLocation>
					<encoding>UTF-8</encoding>
					<failsOnError>true</failsOnError>
					<failOnViolation>true</failOnViolation>
					<violationSeverity>info</violationSeverity>
					<suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
				</configuration>
				<executions>
					<execution>
						<id>checkstyle</id>
						<phase>validate</phase>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<failOnViolation>true</failOnViolation>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>${maven.pmd.version}</version>
				<configuration>
					<excludes>
                        <exclude>com/mc/common/control/TextWrapper.java</exclude>
                        <exclude>com/mc/common/control/TextTooltipBehavior.java</exclude>
                        <exclude>com/sun/javafx/scene/control/skin/SkinUtils.java</exclude>
                        <exclude>javafx/collections/transformation/FilteredListEx.java</exclude>
                        <exclude>com/sun/javafx/scene/control/skin/ColorPaletteEx.java</exclude>
                        <exclude>com/sun/javafx/scene/control/skin/ColorPickerBehaviorEx.java</exclude>
                        <exclude>com/sun/javafx/scene/control/skin/ColorPickerSkinEx.java</exclude>
                    </excludes>
          <analysisCache>true</analysisCache>
					<linkXRef>true</linkXRef>
				</configuration>
				<executions>
					<execution>
						<id>pmd</id>
						<phase>compile</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
				<version>${maven.findbugs.version}</version>
				<configuration>
					<effort>Max</effort>
					<threshold>Low</threshold>
					<xmlOutput>true</xmlOutput>
					<excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
				</configuration>
				<executions>
					<execution>
                        <phase>compile</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.controlsfx</groupId>
			<artifactId>controlsfx</artifactId>
			<version>${controlsfx.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>javax.validation</groupId>-->
<!--			<artifactId>validation-api</artifactId>-->
<!--			<version>2.0.1.Final</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<version>3.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.dooapp.fxform2</groupId>
			<artifactId>core</artifactId>
			<version>8.2.11</version> <!-- Note: For JavaFX 2.2, use 2.2.6 -->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>javax.validation</groupId>-->
<!--					<artifactId>validation-api</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>org.hibernate</groupId>-->
<!--					<artifactId>hibernate-validator</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
		</dependency>
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>2.0.1.Final</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.hibernate.validator</groupId>-->
<!--			<artifactId>hibernate-validator</artifactId>-->
<!--			<version>7.0.5.Final</version>-->
<!--		</dependency>-->
				<dependency>
			<groupId>org.jooq</groupId>
			<artifactId>joor-java-8</artifactId>
			<version>0.9.14</version>
		</dependency>
	</dependencies>
</project>
